import { Platform, StyleSheet, Text, View } from 'react-native';
import React, { useContext } from 'react';

// components
import RadioButton from '../../screens/events/RadioButton';

// utils, constants
import { Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { ALL, CLUB_PERCENTAGE } from '../../utils/constants/strings';
import { FormContext } from '../FormContext';

const ClubPercentage = () => {
    const { form, updateForm } = useContext(FormContext);
    
    return (
        <View style={styles.card}>
            <Text style={[styles.titleStyle, { marginBottom: Spacing.SCALE_12 }]}>Club percentage acceptance</Text>
            <RadioButton
                selected={form.clubPercentage === ALL ? true : false}
                placeHolder={ALL}
                onPress={() => updateForm('clubPercentage', ALL)}
                textStyle={styles.radioTextStyle}
                containerStyle={{ marginVertical: Spacing.SCALE_8 }}
            />
            <RadioButton
                selected={form.clubPercentage === CLUB_PERCENTAGE ? true : false}
                placeHolder={CLUB_PERCENTAGE}
                onPress={() => updateForm('clubPercentage', CLUB_PERCENTAGE)}
                textStyle={styles.radioTextStyle}
                containerStyle={{ marginVertical: Spacing.SCALE_8 }}
            />
        </View>
    );
};

export default ClubPercentage;

const styles = StyleSheet.create({
    card: {
        padding: Spacing.SCALE_12,
        backgroundColor: colors.white,
        borderRadius: Spacing.SCALE_10,
        borderWidth: 1,
        borderColor: colors.lightgray,
        marginVertical: Spacing.SCALE_2,
    },
    countStyle: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: colors.fadeBlack,
        fontWeight: '400',
    },
    btnContainer: {
        backgroundColor: colors.whiteRGB,
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_12,
        borderTopWidth: 1,
        borderTopColor: colors.lightgray,
        ...Platform.select({
            ios: {
                shadowColor: colors.shadowColor,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
            },
            android: {
                elevation: 10,
                shadowOffset: {
                    width: 0,
                    height: 5,
                },
                shadowColor: colors.shadowColor,
                shadowRadius: 4,
                shadowOpacity: 0.5,
            },
        }),
    },
    radioTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        color: colors.lightBlack,
    },
    titleStyle: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
    },
    lableTextStyle: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        color: colors.lightBlack,
    },
});
