import { Platform, StyleSheet, Text, View } from 'react-native';
import React, { useContext } from 'react';

// components and context imports
import { FormContext } from '../FormContext';
import RadioButton from '../../screens/events/RadioButton';

// utils and constants imports
import { CLUB_MEMBER_COUNT } from '../../utils/constants/strings';
import { Spacing } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { Typography } from '../../utils/responsiveUI';
import { ALL, MORE_THAN_10_MEMBERS } from '../../utils/constants/strings';

const ClubMemberCount = () => {
    const { form, updateForm } = useContext(FormContext);
    return (
        <View style={styles.card}>
            <Text style={[styles.titleStyle, { marginBottom: Spacing.SCALE_12 }]}>{CLUB_MEMBER_COUNT}</Text>
            <RadioButton
                selected={form.clubMemberCount === ALL ? true : false}
                placeHolder={ALL}
                onPress={() => updateForm('clubMemberCount', ALL)}
                textStyle={styles.radioTextStyle}
                containerStyle={{ marginVertical: Spacing.SCALE_8 }}
            />
            <RadioButton
                selected={form.clubMemberCount === MORE_THAN_10_MEMBERS ? true : false}
                placeHolder={MORE_THAN_10_MEMBERS}
                onPress={() => updateForm('clubMemberCount', MORE_THAN_10_MEMBERS)}
                textStyle={styles.radioTextStyle}
                containerStyle={{ marginVertical: Spacing.SCALE_8 }}
            />
        </View>
    );
};

export default ClubMemberCount;

const styles = StyleSheet.create({
    card: {
        padding: Spacing.SCALE_12,
        backgroundColor: colors.white,
        borderRadius: Spacing.SCALE_10,
        borderWidth: 1,
        borderColor: colors.lightgray,
        marginVertical: Spacing.SCALE_2,
    },
    countStyle: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: colors.fadeBlack,
        fontWeight: '400',
    },
    btnContainer: {
        backgroundColor: colors.whiteRGB,
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_12,
        borderTopWidth: 1,
        borderTopColor: colors.lightgray,
        ...Platform.select({
            ios: {
                shadowColor: colors.shadowColor,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
            },
            android: {
                elevation: 10,
                shadowOffset: {
                    width: 0,
                    height: 5,
                },
                shadowColor: colors.shadowColor,
                shadowRadius: 4,
                shadowOpacity: 0.5,
            },
        }),
    },
    radioTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        color: colors.lightBlack,
    },
    titleStyle: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
    },
    lableTextStyle: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        color: colors.lightBlack,
    },
});
