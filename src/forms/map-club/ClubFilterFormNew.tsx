import React, { useContext, useEffect, useState } from 'react';
import { View, SafeAreaView, Dimensions, StyleSheet, Platform, Pressable, Text } from 'react-native';

import { AuthContext } from '../../context/AuthContext';
//@ts-ignore
import { LocationContext } from '../../context/LocationContext';
import Form from '../FormContext';
import TealButton from '../../components/buttons/TealButton';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { GlobalContext } from '../../context/contextApi';
import { colors } from '../../theme/theme';
import FilterHeader from './FilterHeader';
import TealSquareCheckbox from '../../components/checkbox/TealSquareCheckbox';
import TGGroupDropDown from './TGGroupDropDown';
import {
    ALL,
    CLUB_MEMBER_COUNT,
    CLUB_PERCENTAGE,
    CLUB_WITH_MY_TG_COMMUNITY,
    GENDER,
    MORE_THAN_10_MEMBERS,
} from '../../utils/constants/strings';
import { FilterState } from '../../interface';
import RadioButton from '../../screens/events/RadioButton';
import { ScrollView } from 'react-native-gesture-handler';
import ClubPercentage from './ClubPercentage';
import ClubMemberCount from './ClubMemberCount';
import constants from '../../utils/constants/constants';

//CleverTap import
const CleverTap = require('clevertap-react-native');

const { width } = Dimensions.get('window');

function FilterControl({ children }: { children: React.ReactNode }) {
    return children;
}

interface ClubFilterFormNewProps {
    filterState: [FilterState, React.Dispatch<React.SetStateAction<FilterState>>];
    setFilterActive: (active: boolean) => void;
    setModal: () => void;
    tierCount: {
        Fern: number;
        Moss: number;
        Sage: number;
        Olive: number;
    };
    playedCount: number;
    femaleCount: number;
    playAsCoupleCount: number;
}

let defaultFilter = {
    fern: false,
    sage: false,
    moss: false,
    olive: false,
    friendsAndContact: false,
    myTGGroupMember: false,
    openOfferClubs: false,
    favoriteClubs: false,
    playedClubs: false,
    playAsCouple: false,
    category: 'All',
    selectedTGGroup: [],
    clubswithFemaleMembers: false,
    clubPercentage: 'All',
    clubMemberCount: 'All',
};

export default function ClubFilterFormNew({
    filterState,
    setFilterActive,
    setModal,
    tierCount,
    playedCount,
    femaleCount,
    playAsCoupleCount,
}: ClubFilterFormNewProps) {
    const { user } = useContext(AuthContext);
    const [filter, setFilter] = filterState;
    const { actions, state } = useContext(GlobalContext);
    //@ts-ignore
    const { userLocation } = useContext(LocationContext);
    const [showDropDown, setShowDropDown] = useState(false);

    useEffect(() => {
        if (state?.mapFilterState) {
            handleDefaultFilter();
        }
    }, [state?.mapFilterState]);

    const handleDefaultFilter = () => {
        setFilter(state?.mapFilterState);
    };

    function applyFilter(form: any) {
        setFilter(form);
        setModal();
        actions.setMapFilterState(form);

        if (JSON.stringify(form) !== JSON.stringify(defaultFilter)) {
            setFilterActive(true);
            CleverTap.recordEvent(constants.CLEVERTAP.Club_Filter_Applied, {
                'Name of whatever filter is applied among': JSON.stringify(form),
                'User Email': user?.email,
                'User tier': user?.tier,
                'User Membership': user?.membership_plan?.name,
                'User’s Location': JSON.stringify(userLocation),
            });
        } else {
            setFilterActive(false);
        }
    }

    return (
        <SafeAreaView style={styles.safeArea}>
            {/* @ts-ignore */}
            <Form initialValues={state?.mapFilterState} onSubmit={applyFilter}>
                <View style={styles.mainContainer}>
                    <FilterHeader
                        onBackPress={() => setModal()}
                        defaultFilter={defaultFilter}
                        handleDefaultFilter={handleDefaultFilter}
                    />
                    <ScrollView
                        style={styles.scrollView}
                        contentContainerStyle={styles.scrollViewContent}
                        nestedScrollEnabled={true}
                        showsVerticalScrollIndicator={false}>
                        <View style={styles.container}>
                            <FilterControl>
                                {/* Tier */}
                                <View style={styles.card}>
                                    <Text style={styles.titleStyle}>Tier</Text>
                                    <View style={styles.tierContainer}>
                                        <TealSquareCheckbox
                                            name="fern"
                                            label="Fern"
                                            labelStyle={styles.lableTextStyle}
                                            checkBoxContainerStyle={{ marginBottom: 0 }}
                                        />
                                        {tierCount.Fern && <Text style={styles.countStyle}>{tierCount.Fern}</Text>}
                                    </View>
                                    <View style={styles.tierContainer}>
                                        <TealSquareCheckbox
                                            name="sage"
                                            label="Sage"
                                            labelStyle={styles.lableTextStyle}
                                            checkBoxContainerStyle={{ marginBottom: 0 }}
                                        />
                                        {tierCount.Sage && <Text style={styles.countStyle}>{tierCount.Sage}</Text>}
                                    </View>
                                    <View style={styles.tierContainer}>
                                        <TealSquareCheckbox
                                            name="moss"
                                            label="Moss"
                                            labelStyle={styles.lableTextStyle}
                                            checkBoxContainerStyle={{ marginBottom: 0 }}
                                        />
                                        {tierCount.Moss && <Text style={styles.countStyle}>{tierCount.Moss}</Text>}
                                    </View>
                                    <View style={styles.tierContainer}>
                                        <TealSquareCheckbox
                                            name="olive"
                                            label="Olive"
                                            labelStyle={styles.lableTextStyle}
                                            checkBoxContainerStyle={{ marginBottom: 0 }}
                                        />
                                        {tierCount.Olive && <Text style={styles.countStyle}>{tierCount.Olive}</Text>}
                                    </View>
                                </View>

                                {/* Club Percentage */}
                                <ClubPercentage />

                                {/* Club Member Count */}
                                <ClubMemberCount />

                                {/* My Tg Community */}
                                <View style={styles.card}>
                                    <Text style={[styles.titleStyle, { marginBottom: Spacing.SCALE_12 }]}>
                                        {CLUB_WITH_MY_TG_COMMUNITY}
                                    </Text>
                                    <View style={styles.tierSubContainer}>
                                        <View style={{ marginTop: Spacing.SCALE_8 }}>
                                            <TealSquareCheckbox
                                                name="friendsAndContact"
                                                label="My Friends and Contacts"
                                                labelStyle={styles.lableTextStyle}
                                            />
                                            <TealSquareCheckbox
                                                name="myTGGroupMember"
                                                label="My TG Group Members"
                                                labelStyle={styles.lableTextStyle}
                                            />

                                            <TGGroupDropDown
                                                showDropDown={showDropDown}
                                                //@ts-ignore
                                                setShowDropDown={setShowDropDown}
                                                containerStyle={{ marginBottom: Spacing.SCALE_8 }}
                                            />
                                        </View>
                                    </View>
                                </View>

                                {user?.playAsCouple && (
                                    <View style={styles.card}>
                                        <View style={[styles.tierContainer, { marginVertical: 0 }]}>
                                            <TealSquareCheckbox
                                                name="playAsCouple"
                                                label="Play as Couple"
                                                labelStyle={[styles.lableTextStyle]}
                                                checkBoxContainerStyle={{ marginBottom: 0 }}
                                            />
                                            {playAsCoupleCount && (
                                                <Text style={styles.countStyle}>{playAsCoupleCount}</Text>
                                            )}
                                        </View>
                                    </View>
                                )}
                                {user?.gender === GENDER.FEMALE && (
                                    <View style={styles.card}>
                                        <View style={[styles.tierContainer, { marginVertical: 0 }]}>
                                            <TealSquareCheckbox
                                                name="clubswithFemaleMembers"
                                                label="Clubs with Female Members"
                                                labelStyle={[styles.lableTextStyle]}
                                                checkBoxContainerStyle={{ marginBottom: 0 }}
                                            />
                                            {femaleCount && <Text style={styles.countStyle}>{femaleCount}</Text>}
                                        </View>
                                    </View>
                                )}
                                <View style={styles.card}>
                                    <View style={[styles.tierContainer, { marginVertical: 0 }]}>
                                        <TealSquareCheckbox
                                            name="favoriteClubs"
                                            label="Favorite Clubs"
                                            labelStyle={[styles.lableTextStyle]}
                                            checkBoxContainerStyle={{ marginBottom: 0 }}
                                        />
                                        {user?.favorite_clubs?.length && (
                                            <Text style={styles.countStyle}>{user?.favorite_clubs?.length}</Text>
                                        )}
                                    </View>
                                </View>
                                <View style={styles.card}>
                                    <View style={[styles.tierContainer, { marginVertical: 0 }]}>
                                        <TealSquareCheckbox
                                            name="playedClubs"
                                            label="Played Clubs"
                                            labelStyle={[styles.lableTextStyle]}
                                            checkBoxContainerStyle={{ marginBottom: 0 }}
                                        />
                                        {playedCount && <Text style={styles.countStyle}>{playedCount}</Text>}
                                    </View>
                                </View>
                            </FilterControl>
                        </View>
                    </ScrollView>

                    <View style={styles.btnContainer}>
                        <TealButton
                            text="Apply"
                            btnStyle={styles.btnStyle}
                            fontSize={Typography.FONT_SIZE_14}
                            fontFamily="Ubuntu-Regular"
                            textStyle={{ fontWeight: '400', lineHeight: Size.SIZE_18 }}
                        />
                    </View>
                </View>
            </Form>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: colors.white,
    },
    mainContainer: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
        backgroundColor: colors.screenBG,
    },
    scrollViewContent: {
        paddingBottom: Spacing.SCALE_80, // Add extra padding for button
    },
    container: {
        marginTop: Spacing.SCALE_12,
        paddingHorizontal: Spacing.SCALE_16,
    },
    tierContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginVertical: Spacing.SCALE_8,
    },
    tierSubContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'flex-start',
    },

    btnStyle: {
        width: width - 30,
        borderRadius: 5,
    },
    titleStyle: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
    },
    lableTextStyle: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        color: colors.lightBlack,
    },
    fontSize16: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
    },
    horizontalLine: {
        flex: 1,
        height: 0.5,
        backgroundColor: colors.inputFieldBorderColor,
        marginVertical: Spacing.SCALE_20,
        zIndex: 1,
    },
    card: {
        padding: Spacing.SCALE_12,
        backgroundColor: colors.white,
        borderRadius: Spacing.SCALE_10,
        borderWidth: 1,
        borderColor: colors.lightgray,
        marginVertical: Spacing.SCALE_2,
    },
    countStyle: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: colors.fadeBlack,
        fontWeight: '400',
    },
    btnContainer: {
        backgroundColor: colors.whiteRGB,
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_12,
        borderTopWidth: 1,
        borderTopColor: colors.lightgray,
        ...Platform.select({
            ios: {
                shadowColor: colors.shadowColor,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
            },
            android: {
                elevation: 10,
                shadowOffset: {
                    width: 0,
                    height: 5,
                },
                shadowColor: colors.shadowColor,
                shadowRadius: 4,
                shadowOpacity: 0.5,
            },
        }),
    },
    radioTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        color: colors.lightBlack,
    },
});
