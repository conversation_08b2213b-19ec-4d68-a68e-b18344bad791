import React, { useState } from 'react';
import { StyleSheet, Text, TextInput, View } from 'react-native';
import CommonBottomSheet from '../../../components/commonBottomSheet/CommonBottomSheet';
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { SUB_HEADER_EDIT_AMBASSADOR } from '../../../utils/constants/strings';
import { colors } from '../../../theme/theme';

type EditAmbassadorPopupProps = {
    isHaveToShowPopup: boolean;
    setIsHaveToShowPopup: (value: boolean) => void;
    textPopupState: [string, React.Dispatch<React.SetStateAction<string>>];
    updateAmbassadorCustomMessage: () => void;
};

const EditAmbassadorPopup: React.FC<EditAmbassadorPopupProps> = ({
    isHaveToShowPopup,
    setIsHaveToShowPopup,
    textPopupState,
    updateAmbassadorCustomMessage,
}) => {
    const [ambassadorMessage, setAmbassadorMessage] = textPopupState;
    const [isEditMessage, setIsEditMessage] = useState<boolean>(false);

    const handleSaveButton = () => {
        setIsEditMessage(false);
        updateAmbassadorCustomMessage();
    };

    return (
        <CommonBottomSheet showPopup={isHaveToShowPopup}>
            <View style={styles.container}>
                <Text style={styles.header}>Message from Ambassador</Text>
                <View style={{ width: Size.SIZE_335, alignSelf: 'center' }}>
                    <Text style={styles.subHeader}>{SUB_HEADER_EDIT_AMBASSADOR}</Text>
                </View>
                <View style={{ marginHorizontal: Spacing.SCALE_16 }}>
                    <Text style={styles.message}>Message</Text>
                    <View
                        style={{
                            borderColor: colors.inputFieldBorderColor,
                            borderRadius: Size.SIZE_4,
                            borderWidth: 1,
                            paddingHorizontal: Spacing.SCALE_10,
                        }}>
                        <TextInput
                            value={ambassadorMessage}
                            onChangeText={(text) => setAmbassadorMessage(text)}
                            multiline
                            style={isEditMessage ? styles.editableInputTextStyle : styles.inputTextStyle}
                            editable={isEditMessage}
                        />
                    </View>
                </View>
                <View style={styles.btnWrapper}>
                    <TealButtonNew
                        text="Dismiss"
                        btnStyle={[styles.btnStyle, { backgroundColor: colors.greyRgba }]}
                        textStyle={{
                            fontSize: Typography.FONT_SIZE_14,
                            color: colors.dark_charcoal,
                            fontFamily: 'Ubuntu-medium',
                        }}
                        onPress={() => {
                            setIsHaveToShowPopup(false);
                        }}
                        disabledStyle={false}
                    />
                    <TealButtonNew
                        //@ts-ignore
                        fontSize={16}
                        onPress={() => {
                            isEditMessage ? handleSaveButton() : setIsEditMessage(true);
                        }}
                        btnStyle={[styles.btnStyle]}
                        text={isEditMessage ? 'Save Changes' : 'Edit Message'}
                        disabled={false}
                    />
                </View>
            </View>
        </CommonBottomSheet>
    );
};

export default EditAmbassadorPopup;

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.whiteRGB,
        maxHeight: Size.SIZE_640,
        borderTopLeftRadius: Size.SIZE_8,
        borderTopRightRadius: Size.SIZE_8,
        width: '100%',
        position: 'absolute',
        bottom: 0,
        paddingVertical: Spacing.SCALE_24,
    },
    header: {
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_32,
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '500',
        textAlign: 'center',
    },
    subHeader: {
        color: colors.greyRgb,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        marginTop: Spacing.SCALE_12,
        marginBottom: Spacing.SCALE_24,
        textAlign: 'center',
    },
    message: {
        color: colors.greyRgb,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_16,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        marginBottom: Spacing.SCALE_8,
    },
    inputTextStyle: {
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_20,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        paddingBottom: Spacing.SCALE_10,
    },
    editableInputTextStyle: {
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_20,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        backgroundColor: colors.greyRgba,
        paddingHorizontal: 12,
        marginVertical: 12,
        borderRadius: Size.SIZE_4,
        maxHeight: Size.SIZE_150,
        paddingBottom: Spacing.SCALE_10,
    },
    btnWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_20,
        backgroundColor: colors.whiteRGB,
        paddingTop: Spacing.SCALE_20,
    },
    btnStyle: {
        borderRadius: Size.SIZE_8,
        alignSelf: 'center',
        width: '45%',
    },
});
