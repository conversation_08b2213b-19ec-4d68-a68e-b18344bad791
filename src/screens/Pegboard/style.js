import { Platform, StyleSheet } from 'react-native';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { IS_BIG } from '../../utils/responsiveUI/size';

export default StyleSheet.create({
    container: {
        flex: 1,
    },
    headerContainer: {
        justifyContent: 'center',
        paddingBottom: Spacing.SCALE_10,
    },
    font18: {
        fontSize: Typography.FONT_SIZE_18,
        fontFamily: 'Ubuntu',
        marginLeft: 5,
        textAlign: 'left',
    },
    font14: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu',
    },
    font16: {
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        lineHeight: Size.SIZE_18,
        color: colors.lightBlack,
    },
    font12: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        lineHeight: Size.SIZE_14,
        color: colors.darkgray,
    },
    spacing6: {
        marginBottom: Spacing.SCALE_6,
    },
    headerHeading: {
        fontWeight: '400',
    },
    bodyContainer: {
        flex: 1,
        borderTopRightRadius: Size.SIZE_10,
        borderTopLeftRadius: Size.SIZE_10,
        backgroundColor: colors.whiteF6,
        paddingHorizontal: Spacing.SCALE_12,
    },
    listStyle: {
        paddingVertical: Spacing.SCALE_18,
        justifyContent: 'center',
        paddingHorizontal: Spacing.SCALE_15,
        flexDirection: 'row',
        borderRadius: Size.SIZE_8,
        marginBottom: Spacing.SCALE_10,
        backgroundColor: colors.whiteColor,
    },
    listText: {
        fontSize: 16,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '500',
    },
    listSubText: {
        fontSize: 12,
        fontFamily: 'Ubuntu-Regular',
    },
    headingTxt: {},
    listHeaderTxt: {
        color: '#000',
        fontWeight: '500',
    },
    emptyList: {
        height: Size.SIZE_60,
        alignSelf: 'center',
        justifyContent: 'center',
    },
    leaderboardHeader: {
        marginTop: Spacing.SCALE_20,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    headerStyle: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        lineHeight: Size.SIZE_14,
        color: colors.fadeBlack,
    },
    emptyPageStyle: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    iconContainer: {
        backgroundColor: 'rgba(9, 128, 137, 0.12)',
        padding: 10,
        borderRadius: 50,
        marginBottom: Spacing.SCALE_15,
    },
    emptyMessageText: {
        fontSize: Typography.FONT_SIZE_13,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        lineHeight: Size.SIZE_14,
        color: colors.fadeBlack,
    },
    paddingRight: {
        paddingRight: Spacing.SCALE_140,
    },
    listContainer: {
        backgroundColor: colors.white,
        borderRadius: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignContent: 'center',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_5,
        paddingVertical: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_10,
    },
    rankBubbleStyle: {
        backgroundColor: colors.lightgray,
        height: Size.SIZE_26,
        width: Size.SIZE_26,
        borderRadius: 50,
        justifyContent: 'center',
        alignItems: 'center',
    },
    rankText: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        lineHeight: Size.SIZE_10,
        color: colors.lightBlack,
    },
    imageWrapper: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        backgroundColor: colors.lightgray,
        borderRadius: Size.SIZE_4,
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: Spacing.SCALE_14,
    },
    imageStyle: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        resizeMode: 'cover',
        borderRadius: Size.SIZE_4,
    },
    imageTextStyle: {
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_20,
        color: 'rgba(9, 128, 137, 1)',
        fontFamily: 'Ubuntu-Medium',
    },
    userNameStyle: {
        color: colors.lightBlack,
    },
    starImage: {
        position: 'absolute',
        left: 20,
        top: -3,
    },
    rankOneBubble: {
        backgroundColor: colors.white,
    },
    rankOneContainer: {
        backgroundColor: 'rgba(9, 128, 137, 0.12)',
    },
    playedTextContainer: {
        marginRight: Spacing.SCALE_12,
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
    tooltipStyle: {
        position: 'absolute',
        right: Spacing.SCALE_16,
        top: Platform.OS === 'ios' ? Spacing.SCALE_90 : Spacing.SCALE_50,
        zIndex: 1000,
    },
});
