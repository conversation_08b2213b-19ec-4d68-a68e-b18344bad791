import {Platform, StyleSheet} from 'react-native';

import {Size, Spacing, Typography} from '../../utils/responsiveUI';
import {colors} from '../../theme/theme';

export default styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    titleStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        fontSize: Typography.FONT_SIZE_18,
        lineHeight: Size.SIZE_20,
        color: colors.lightBlack,
        textAlign: 'left',
        marginTop: Spacing.SCALE_2,
    },
    headerContainer: {
        justifyContent: 'center',
        paddingTop: Spacing.SCALE_30,
        paddingBottom: Spacing.SCALE_18,
        backgroundColor: colors.whiteColor,
        position: 'relative',
    },
    body: {
        flex: 1,
        padding: Spacing.SCALE_16,
        marginTop: Spacing.SCALE_20,
    },
    subHeaderWrapper: {
        padding: Spacing.SCALE_16,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    subHeaderText1: {
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        color: colors.dark_charcoal,
    },
    subHeaderText2: {
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        color: colors.tealRgb,
    },
    editClubWrapper: {
        flexDirection: 'row'

    },
    subHeaderText3: {
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_16,
        color: colors.lightBlack,
    },
    creatorModeWrapper: {
        width: Size.SIZE_80,
        height: Size.SIZE_22,
        backgroundColor: colors.tealRGBAColor,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: Spacing.SCALE_10,
        borderRadius: Size.SIZE_6,
    },
    creatorModeText: {
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        fontSize: Typography.FONT_SIZE_10,
        lineHeight: Size.SIZE_10,
        color: colors.tealRgb,
    },
    box: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    tooltipStyle: {
        position: 'absolute',
        right: Spacing.SCALE_16,
        top: Platform.OS === 'ios' ? Spacing.SCALE_80 : Spacing.SCALE_60,
        zIndex: 1000,
    },
});
