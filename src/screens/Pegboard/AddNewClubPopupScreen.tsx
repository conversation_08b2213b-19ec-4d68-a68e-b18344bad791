import React, { useContext, useEffect, useState } from 'react';
import {
    Modal,
    Pressable,
    StyleSheet,
    TouchableWithoutFeedback,
    View,
    Text,
    KeyboardAvoidingView,
    Platform,
    TextInput,
    Keyboard,
} from 'react-native';

//@ts-ignore
import Cross from '../../assets/svg/cross.svg';
import MyFriendButton from '../../components/buttons/MyFriendButton';
import GetAddress from '../../screens/myContacts/view/AddressSearch';
import InputLabel from '../../components/fields/InputLabel';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { AuthContext } from '../../context/AuthContext';
import { addManualClub } from './actions/addManualClub';
import showToast from '../../components/toast/CustomToast';
import { useNavigation } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

// Define the route params interface
interface AddNewClubPopupScreenParams {
    header: string;
    body: string;
    inputState: [any, React.Dispatch<React.SetStateAction<any>>]; // You can replace 'any' with more specific type
    selectedClub: string;
    pegboardId: string;
    handleGetClubList: () => void;
    setClubInputState: (value: string) => void;
}

// Define the component props type
type Props = NativeStackScreenProps<
    {
        AddNewClubPopupScreen: AddNewClubPopupScreenParams;
    },
    'AddNewClubPopupScreen'
>;

const AddNewClubPopupScreen: React.FC<Props> = ({ route }) => {
    const { header, body, inputState, selectedClub, pegboardId, handleGetClubList, setClubInputState } = route?.params;
    const { user } = useContext(AuthContext);
    const [modalInput, setModalInput] = inputState;
    const [address, setAddress] = useState('');
    const [long, setLong] = useState('');
    const [lat, setLat] = useState('');
    const [errors, setErrors] = useState({ address: '' });
    const navigation = useNavigation();

    useEffect(() => {
        selectedClub && setModalInput((prev: any) => ({ ...prev, clubName: selectedClub }));
    }, [selectedClub]);
    console.log('modalInput', modalInput);

    /**
     * Api call for add manual club to DB
     */
    const handleSaveButton = async () => {
        if (!address) {
            setErrors((prev) => ({
                ...prev,
                address: 'Address is required',
            }));
        } else {
            let payload = {
                userId: user?.id,
                pegboardId: pegboardId,
                name: modalInput?.clubName || selectedClub,
                address: address,
                lat: lat,
                lng: long,
            };
            const manualClubResponse = await addManualClub(payload);
            if (manualClubResponse?.status) {
                navigation.goBack();
                handleGetClubList();
                setClubInputState('');
            } else {
                showToast({});
            }
        }
    };

    return (
        <View
            style={{
                flex: 1,
            }}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}
                //@ts-ignore
                keyboardShouldPersistTaps="handled">
                <View style={{ flex: 1 }}>
                    <Pressable
                        style={{
                            flex: 1,
                            backgroundColor: colors.transparentRgba,
                        }}
                        onPress={() => Keyboard.dismiss()}
                    />
                    <View style={styles.popupWrapper}>
                        <View style={styles.headerWrapper}>
                            <Pressable
                                style={styles.crossIconWrapper}
                                onPress={() => {
                                    navigation.goBack();
                                }}>
                                <Cross />
                            </Pressable>
                        </View>
                        <View style={{ zIndex: 1 }}>
                            <Text style={styles.popupHeaderText}>{header}</Text>
                            <Text style={styles.popupBodyText}>{body}</Text>
                            <View style={styles.inputWrapper}>
                                <InputLabel label={'Club name'} labelStyle={styles.labelStyle} />
                                <TextInput
                                    value={modalInput?.clubName || selectedClub}
                                    placeholderTextColor={colors.darkGreyRgba}
                                    onChangeText={(data) =>
                                        setModalInput((prev: any) => ({
                                            ...prev,
                                            clubName: data.trimStart(),
                                        }))
                                    }
                                    editable={false}
                                    style={styles.inputStyle}
                                />
                            </View>

                            {/* Address input field */}
                            <View style={styles.inputWrapper}>
                                <GetAddress
                                    setAddress={setAddress}
                                    setLat={setLat}
                                    setLong={setLong}
                                    errors={errors}
                                    setErrors={setErrors}
                                    errorStyle={{ textAlign: 'left' }}
                                    customErrorTitleStyle={{ color: 'red' }}
                                />
                            </View>
                            <MyFriendButton
                                label="Save"
                                loading={false}
                                buttonContainer={{
                                    paddingHorizontal: Spacing.SCALE_30,
                                    marginBottom: Spacing.SCALE_20,
                                    zIndex: 1,
                                }}
                                onPress={() => {
                                    handleSaveButton();
                                }}
                            />
                        </View>
                    </View>
                </View>
            </KeyboardAvoidingView>
        </View>
    );
};

export default AddNewClubPopupScreen;

const styles = StyleSheet.create({
    popupWrapper: {
        width: '100%',
        minHeight: Spacing.SCALE_250,
        position: 'absolute',
        bottom: 0,
        backgroundColor: colors.white,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    popupHeaderText: {
        color: colors.dark_charcoal,
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '400',
        lineHeight: Size.SIZE_27,
        paddingHorizontal: Spacing.SCALE_18,
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
    },
    headerWrapper: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingTop: Spacing.SCALE_18,
        paddingBottom: Spacing.SCALE_12,
    },
    crossIconWrapper: {
        paddingHorizontal: 20,
        paddingTop: 5,
    },
    popupBodyText: {
        color: colors.greyRgb,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        lineHeight: Size.SIZE_14,
        paddingHorizontal: Spacing.SCALE_30,
        marginVertical: Spacing.SCALE_10,
        fontFamily: 'Ubuntu-Medium',
        textAlign: 'center',
        marginTop: Spacing.SCALE_20,
    },
    inputStyle: {
        borderBottomWidth: 1,
        borderBottomColor: colors.dividerColor,
        paddingBottom: Spacing.SCALE_5,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        color: colors.dark_charcoal,
        marginBottom: Spacing.SCALE_20,
    },
    labelStyle: {
        marginBottom: Spacing.SCALE_10,
    },
    inputWrapper: {
        paddingHorizontal: Spacing.SCALE_18,
    },
    dropDownContainerStyle: {
        height: Size.SIZE_150,
        backgroundColor: colors.white,
        position: 'absolute',
        top: Size.SIZE_50,
        width: '100%',
        zIndex: 1,
        alignSelf: 'center',
        borderRadius: Size.SIZE_4,
        shadowColor: colors.shadowColor,
        shadowOffset: { width: -2, height: 4 },
        shadowOpacity: 0.2,
        elevation: 9,
        shadowRadius: 3,
        padding: Spacing.SCALE_10,
    },
    dropdownText: {
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_14,
        color: colors.darkgray,
    },
    dropdownTextWrapper: {
        marginBottom: Spacing.SCALE_10,
    },
});
