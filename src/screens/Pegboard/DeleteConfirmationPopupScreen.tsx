import React, { useContext, useEffect, useState } from 'react';
import {
    Modal,
    Pressable,
    StyleSheet,
    TouchableWithoutFeedback,
    View,
    Text,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';

import Cross from '../../assets/svg/cross.svg';
import MyFriendButton from '../../components/buttons/MyFriendButton';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { useNavigation } from '@react-navigation/native';

const DeleteConfirmationPopupScreen = ({ route }) => {
    const { header, body, handleDeletePegboard = () => {}, club, btnLabel = '' } = route?.params;
    const navigation = useNavigation();

    return (
        <View
            style={{
                flex: 1,
            }}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}
                keyboardShouldPersistTaps="handled">
                <View style={{ flex: 1 }}>
                    <Pressable
                        style={{
                            flex: 1,
                            backgroundColor: colors.transparentRgba,
                        }}
                    />
                    <View style={styles.popupWrapper}>
                        <View style={styles.headerWrapper}>
                            <Pressable
                                style={styles.crossIconWrapper}
                                onPress={() => {
                                    navigation.goBack();
                                }}>
                                <Cross />
                            </Pressable>
                        </View>
                        <Text style={styles.popupHeaderText}>{header}</Text>
                        <Text style={styles.popupBodyText}>{body}</Text>
                    </View>
                    <MyFriendButton
                        text={btnLabel}
                        label={btnLabel}
                        loading={false}
                        buttonContainer={{
                            paddingHorizontal: Spacing.SCALE_30,
                            marginBottom: Spacing.SCALE_20,
                        }}
                        btn={{ backgroundColor: colors.orange }}
                        onPress={() => {
                            navigation.goBack();
                            handleDeletePegboard(club?.club_id);
                        }}
                    />
                </View>
            </KeyboardAvoidingView>
        </View>
    );
};
export default DeleteConfirmationPopupScreen;

const styles = StyleSheet.create({
    popupWrapper: {
        width: '100%',
        minHeight: Spacing.SCALE_250,
        position: 'absolute',
        bottom: 0,
        backgroundColor: colors.white,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    popupHeaderText: {
        color: colors.dark_charcoal,
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '400',
        lineHeight: Size.SIZE_27,
        paddingHorizontal: Spacing.SCALE_18,
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
        marginTop: Spacing.SCALE_20,
    },
    headerWrapper: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingTop: Spacing.SCALE_18,
        paddingBottom: Spacing.SCALE_12,
    },
    crossIconWrapper: {
        paddingHorizontal: 20,
        paddingTop: 5,
    },
    popupBodyText: {
        color: colors.greyRgb,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        lineHeight: Size.SIZE_14,
        paddingHorizontal: Spacing.SCALE_18,
        marginVertical: Spacing.SCALE_10,
        fontFamily: 'Ubuntu-Medium',
        textAlign: 'center',
        marginTop: Spacing.SCALE_20,
    },
});
