import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import Icon from 'react-native-vector-icons/Feather';

import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import config from '../../config';

const SelectorInput = ({
    type,
    navigation,
    handleCallBack,
    data,
    placeHolder,
    disable = false,
    searchCallBack = () => {},
}) => {
    return (
        <TouchableOpacity
            style={styles.inputWrapper}
            onPress={() =>
                disable
                    ? null
                    : navigation.push(config.routes.CUSTOM_SELECTOR, {
                          type: type,
                          callBack: handleCallBack,
                          searchCallBack: searchCallBack,
                      })
            }>
            <Text numberOfLines={1} style={[styles.text, { color: data ? colors.dark_charcoal : colors.darkgray }]}>
                {(data && data?.length > Size.SIZE_14 ? data.substring(0, Size.SIZE_14) + '...' : data) || placeHolder}
            </Text>
            <Icon name="chevron-down" size={20} color={'gray'} style={{ right: 10 }} />
        </TouchableOpacity>
    );
};

export default SelectorInput;

const styles = StyleSheet.create({
    inputWrapper: {
        height: Size.SIZE_42,
        borderColor: colors.borderGray,
        borderWidth: 1,
        marginBottom: Spacing.SCALE_20,
        paddingLeft: Spacing.SCALE_10,
        borderRadius: 8,
        backgroundColor: colors.white,
        color: colors.lightBlack, // Text color for selected value
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    text: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
});
