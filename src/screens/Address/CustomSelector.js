import { FlatList, Pressable, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Spacing } from '../../utils/responsiveUI';
import { GlobalContext } from '../../context/contextApi';
import { useNavigation } from '@react-navigation/native';
import { colors } from '../../theme/theme';
import StreamChatSearchInput from '../my-TG-friends/view/StreamChatSearchInput';
import debounce from '../my-TG-Stream-Chat/action';
import CrossIcon from '../../assets/svg/Cross1.svg';

const CustomSelector = ({ route }) => {
    const { state } = useContext(GlobalContext);
    const navigation = useNavigation();
    const { type = '', callBack = () => {}, searchCallBack = () => {} } = route.params;
    const selectorData =
        type === 'country' ? state?.country : type === 'state' ? state?.state : type === 'city' ? state?.city : [];
    const [searchData, setSearchData] = useState('');
    const [triggerSearchString, setTriggerSearchString] = useState('');

    useEffect(() => {
        updateTriggerSearchStringUpdate(searchData);
    }, [searchData]);

    const updateTriggerSearchStringUpdate = useCallback(
        debounce((value) => {
            setTriggerSearchString(value);
        }, 200),
        [],
    );

    useEffect(() => {
        searchCallBack(triggerSearchString);
    }, [triggerSearchString]);

    return (
        <View style={styles.container}>
            <View
                style={{
                    flex: 1,
                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    padding: Spacing.SCALE_16,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                <View
                    style={{
                        width: '100%',
                        height: '80%',
                        backgroundColor: 'white',
                        padding: Spacing.SCALE_16,
                        borderRadius: 10,
                    }}>
                    <CrossIcon
                        width={15}
                        height={15}
                        style={{ alignSelf: 'flex-end', padding: 10 }}
                        onPress={() => navigation.pop(1)}
                    />
                    <StreamChatSearchInput
                        searchState={[searchData, setSearchData]}
                        searchBoxWrapperStyle={{
                            marginVertical: 0,
                            marginHorizontal: -10,
                            marginTop: Spacing.SCALE_20,
                        }}
                        searchBoxStyle={{ fontFamily: 'Ubuntu-Regular' }}
                    />
                    <FlatList
                        data={selectorData}
                        renderItem={({ item }) => (
                            <Text
                                onPress={() => {
                                    callBack(item?.value, type);
                                    navigation.pop(1);
                                }}
                                style={{
                                    paddingHorizontal: 5,
                                    marginVertical: Spacing.SCALE_15,
                                    color: colors.black,
                                    fontFamily: 'Ubuntu-Regular',
                                }}>
                                {item?.label}
                            </Text>
                        )}
                        keyExtractor={(item, index) => index}
                        ListHeaderComponent={() => (
                            <Text
                                style={{ paddingHorizontal: 5, marginVertical: Spacing.SCALE_15 }}
                                onPress={() => {
                                    callBack(null, type);
                                    navigation.pop(1);
                                }}>
                                Select {type}
                            </Text>
                        )}
                        ListEmptyComponent={() => (
                            <Text
                                style={{
                                    paddingHorizontal: 5,
                                    marginVertical: Spacing.SCALE_15,
                                    color: colors.black,
                                    fontFamily: 'Ubuntu-Regular',
                                    alignSelf: 'center',
                                    marginTop: Spacing.SCALE_150,
                                }}>
                                No Data Found
                            </Text>
                        )}
                    />
                </View>
            </View>
        </View>
    );
};

export default CustomSelector;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
});
