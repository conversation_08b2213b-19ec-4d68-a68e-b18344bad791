import { StyleSheet, Text, View, Image, Pressable, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';

import UsersIcon from '../../../../assets/svg/UsersIcon.svg';
import ChatIcon from '../../../../assets/svg/ChatIcon.svg';
import { acceptPendingRequest, declinePendingRequest } from '../action/getPendingRequest';
import { useContext, useEffect, useState } from 'react';
import { AuthContext } from '../../../../context/AuthContext';
import Button from '../../../../components/buttons/CustomButton';
import DeleteConfirmationPopup from '../../../my-TG-Stream-Chat/view/DeleteConfirmationPopup';
import {
    DECLINE_MEMBER_REQUEST_POPUP_HEADER,
    DECLINE_MEMBER_REQUEST_POPUP_TITLE,
} from '../../../../utils/constants/strings';
import { createOneToOneChatFrndScreen } from '../../../my-TG-friends/action/createOnetoOneChat';
import { StreamChatContext } from '../../../../context/StreamChatContext';
import { GlobalContext } from '../../../../context/contextApi';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';
import SingleBtnConfirmationModal from '../../../../components/modals/SingleBtnConfirmationModal';
import { handleParticipantsNameAndUserName } from '../action/handleNameAndUserName';
import config from '../../../../config';

const PendingMemberRequestCard = ({ data, handlePendingMemberRequestList, groupStreamId = '' }) => {
    const { item } = data;
    const { user } = useContext(AuthContext);
    const { client, setChannel } = useContext(StreamChatContext);
    const { actions, state } = useContext(GlobalContext);

    const [acceptingRequest, setAcceptingRequest] = useState(false);
    const [declinePopupState, setDeclinePopupState] = useState(false);
    const [loading, setLoading] = useState(false);
    const [oneToOneChannelRes, setOneToOneChannelRes] = useState({});
    const [reason, setReason] = useState('');
    const navigation = useNavigation();
    const { allFriendsId } = state;

    //This useEffect is use to query one to one channel and set into stream chat context and navigate to message screen, after oneToOneChannelRes response update
    useEffect(() => {
        const handleChannelCreation = async () => {
            const channel = await client.queryChannels(
                {
                    members: { $in: [user?.id] },
                    id: { $eq: oneToOneChannelRes?.channel?.id },
                    $or: [{ hidden: { $eq: true } }, { hidden: { $eq: false } }],
                },
                {},
                {},
            );
            setChannel(channel?.[0]);
            actions?.updatedChannelAction(channel?.[0]);
            navigation.navigate(config.routes.MESSAGESCREEN, { screen: config.routes.PENDING_MEMBER_REQUEST });
            setLoading(false);
        };
        if (oneToOneChannelRes?.status == 1) {
            handleChannelCreation();
        }
    }, [oneToOneChannelRes]);

    //function to handle decline button
    const handleDeclineButton = async () => {
        setLoading(true);
        const declinePendingReqResponse = await declinePendingRequest({
            userId: user?.id,
            requestId: item?.id,
            reason: reason,
        });
        if (declinePendingReqResponse?.status) {
            handlePendingMemberRequestList(true);
        }
        setLoading(false);
    };

    //function to handle accept button
    const handleAcceptButton = async () => {
        setAcceptingRequest(true);
        setLoading(true);
        const acceptPendingReqResponse = await acceptPendingRequest({
            userId: user?.id,
            requestId: item?.id,
            groupId: groupStreamId,
        });
        if (acceptPendingReqResponse?.status) {
            handlePendingMemberRequestList(true);
        }
        setAcceptingRequest(false);
        setLoading(false);
    };

    //Create one to one chat when user click on chat icon
    const handleChatBtn = () => {
        setLoading(true);
        createOneToOneChatFrndScreen(user?.id, item?.userId, setOneToOneChannelRes, setLoading);
    };

    const redirectToUserProfile = () => {
        if (item?.userId !== user?.id) {
            navigation.navigate('UserProfileScreen', { selectedUser: { id: item?.userId } });
        }
    };

    return (
        <>
            <TouchableOpacity style={styles.container} onPress={redirectToUserProfile}>
                <View style={styles.card}>
                    <View style={styles.box1}>
                        <View style={styles.box}>
                            <View style={styles.imageContainer}>
                                {item?.profilePhoto ? (
                                    <Image source={{ uri: item?.profilePhoto }} style={styles.imageStyle} />
                                ) : (
                                    <Text style={styles.imageTextStyle}>
                                        {handleParticipantsNameAndUserName(allFriendsId, item, user)[0]}
                                    </Text>
                                )}
                            </View>
                            <View style={styles.box2}>
                                <View style={styles.nameWrapper}>
                                    <Text style={styles.name}>
                                        {handleParticipantsNameAndUserName(allFriendsId, item, user)}
                                    </Text>
                                </View>
                                <View style={styles.userIconWrapper}>
                                    <UsersIcon />
                                    <Text style={styles.text}>{item?.groupName}</Text>
                                </View>
                            </View>
                        </View>
                        <Pressable onPress={handleChatBtn}>
                            <ChatIcon />
                        </Pressable>
                    </View>
                    {item?.notes && (
                        <View style={styles.description}>
                            <Text style={styles.descriptionText}>{item?.notes}</Text>
                        </View>
                    )}
                    <View style={styles.btnContainer}>
                        <Button
                            label="Decline"
                            labelStyle={styles.btnText}
                            btnStyle={styles.btn1Wrapper}
                            onPress={() => setDeclinePopupState((prev) => !prev)}
                        />
                        <Button
                            label="Accept"
                            labelStyle={styles.btnText}
                            btnStyle={styles.btn2Wrapper}
                            loading={acceptingRequest}
                            onPress={handleAcceptButton}
                        />
                    </View>
                </View>
            </TouchableOpacity>
            {declinePopupState && (
                <SingleBtnConfirmationModal
                    openPopupState={[declinePopupState, setDeclinePopupState]}
                    popupHeading={DECLINE_MEMBER_REQUEST_POPUP_HEADER}
                    popupText={DECLINE_MEMBER_REQUEST_POPUP_TITLE}
                    onPress={handleDeclineButton}
                    loading={loading}
                    showReason={true}
                    reason={reason}
                    setReason={setReason}
                />
            )}
        </>
    );
};

export default PendingMemberRequestCard;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    card: {
        marginVertical: Spacing.SCALE_5,
        borderRadius: Spacing.SCALE_8,
        backgroundColor: colors.whiteRGB,
    },
    box: {
        justifyContent: 'flex-start',
        flexDirection: 'row',
    },
    box1: {
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_12,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    imageContainer: {
        width: 64,
        height: 64,
        backgroundColor: colors.greyRgba,
        borderRadius: Size.SIZE_4,
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: Spacing.SCALE_4,
    },
    btnContainer: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_17,
        paddingVertical: Spacing.SCALE_10,
        lineHeight: Size.SIZE_20,
    },
    imageTextStyle: {
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_28,
        fontWeight: '500',
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
        textTransform: 'capitalize',
    },
    imageStyle: {
        width: 64,
        height: 64,
        resizeMode: 'cover',
        borderRadius: Size.SIZE_4,
    },
    nameWrapper: {
        width: Size.SIZE_156,
    },
    name: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_19,
        fontWeight: '500',
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
    },
    text: {
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_14,
        fontWeight: '400',
        color: colors.greyRgb,
        marginLeft: Spacing.SCALE_10,
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
    },
    userIconWrapper: {
        flexDirection: 'row',
        paddingTop: Spacing.SCALE_10,
        alignItems: 'center',
    },
    box2: {
        marginLeft: Spacing.SCALE_10,
        justifyContent: 'center',
    },
    description: {
        width: Size.SIZE_300,
        paddingVertical: Spacing.SCALE_10,
        alignSelf: 'center',
    },
    descriptionText: {
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_20,
        fontWeight: '400',
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
    },
    btnText: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '500',
        color: colors.whiteRGB,
        fontFamily: 'Ubuntu-Medium',
    },
    btn1Wrapper: {
        backgroundColor: colors.orange,
        borderRadius: Size.SIZE_4,
        width: Size.SIZE_130,
        height: Size.SIZE_40,
        paddingVertical: 0,
    },
    btn2Wrapper: {
        backgroundColor: colors.tealRgb,
        borderRadius: Size.SIZE_4,
        width: Size.SIZE_130,
        height: Size.SIZE_40,
        paddingVertical: 0,
    },
});
