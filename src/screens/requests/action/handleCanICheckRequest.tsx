const CleverTap = require('clevertap-react-native');

import { tokenCheckURL } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';
import constants from '../../../utils/constants/constants';

const matchTokenActive = async (user_id: string) => {
    CleverTap.recordEvent(constants.CLEVERTAP.EVENTS.REQUESTS_CREATE_REQUEST);
    const res = await fetcher({
        endpoint: tokenCheckURL,
        method: 'POST',
        body: {
            user_id: user_id,
        },
    });
    return res;
};

export default matchTokenActive;
