import showToast from '../../../components/toast/CustomToast';
import { DELETE_REQUESTED_REQUEST } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';
import { REQUEST_MOVED_TO_HISTORY, SUCCESS } from '../../../utils/constants/strings';

export const handleDeleteRequestedRequest = (body: any, navigation: any, callBack: () => void) => {
    try {
        return fetcher({
            endpoint: DELETE_REQUESTED_REQUEST,
            method: 'POST',
            body,
        }).then((res) => {
            if (res?.status) {
                navigation.goBack();
                callBack();
                showToast({ type: SUCCESS, header: '', message: REQUEST_MOVED_TO_HISTORY });
            } else {
                showToast({});
            }
        });
    } catch (error) {
        showToast({});
    }
};
