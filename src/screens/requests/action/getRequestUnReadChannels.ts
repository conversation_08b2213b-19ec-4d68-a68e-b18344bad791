import { REQUEST_CHAT_GROUP } from '../../my-TG-Stream-Chat/client';

//Get stream unread messages
export const getStreamRequestChannel = async (client: any, user: any, actions: any, state: any) => {
    let page = 0;
    let updatedUnreadChannelsIds: any[] = [];
    let updatedChannels: any[] = [];
    while (1) {
        const channels = await client?.queryChannels(
            {
                type: REQUEST_CHAT_GROUP,
                members: { $in: [user?.id] },
                has_unread: true,
            },
            {},
            { limit: 30, offset: 30 * page },
        );
        page = page + 1;
        if (!channels?.length) {
            break;
        } else {
            let requestIds = channels.map((data: any) => data?.data?.request_id);
            const unreadChannelsIds = Array.from(new Set([...requestIds]));
            updatedUnreadChannelsIds = [...updatedUnreadChannelsIds, ...unreadChannelsIds];
            updatedChannels = [...updatedChannels, ...channels];
        }
    }
    actions.setUnreadChannels(updatedUnreadChannelsIds);
    actions.setUnreadChannelsObject(updatedChannels);
};
