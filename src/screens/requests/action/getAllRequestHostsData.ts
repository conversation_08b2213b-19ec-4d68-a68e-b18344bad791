import showToast from '../../../components/toast/CustomToast';
import { GET_ALL_REQUEST_HOST } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';

interface getAllRequestHostsDataProps {
    userId: string;
    requestIds: string[];
}

export const getAllRequestHostsData = async (body: getAllRequestHostsDataProps) => {
    try {
        return fetcher({
            endpoint: GET_ALL_REQUEST_HOST,
            method: 'POST',
            body,
        }).then((res) => res);
    } catch (error) {
        showToast({});
    }
};
