import { receivedAcceptedURL } from "../../../service/EndPoint";
import { receivedOpenURL, requestedAcceptedURLv4, requestedOpenURLv4 } from "../../../service/EndPoint";
import { getRequests } from "../Utils";

const getReceivedOpen = (userId: string) => {
    return getRequests(receivedOpenURL, {
        userId,
    });
};
const getRequestedOpen = (userId: string) => {
    return getRequests(requestedOpenURLv4, {
        userId,
    });
};
const getRequestedAccepted = (userId: string) => {
    return getRequests(requestedAcceptedURLv4, {
        userId,
    });
};
const getReceivedAccepted = (userId: string) => {
    return getRequests(receivedAcceptedURL, {
        userId,
    });
};

export const getAllRequest = async (actions: any, userId: string) => {
    try {
        actions.setAppSkeltonLoader(true);
        const [receivedOpen, requestedOpen, requestedAccepted, receivedAccepted] = await Promise.all([
            getReceivedOpen(userId),
            getRequestedOpen(userId),
            getRequestedAccepted(userId),
            getReceivedAccepted(userId),
        ]);
        const mergedAccepted = [...(requestedAccepted.data || []), ...(receivedAccepted.data || [])];
        const sortedAccepted = mergedAccepted.sort((a, b) => a.game_id - b.game_id);
        actions.setReceivedOpen(receivedOpen.data);
        actions.setRequestedOpen(requestedOpen.data);
        actions.setRequestedAccepted(requestedAccepted.data);
        actions.setReceivedAccepted(receivedAccepted.data);
        actions.setAllAcceptedRequests(sortedAccepted);
    } catch (error) {
        console.error('Error fetching requests:', error);
    } finally {
        actions.setAppSkeltonLoader(false);
    }
};