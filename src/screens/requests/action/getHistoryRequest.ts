import showToast from '../../../components/toast/CustomToast';
import { fetcher } from '../../../service/fetcher';
import { GET_ALL_REQUEST_HISTORY } from '../../../service/EndPoint';

export const getAllHistoryRequest = (body: object) => {
    try {
        return fetcher({
            endpoint: GET_ALL_REQUEST_HISTORY,
            method: 'POST',
            body,
        }).then((res) => res);
    } catch (error) {
        showToast({});
    }
};
