import { StyleProp, StyleSheet, Text, View, ViewStyle } from 'react-native';
import React from 'react';

import { RequestEmptyState } from '../../../assets/svg';
import { NO_ACTIVE_REQUESTS_HISTORY } from '../../../utils/constants/strings';
import { Size } from '../../../utils/responsiveUI';
import { Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';

const RequestEmptyScreen = ({ wrapperStyle, text='' }: { wrapperStyle?: StyleProp<ViewStyle>; text?: string }) => {
    return (
        <View style={[styles.emptyStateContainer, wrapperStyle]}>
            <RequestEmptyState />
            <Text style={styles.emptyStateText}>{text ? text : NO_ACTIVE_REQUESTS_HISTORY}</Text>
        </View>
    );
};

export default RequestEmptyScreen;

const styles = StyleSheet.create({
    emptyStateContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: Size.SIZE_20,
    },
    emptyStateText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
        lineHeight: Size.SIZE_21,
        marginTop: Size.SIZE_24,
    },
});
