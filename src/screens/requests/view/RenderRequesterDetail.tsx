import { Image, Linking, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useMemo } from 'react';

import { EmailGreyIcon, FirstTealIcon, PhoneGreyIcon, TGAmbassadorLogo } from '../../../assets/svg';
import { FIRST_TIME_REQUESTER_MESSAGE } from '../../../utils/constants/strings';
import { Size, Spacing } from '../../../utils/responsiveUI';
import { Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { User } from '../../../interface';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../interface/type';
import { GlobalContext } from '../../../context/contextApi';

const RenderRequesterDetail = ({
    request,
    userInfo,
    redirectToUserProfile,
    navigation,
    user,
    type,
}: {
    request: any;
    userInfo: any;
    redirectToUserProfile: any;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    user: User;
    type: string;
}) => {
    const { state } = useContext(GlobalContext);
    const {
        username,
        name,
        profilePhoto,
        isTgFounder,
        isSuperHost,
        isTgAmbassador,
        age,
        phone,
        email,
        userId,
        deleted_at,
    } = useMemo(() => {
        return {
            username: userInfo?.username,
            name: userInfo?.name,
            profilePhoto: userInfo?.profilePhoto,
            isTgFounder: userInfo?.isTgFounder,
            isSuperHost: userInfo?.isSuperHost,
            isTgAmbassador: userInfo?.isTgAmbassador,
            age: userInfo?.age,
            phone: userInfo?.phone,
            email: userInfo?.email,
            userId: userInfo?.userId,
            deleted_at: userInfo?.deleted_at,
        };
    }, [userInfo]);
    return (
        <>
            <Text style={styles.title}>Requester Details</Text>
            <View style={[styles.box, { paddingVertical: Spacing.SCALE_12 }]}>
                {request?.is_first_request && !deleted_at && (
                    <View style={styles.ftrTextContainer}>
                        <FirstTealIcon width={32} height={32} />
                        <Text style={styles.MainTextStyle}>
                            First Time Requestor:{' '}
                            <Text style={{ fontWeight: '400', fontFamily: 'Ubuntu-Regular' }}>
                                {FIRST_TIME_REQUESTER_MESSAGE}
                            </Text>
                        </Text>
                    </View>
                )}

                {(isTgFounder || isSuperHost) && !deleted_at && (
                    <View style={styles.badgeContainer}>
                        {isTgFounder && (
                            <View style={[styles.badge, styles.founderBadge]}>
                                <Text style={styles.badgeText}>Founder Club Member</Text>
                            </View>
                        )}
                        {isSuperHost && (
                            <View style={[styles.badge, styles.superHostBadge]}>
                                <Text style={styles.badgeText}>Super Host</Text>
                            </View>
                        )}
                    </View>
                )}

                <View style={[styles.userInfoContainer, { justifyContent: 'space-between' }]}>
                    <View style={[styles.userInfoContainer, { padding: 0 }]}>
                        <TouchableOpacity
                            style={styles.profileImageContainer}
                            onPress={() => {
                                deleted_at ? null : redirectToUserProfile({ userId: userId, navigation, user });
                            }}>
                            {profilePhoto ? (
                                deleted_at ? (
                                    <View
                                        style={[
                                            styles.profileImageFallback,
                                            { backgroundColor: deleted_at ? colors.lightShadeGray : colors.tealRgb },
                                        ]}>
                                        <Text style={styles.profileImageText}>{'?'}</Text>
                                    </View>
                                ) : (
                                    <Image source={{ uri: profilePhoto }} style={styles.profileImage} />
                                )
                            ) : (
                                <View
                                    style={[
                                        styles.profileImageFallback,
                                        { backgroundColor: deleted_at ? colors.lightShadeGray : colors.tealRgb },
                                    ]}>
                                    <Text style={styles.profileImageText}>
                                        {deleted_at ? '?' : name?.charAt(0)?.toUpperCase()}
                                    </Text>
                                </View>
                            )}
                            {isTgAmbassador && !deleted_at && (
                                <View style={styles.ambassadorIconContainer}>
                                    <TGAmbassadorLogo />
                                </View>
                            )}
                        </TouchableOpacity>
                        <Text
                            style={[
                                styles.userName,
                                { color: deleted_at ? colors.lightShadeGray : colors.lightBlack },
                            ]}>
                            {deleted_at ? 'Deleted User' : name}
                        </Text>
                        {!deleted_at && (
                            <Text style={styles.ageText}>
                                {request?.number_of_players > 1 && `+${request?.number_of_players - 1} | `}
                                {age}
                            </Text>
                        )}
                    </View>
                    {type.includes('history') && !deleted_at ? (
                        <TouchableOpacity
                            style={styles.viewDetailsButton}
                            onPress={() => {
                                redirectToUserProfile({ userId: userId, navigation, user });
                            }}>
                            <Text style={styles.viewDetailsText}>View Details</Text>
                        </TouchableOpacity>
                    ) : null}
                </View>

                {phone && !deleted_at && (
                    <TouchableOpacity style={styles.iconWrapper} onPress={() => Linking.openURL(`tel:${phone}`)}>
                        <PhoneGreyIcon />
                        <Text style={styles.contactText}>{phone}</Text>
                    </TouchableOpacity>
                )}
                {email && !deleted_at && (
                    <TouchableOpacity style={styles.iconWrapper} onPress={() => Linking.openURL(`mailto:${email}`)}>
                        <EmailGreyIcon />
                        <Text style={styles.contactText}>{email}</Text>
                    </TouchableOpacity>
                )}
            </View>
        </>
    );
};

export default RenderRequesterDetail;

const styles = StyleSheet.create({
    ftrTextContainer: {
        flexDirection: 'row',
        backgroundColor: 'rgba(9, 128, 137, 0.07)',
        paddingHorizontal: Spacing.SCALE_10,
        paddingVertical: Spacing.SCALE_12,
        borderRadius: Size.SIZE_8,
        columnGap: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_10,
    },
    MainTextStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '500',
        color: colors.lightBlack,
        lineHeight: Typography.FONT_SIZE_16,
        width: Size.SIZE_240,
    },
    title: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    box: {
        backgroundColor: colors.whiteRGB,
        paddingVertical: Spacing.SCALE_16,
        paddingHorizontal: Spacing.SCALE_12,
        borderRadius: Spacing.SCALE_12,
        marginTop: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_16,
    },
    infoRow: {
        flexDirection: 'row',
        marginBottom: Spacing.SCALE_16,
    },
    infoContent: {
        marginLeft: Spacing.SCALE_8,
        rowGap: Spacing.SCALE_4,
        flex: 1,
    },
    label: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.darkgray,
        fontFamily: 'Ubuntu-Regular',
    },
    value: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    divider: {
        height: 1,
        backgroundColor: colors.lightgray,
        marginVertical: Spacing.SCALE_8,
    },
    message: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_16,
    },
    accompaniedText: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_22,
        marginTop: Spacing.SCALE_16,
    },
    dateStyle: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    badgeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_10,
    },
    badge: {
        paddingVertical: Spacing.SCALE_5,
        paddingHorizontal: Spacing.SCALE_8,
        borderRadius: Size.SIZE_6,
    },
    founderBadge: {
        backgroundColor: colors.opacityTeal,
    },
    superHostBadge: {
        backgroundColor: colors.badgeLabelBackgroundColor,
        borderColor: colors.darkteal,
    },
    badgeText: {
        fontSize: Typography.FONT_SIZE_10,
        color: colors.darkteal,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Typography.FONT_SIZE_10,
    },
    userInfoContainer: {
        flexDirection: 'row',
        backgroundColor: colors.lightgray,
        borderRadius: Size.SIZE_12,
        padding: Spacing.SCALE_8,
        alignItems: 'center',
        columnGap: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_6,
    },
    profileImageContainer: {
        width: Size.SIZE_38,
        height: Size.SIZE_38,
        alignItems: 'center',
        justifyContent: 'center',
    },
    profileImage: {
        width: '100%',
        height: '100%',
        borderRadius: Size.SIZE_70 / 2,
        backgroundColor: colors.tealRgb,
    },
    profileImageFallback: {
        width: '100%',
        height: '100%',
        borderRadius: Size.SIZE_70 / 2,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
    },
    profileImageText: {
        fontSize: Typography.FONT_SIZE_24,
        color: colors.whiteRGB,
        fontFamily: 'Ubuntu-Medium',
    },
    userDetailsContainer: {
        flex: 1,
        marginLeft: Spacing.SCALE_16,
    },
    userName: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    ageText: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        marginLeft: -Spacing.SCALE_5,
    },
    contactInfo: {
        marginTop: Spacing.SCALE_4,
    },
    contactRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_8,
    },
    contactText: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    iconContainer: {
        width: Size.SIZE_16,
        height: Size.SIZE_16,
        justifyContent: 'center',
        alignItems: 'center',
    },
    iconText: {
        fontSize: Typography.FONT_SIZE_12,
    },
    iconWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_5,
        marginTop: Spacing.SCALE_6,
    },
    additionalInfoValue: {
        fontSize: Size.SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_12,
    },
    crossButton: {
        height: '100%',
        borderRadius: Size.SIZE_8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    customStyle: {
        width: '42%',
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.greenColor,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 0,
    },
    btnTextStyle: {
        fontSize: Size.SIZE_12,
        lineHeight: Size.SIZE_18,
        color: colors.white,
        fontFamily: 'Ubuntu-Regular',
    },
    editTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_18,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    chatButtonStyle: {
        width: '42%',
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: colors.tealRgb,
        flexDirection: 'row',
        columnGap: Spacing.SCALE_7,
    },
    chatButtonText: {
        fontSize: Size.SIZE_12,
        lineHeight: Size.SIZE_18,
        fontFamily: 'Ubuntu-Regular',
    },
    cardButtonWrapper: {
        flexDirection: 'row',
        marginTop: Spacing.SCALE_14,
        justifyContent: 'space-between',
    },
    tealDotStyle: {
        width: 10,
        height: 10,
        borderRadius: 50,
        backgroundColor: colors.tealRgb,
        position: 'absolute',
        right: -2,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
        top: -2,
    },
    bottomBox: {
        position: 'absolute',
        bottom: 0,
        height: Size.SIZE_64,
        backgroundColor: colors.whiteRGB,
        width: '100%',
        paddingHorizontal: Spacing.SCALE_16,
    },
    ambassadorIconContainer: {
        position: 'absolute',
        top: -4,
        right: -4,
        borderRadius: 50,
        padding: 2,
        backgroundColor: colors.whiteRGB,
        borderWidth: 1,
        borderColor: colors.lightgray,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    noHostsText: {
        fontSize: Size.SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    hostIconWrapper: {
        backgroundColor: colors.requestedBtnColor,
        height: Size.SIZE_32,
        width: Size.SIZE_32,
        borderRadius: Size.SIZE_50,
        alignItems: 'center',
        justifyContent: 'center',
    },
    noHostsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
    },
    editButtonWrapper: {
        width: '90%',
        height: Size.SIZE_40,
        backgroundColor: colors.lightgray,
        borderRadius: Size.SIZE_8,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        columnGap: Spacing.SCALE_8,
    },
    viewDetailsButton: {},
    viewDetailsText: {
        fontSize: Size.SIZE_12,
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Regular',
    },
});
