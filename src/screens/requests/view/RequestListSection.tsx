import { FlatList, StyleSheet, Text, View } from 'react-native';
import React from 'react';

import { Size, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import DynamicRequestItemCard from './DynamicRequestItemCard';

interface RequestListSectionProps {
    title: string;
    subtitle: string;
    data: any[];
    setAcceptRequestModal?: (modal: { request: any; onRefresh: () => void } | null) => void;
    acceptRequestModal?: { request: any; onRefresh: () => void } | null;
    type: string;
    setIsVisible?: (visible: boolean) => void;
    isVisible?: boolean;
    isAccepted?: boolean;
    setIsAccepted?: (accepted: boolean) => void;
    setDateRangeModal?: (modal: any) => void;
    dateRangeModal?: any;
    setDeclineRequest?: (request: any) => void;
    declineRequest?: any;
    setReviewModal?: (modal: any) => void;
    reviewModal?: any;
    setIsMyRequest: (isMyRequest: boolean) => void;
    isMyRequest: boolean;
    marginBottom?: boolean;
    setShowAcceptRequestPopupInfo?: (showAcceptRequestPopupInfo: object) => void;
}

const RequestListSection: React.FC<RequestListSectionProps> = ({
    title,
    subtitle,
    data,
    setAcceptRequestModal,
    acceptRequestModal,
    type,
    setIsVisible,
    isVisible,
    isAccepted,
    setIsAccepted,
    setDateRangeModal,
    dateRangeModal,
    setDeclineRequest,
    declineRequest,
    setReviewModal,
    reviewModal,
    setIsMyRequest,
    isMyRequest,
    marginBottom,
    setShowAcceptRequestPopupInfo,
}) => {
    if (data.length === 0) return null;

    return (
        <View style={{ marginBottom: marginBottom ? Size.SIZE_20 : 0 }}>
            <Text style={styles.title}>
                {title} ({data.length})
            </Text>
            <Text style={styles.subtitle}>{subtitle}</Text>
            <FlatList
                data={data}
                renderItem={({ item, index }) => (
                    <DynamicRequestItemCard
                        item={item}
                        title={title}
                        setAcceptRequestModal={setAcceptRequestModal}
                        acceptRequestModal={acceptRequestModal}
                        type={type}
                        setIsVisible={setIsVisible}
                        isVisible={isVisible}
                        isAccepted={isAccepted}
                        setIsAccepted={setIsAccepted}
                        setDateRangeModal={setDateRangeModal}
                        dateRangeModal={dateRangeModal}
                        setDeclineRequest={setDeclineRequest}
                        declineRequest={declineRequest}
                        setReviewModal={setReviewModal}
                        reviewModal={reviewModal}
                        setIsMyRequest={setIsMyRequest}
                        isMyRequest={isMyRequest}
                        setShowAcceptRequestPopupInfo={setShowAcceptRequestPopupInfo}
                    />
                )}
                keyExtractor={(item) => item.id?.toString()}
                showsVerticalScrollIndicator={false}
                scrollEnabled={false}
            />
        </View>
    );
};

export default RequestListSection;

const styles = StyleSheet.create({
    title: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
        marginTop: Size.SIZE_12,
        marginBottom: Size.SIZE_4,
    },
    subtitle: {
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_16,
        color: colors.greyRgb,
        fontFamily: 'Ubuntu-Regular',
        marginBottom: Size.SIZE_12,
    },
});
