import { StyleSheet, Text, View } from 'react-native';
import React, { useCallback } from 'react';

import { REQUEST_SENT_TO_MEMBERS_MEETING_THE_FOLLOWING_CRITERIA, REQUESTED } from '../../../utils/constants/strings';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';

interface RenderAdditionalInfoProps {
    hostData: any;
    type: string;
    request: any;
    gameInfo: any;
    isMyRequest: boolean;
    userInfo: any;
}

const RenderAdditionalInfo = ({
    hostData,
    type,
    request,
    gameInfo,
    isMyRequest,
    userInfo,
}: RenderAdditionalInfoProps) => {
    const getModifiedString = useCallback((data: string[] | undefined) => {
        if (!data?.length) return '';
        return data.map((item) => item.charAt(0).toUpperCase() + item.slice(1)).join(',');
    }, []);

    return (
        <>
            <Text style={styles.title}>Additional Info</Text>
            <View style={[styles.box, { paddingVertical: Spacing.SCALE_12 }]}>
                {type.includes('requested') && (
                    <>
                        <Text style={styles.label}>Total Hosts</Text>
                        <Text style={[styles.value, styles.additionalInfoValue]}>{gameInfo?.number_of_hosts}</Text>
                        <Text style={styles.label}>Host(s) Declined</Text>
                        <Text style={[styles.value, styles.additionalInfoValue]}>
                            {request?.hosts_declined?.length}
                        </Text>
                        <Text style={styles.requestSentText}>
                            {REQUEST_SENT_TO_MEMBERS_MEETING_THE_FOLLOWING_CRITERIA}
                        </Text>
                    </>
                )}
                <Text style={styles.label}>Golf Index</Text>
                <Text style={[styles.value, styles.additionalInfoValue]}>
                    {gameInfo?.handicap
                        ? gameInfo?.handicap?.length === 3
                            ? 'All'
                            : getModifiedString(gameInfo?.handicap)
                        : userInfo?.handicap}
                </Text>
                <Text style={styles.label}>English Fluency</Text>
                <Text style={[styles.value, styles.additionalInfoValue]}>
                    {isMyRequest
                        ? gameInfo?.english_fluency?.length === 4
                            ? 'All'
                            : getModifiedString(gameInfo?.english_fluency)
                        : userInfo?.english_fluency}
                </Text>
                {type.includes('requested') && (
                    <>
                        <Text style={styles.label}>Play as a couple</Text>
                        <Text style={[styles.value, styles.additionalInfoValue]}>
                            {gameInfo?.playAsCouple ? 'Yes' : 'No'}
                        </Text>
                        <Text style={[styles.label, { marginBottom: 0 }]}>Age</Text>
                        <Text style={[styles.value, styles.additionalInfoValue]}>
                            {type.includes('received')
                                ? userInfo?.age
                                : gameInfo?.ageGroup
                                ? 'All'
                                : `${gameInfo?.minAgeGroup} Years - ${gameInfo?.maxAgeGroup} Years`}
                        </Text>
                    </>
                )}
            </View>
        </>
    );
};

export default RenderAdditionalInfo;

const styles = StyleSheet.create({
    title: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    box: {
        backgroundColor: colors.whiteRGB,
        paddingVertical: Spacing.SCALE_16,
        paddingHorizontal: Spacing.SCALE_12,
        borderRadius: Spacing.SCALE_12,
        marginTop: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_16,
    },
    label: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.darkgray,
        fontFamily: 'Ubuntu-Regular',
    },
    requestSentText: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.systemMessageText,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_18,
        marginBottom: Spacing.SCALE_16,
    },
    value: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    additionalInfoValue: {
        fontSize: Size.SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_12,
    },
});
