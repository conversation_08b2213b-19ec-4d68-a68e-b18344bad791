import { StatusBar, StyleSheet, View } from 'react-native';
import React, { useCallback, useContext, useEffect, useState } from 'react';

import ProfileHeader from '../../../../../components/layout/ProfileHeader';
import { colors } from '../../../../../theme/theme';
import { Spacing, Typography } from '../../../../../utils/responsiveUI';
import RequestHistoryListing from '../components/RequestHistoryListing';
import { GlobalContext } from '../../../../../context/contextApi';
import RequestScreenSkelton from '../../RequestScreenSkelton';
import RequestHistoryFilter from '../../../RequestHistoryFilter';
import StreamChatSearchInput from '../../../../my-TG-friends/view/StreamChatSearchInput';
import { getAllHistoryRequest } from '../../../action/getHistoryRequest';
import { AuthContext } from '../../../../../context/AuthContext';
import { RootStackParamList } from '../../../../../interface/type';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import showToast from '../../../../../components/toast/CustomToast';
import debounce from '../../../../my-TG-Stream-Chat/action';
import { getAllRequestHostsData } from '../../../action/getAllRequestHostsData';
import { useIsFocused } from '@react-navigation/native';
import { StreamChatContext } from '../../../../../context/StreamChatContext';
import { getStreamRequestChannel } from '../../../action/getRequestUnReadChannels';
import { fetcher } from '../../../../../service/fetcher';
import { GET_UNREAD_MESSAGE_STATUS } from '../../../../../service/EndPoint';

interface HistoryScreenState {
    historyData: any[];
    totalPages: number;
    totalCount: number;
    paginationLoader: boolean;
    limit: number;
    page: number;
    showFilterOption: boolean;
    triggerSearch: string;
    isFilterApplied: boolean;
}

interface HistoryScreenPayload {
    userId: string;
    limit: number;
    page: number;
    search?: string;
    filter?: string;
}

const RequestHistoryScreen = ({ navigation }: { navigation: NativeStackNavigationProp<RootStackParamList> }) => {
    const { state, actions } = useContext(GlobalContext);
    const isFocused = useIsFocused();
    const { user } = useContext(AuthContext);
    const { client, setChannel } = useContext(StreamChatContext);
    const { appSkeltonLoader, allRequestHostsData, unreadChannels, unReadMessageStatus } = state;
    const [filter, setFilter] = useState('all');
    const [searchData, setSearchData] = useState('');
    const [historyScreenState, setHistoryScreenState] = useState<HistoryScreenState>({
        historyData: [],
        totalPages: 0,
        totalCount: 0,
        paginationLoader: false,
        limit: 10,
        page: 1,
        showFilterOption: false,
        triggerSearch: '',
        isFilterApplied: false,
    });

    // Stream chat message listener
    useEffect(() => {
        const messageNewEventListener = client.on('message.new', () => {
            getStreamRequestChannel(client, user, actions, state);
            fetchUnreadMessage();
        });
        return () => messageNewEventListener?.unsubscribe();
    }, [client]);

    // Handle request hosts data
    useEffect(() => {
        fetchUnreadMessage();
        getStreamRequestChannel(client, user, actions, state);
        if (historyScreenState.historyData.length) {
            const requestIds = historyScreenState.historyData.map((request: any) => request.request_id);
            getAllRequestHostsData({
                userId: user?.id,
                requestIds: requestIds,
            }).then((res) => {
                if (res.status) {
                    actions.setAllRequestHostsData([...allRequestHostsData, ...res.data]);
                }
            });
        }
    }, [historyScreenState.historyData, user?.id, actions, historyScreenState.page, isFocused]);

    useEffect(() => {
        actions.setAppSkeltonLoader(true);
        getHistoryRequest();
    }, [historyScreenState.triggerSearch, isFocused]);

    useEffect(() => {
        updateTriggerSearchStringUpdate(searchData);
    }, [searchData]);

    const updateTriggerSearchStringUpdate = useCallback(
        debounce((value: string) => {
            setHistoryScreenState((prev) => ({
                ...prev,
                triggerSearch: value,
            }));
        }, 300),
        [],
    );

    //Fetch unread message red dot value for all tabs
    const fetchUnreadMessage = () => {
        fetcher({
            endpoint: GET_UNREAD_MESSAGE_STATUS,
            method: 'POST',
            body: {
                userId: user?.id,
                requestIds: unreadChannels,
            },
        }).then((res) => {
            if (res?.status) {
                actions?.setUnreadMessageStatus(res?.response);
            } else {
                showToast({});
            }
        });
    };

    const getHistoryRequest = async (isRefresh: boolean = false) => {
        const payload: HistoryScreenPayload = {
            userId: user.id,
            limit: historyScreenState.limit,
            page: isRefresh || searchData ? 1 : historyScreenState.page,
        };
        if (searchData) {
            payload.search = searchData;
        }
        if (filter) {
            payload.filter = filter;
        }
        // [all, cancelled, completed, declined, fulfilled]
        try {
            const res = await getAllHistoryRequest(payload);
            if (res.status) {
                setHistoryScreenState((prev) => ({
                    ...prev,
                    paginationLoader: false,
                    page: isRefresh || searchData ? 1 : prev.page,
                    historyData:
                        isRefresh || searchData || historyScreenState.page === 1
                            ? res.data
                            : [...prev.historyData, ...res.data],
                    totalPages: res.totalPages,
                    totalCount: res.totalCount,
                }));
                actions.setAppSkeltonLoader(false);
            }
        } catch (error) {
            showToast({
                message: 'Something went wrong',
                type: 'error',
            });
        }
    };

    const handleItemPress = (item: any) => {
        navigation.navigate('RequestDetailScreen', {
            request: item,
            type: item.type === 'received' ? 'received-history' : 'requested-history',
            isMyRequest: item.requestor_user_id === user.id,
            requestDetailType: item.type === 'received' ? 'received-history' : 'requested-history',
        });
    };

    const handleChatPress = (item: any) => {
        setChannel({});
        if (
            item?.host_user_id &&
            (item?.requestor_user_id || user?.id) &&
            item?.id !== (item?.requestor_user_id || user?.id)
        ) {
            navigation.navigate('ChatDetails', {
                type: item?.requestor_user_id === user?.id ? 'requested-history' : 'received-history',
                request_id: item?.request_id,
                game_id: item?.game_id,
                streamChannelId: item?.stream_channel_id,
                requestor_user_id: item?.requestor_user_id || user?.id,
                requestor_full_name: item?.requestor_full_name,
                host_user_id: item?.host_user_id,
                has_messages: item?.has_messages,
                isDeletedUser: item?.requestor_username.includes('deleted'),
            });
        } else {
            showToast({});
        }
    };

    const handleRefresh = () => {
        setHistoryScreenState((prev) => ({
            ...prev,
            page: 1,
            historyData: [],
        }));
        actions.setAppSkeltonLoader(true);
        getHistoryRequest(true);
    };

    const handleFilterPress = () => {
        setHistoryScreenState((prev) => ({
            ...prev,
            showFilterOption: true,
        }));
    };

    const handleEndReached = () => {
        if (historyScreenState.paginationLoader) return;
        if (!historyScreenState.historyData.length) return;
        if (historyScreenState.page < historyScreenState.totalPages) {
            setHistoryScreenState((prev) => ({
                ...prev,
                paginationLoader: true,
                page: prev.page + 1,
            }));
            getHistoryRequest();
        }
    };

    const handleDelete = (item: any) => {
        navigation.navigate('RequestConfirmScreen', {
            type: 'history',
            game_id: item.game_id,
            requestor_full_name: item.requestor_full_name,
            request: item,
            callBack: () => {
                getHistoryRequest();
            },
        });
    };

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.whiteRGB} />
            <View style={styles.container}>
                <ProfileHeader
                    title={'History'}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                    containerStyle={{
                        backgroundColor: colors.whiteRGB,
                        paddingBottom: Spacing.SCALE_10,
                    }}
                    iconShow={'History'}
                    onClick={handleFilterPress}
                    filterTealDot={historyScreenState.isFilterApplied}
                />
                <View style={styles.body}>
                    <StreamChatSearchInput
                        searchState={[searchData, setSearchData]}
                        searchBoxWrapperStyle={styles.streamInputWrapper}
                        placeholder="Search Club, Requester, Host or ID"
                        searchBoxStyle={styles.searchBox}
                    />
                    {appSkeltonLoader ? (
                        <RequestScreenSkelton screen={'History'} />
                    ) : (
                        <RequestHistoryListing
                            data={historyScreenState.historyData}
                            onItemPress={handleItemPress}
                            onChatPress={handleChatPress}
                            onRefresh={handleRefresh}
                            refreshing={appSkeltonLoader}
                            onEndReached={handleEndReached}
                            limit={historyScreenState.limit}
                            paginationLoader={historyScreenState.paginationLoader}
                            setPaginationLoader={setHistoryScreenState}
                            handleDelete={handleDelete}
                            navigation={navigation}
                            isLoading={appSkeltonLoader}
                            searchData={searchData}
                        />
                    )}
                </View>
            </View>
            {historyScreenState.showFilterOption && (
                <RequestHistoryFilter
                    popupState={[historyScreenState.showFilterOption, setHistoryScreenState]}
                    filterState={[filter, setFilter]}
                    getHistory={handleRefresh}
                />
            )}
        </>
    );
};

export default React.memo(RequestHistoryScreen);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.whiteRGB,
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
    body: {
        flex: 1,
        paddingHorizontal: Spacing.SCALE_16,
        backgroundColor: colors.screenBG,
    },
    streamInputWrapper: {
        marginVertical: 0,
        width: '100%',
        alignSelf: 'center',
        marginBottom: Spacing.SCALE_12,
        borderRadius: Spacing.SCALE_10,
    },
    searchBox: {
        fontWeight: '400',
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: colors.darkgray,
    },
});
