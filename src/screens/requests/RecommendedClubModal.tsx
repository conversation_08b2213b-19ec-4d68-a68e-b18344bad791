import { FlatList, Platform, SafeAreaView, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { Modal, TouchableWithoutFeedback, View } from 'react-native';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
//@ts-ignore
import Cross from '../../assets/svg/cross.svg';
//@ts-ignore
import MapMarkerGray from '../../assets/svg/MapMarkerGray.svg';
//@ts-ignore
import GolfPointTealIcon from '../../assets/svg/GolfPointTealIcon.svg';
//@ts-ignore
import CreateRequest from '../../assets/images/createRequestTeal.svg';
import { useContext } from 'react';
import { AuthContext } from '../../context/AuthContext';
import getTierName from '../../utils/helpers/getTierName';

interface RecommendedClubModalProps {
    popupState: [boolean, (value: boolean) => void];
    requestedClub: any;
    recommendedClubs: any[];
    setIsRecommendedClubRequest: (value: boolean) => void;
    handleCreateRequest: (arg?: any) => void;
    setRequestedClub: (club: any) => void;
}

const RecommendedClubModal = ({
    popupState = [false, () => {}],
    requestedClub = {},
    recommendedClubs = [],
    setIsRecommendedClubRequest = () => {},
    handleCreateRequest = () => {},
    setRequestedClub = () => {},
}: RecommendedClubModalProps) => {
    const { user } = useContext(AuthContext);
    const [showPopup, setShowPopup] = popupState;

    return (
        <Modal
            //@ts-ignore
            animationIn="fadeInUp"
            transparent={true}
            isVisible={showPopup}
            customBackdrop={
                <TouchableWithoutFeedback style={{ flex: 1 }}>
                    <View
                        style={{
                            flex: 1,
                            backgroundColor: 'rgba(0, 0, 0, 0.3)',
                        }}
                    />
                </TouchableWithoutFeedback>
            }
            backdropTransitionOutTiming={1}
            style={styles.modalStyle}>
            <SafeAreaView style={{ flex: 1 }}>
                <View
                    style={{
                        flex: 1,
                        backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    }}>
                    <View style={styles.popupWrapper}>
                        <View style={styles.headerWrapper}>
                            <TouchableOpacity
                                style={styles.crossIconWrapper}
                                onPress={() => {
                                    setShowPopup(false);
                                    setRequestedClub({});
                                }}>
                                <Cross />
                            </TouchableOpacity>
                        </View>
                        <View style={styles.golfIconWrapper}>
                            <GolfPointTealIcon height={34} width={34} />
                        </View>

                        <View style={{ alignItems: 'center' }}>
                            <View
                                style={{
                                    alignContent: 'center',
                                    justifyContent: 'center',
                                }}>
                                <Text style={styles.headerText}>Club Recommendations</Text>

                                <Text style={styles.bodyText}>
                                    Thank you for your game request at{' '}
                                    <Text style={{ fontWeight: Platform.OS === 'ios' ? '500' : '600' }}>
                                        {requestedClub?.name}
                                    </Text>
                                    . While it's being processed, we'd like to introduce you to nearby golf clubs with
                                    higher request acceptance rates.
                                </Text>

                                <Text style={styles.bodyText_1}>Here are some recommended golf club(s):</Text>

                                <View
                                    style={[
                                        styles.listContainer,
                                        recommendedClubs?.length > 1
                                            ? { maxHeight: Platform.OS == 'ios' ? Size.SIZE_180 : Size.SIZE_200 }
                                            : {},
                                    ]}>
                                    <FlatList
                                        showsVerticalScrollIndicator={false}
                                        data={recommendedClubs}
                                        keyExtractor={(item) => item.id.toString()}
                                        renderItem={({ item, index }) => (
                                            <View
                                                style={[
                                                    styles.clubCard,
                                                    index + 1 === recommendedClubs?.length
                                                        ? { marginBottom: 0 }
                                                        : { marginBottom: Spacing.SCALE_10 },
                                                ]}>
                                                <View
                                                    style={{
                                                        flexDirection: 'column',
                                                    }}>
                                                    <View style={styles.tagContainer}>
                                                        <Text style={styles.tag}>
                                                            {getTierName(item?.lowest_visible_tier)?.toUpperCase()}
                                                        </Text>
                                                    </View>

                                                    <View style={styles.clubInfo}>
                                                        <Text style={styles.clubName}>{item.name}</Text>
                                                    </View>

                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                            flex: 1,
                                                            paddingRight: 10,
                                                            width: '95%',
                                                        }}>
                                                        <View
                                                            style={{
                                                                paddingTop: Spacing.SCALE_3,
                                                            }}>
                                                            <MapMarkerGray />
                                                        </View>
                                                        <Text style={styles.clubAddress}>
                                                            {item?.address?.length > 35
                                                                ? item?.address.slice(0, 35) + '...'
                                                                : item.address}
                                                        </Text>
                                                    </View>
                                                </View>

                                                <TouchableOpacity
                                                    style={{}}
                                                    onPress={() => {
                                                        setIsRecommendedClubRequest(true);
                                                        handleCreateRequest({
                                                            isRecommendedClubRequest: true,
                                                            club: { id: item?.id, name: item?.name },
                                                        });
                                                        setShowPopup(false);
                                                    }}>
                                                    <CreateRequest height={32} width={32} />
                                                </TouchableOpacity>
                                            </View>
                                        )}
                                    />
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </SafeAreaView>
        </Modal>
    );
};

export default RecommendedClubModal;

const styles = StyleSheet.create({
    modalStyle: {
        paddingHorizontal: 0,
        marginHorizontal: 0,
        paddingVertical: 0,
        marginVertical: 0,
    },
    popupWrapper: {
        width: '100%',
        minHeight: '90%',
        position: 'absolute',
        bottom: -15,
        backgroundColor: colors.whiteRGB,
        borderTopRightRadius: Size.SIZE_18,
        borderTopLeftRadius: Size.SIZE_18,
        paddingBottom: Platform?.OS === 'ios' ? Spacing.SCALE_30 : Spacing.SCALE_40,
        paddingHorizontal: Spacing.SCALE_16,
    },
    headerWrapper: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingTop: Spacing.SCALE_16,
    },
    crossIconWrapper: {
        position: 'absolute',
        top: 16,
    },
    golfIconWrapper: {
        width: 72,
        height: 72,
        backgroundColor: colors.lightTeal,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 50,
        alignSelf: 'center',
        marginTop: Spacing.SCALE_8,
    },
    headerText: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_24,
        lineHeight: Size.SIZE_32,
        textAlign: 'center',
        fontWeight: '500',
        color: colors.lightBlack,
        paddingTop: Spacing.SCALE_20,
        paddingBottom: Spacing.SCALE_12,
    },
    bodyText: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_20,
        textAlign: 'center',
        fontWeight: '400',
        color: colors.lightBlack,
        paddingBottom: Spacing.SCALE_20,
        paddingHorizontal: Spacing.SCALE_30,
    },
    bodyText_1: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_20,
        textAlign: 'center',
        fontWeight: '400',
        color: colors.lightBlack,
        paddingBottom: Spacing.SCALE_16,
    },
    listContainer: {
        flex: 1,
    },
    clubCard: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: Spacing.SCALE_12,
        backgroundColor: colors.white,
        borderRadius: 10,
        borderWidth: 1.5,
        borderColor: colors.lightGrey,
    },
    tagContainer: {
        backgroundColor: 'rgba(9, 128, 137, 0.1)',
        borderRadius: 6,
        paddingVertical: Spacing.SCALE_6,
        paddingHorizontal: Spacing.SCALE_8,
        alignSelf: 'flex-start',
        marginBottom: Spacing.SCALE_8,
    },
    tag: {
        fontSize: Typography.FONT_SIZE_10,
        color: colors.darkteal,
        fontWeight: '700',
    },
    clubInfo: {
        flex: 1,
    },
    clubName: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        fontWeight: '500',
        color: colors.lightBlack,
        marginBottom: Spacing.SCALE_8,
    },
    clubAddress: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_18,
        fontWeight: '400',
        color: colors.fadeBlack,
        paddingLeft: Spacing.SCALE_4,
    },
});
