import { allNotificationsV3 } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';

export const getNotifications = async (userId: string) => {
    return await fetcher({
        endpoint: allNotificationsV3,
        method: 'POST',
        body: {
            userId: userId,
            lastCreatedAt: null,
            limit: 100,
            isRead: false,
        },
    }).then((response) => {
        return response;
    });
};
