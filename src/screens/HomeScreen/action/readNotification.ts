import { READ_NOTIFICATION } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';

export const readNotifications = async (userId: string, notificationId: string) => {
    return await fetcher({
        endpoint: READ_NOTIFICATION,
        method: 'POST',
        body: {
            userId: userId,
            notificationId: notificationId
        },
    }).then((response) => {
        return response;
    });
};
