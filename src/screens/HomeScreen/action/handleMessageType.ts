const messageType = (messageType = '') => {
    const type = messageType.toLowerCase();

    const categories = {
        'My TG': [
            'friend-request',
            'join-group-request',
            'poll_ended',
            'my-tg-groups',
            'tga-friend-connected',
            'friend-joined-my-tg-group',
            'new-club-user',
            'friend-favorite-club',
            'mutual-friends-connected',
            'couple-joined',
            'female-member-joined',
            'my-tg-group',
        ],
        Game: [
            'request',
            'my-tg-offer',
            'requester',
            'host-game-completed',
            'game',
            'request-chat',
            'tier-revision',
            'game-reminder',
            'tier-revision',
        ],
        Account: [
            'referral-joined',
            'admin-clubs-edit',
            'admin-profile-edit',
            'faq',
            'account-activated',
            'ngv-update',
            'update-email',
            'unmute-user-club',
        ],
        Events: ['event'],
        Clubs: ['club-update', 'muted-club-reminder', 'club-unmuted'],
        Benefit: ['benefit'],
        Pegboard: ['pegboard'],
        'TG Admin': ['admin-notification'],
    };

    return Object.entries(categories).find(([, keywords]) => keywords.some((keyword) => type.includes(keyword)))?.[0];
};

export default messageType;
