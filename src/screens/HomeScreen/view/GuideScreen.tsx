import { Platform, SafeAreaView, StatusBar, StyleSheet, View } from 'react-native';
import React, { useContext } from 'react';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { colors } from '../../../theme/theme';
import GuideProfilePart from './guide/GuideProfilePart';
import { GlobalContext } from '../../../context/contextApi';
import GuideNotificationPart from './guide/GuideNotificationPart';
import GuideBottomSheet from './guide/GuideBottomSheet';
import { Spacing } from '../../../utils/responsiveUI';

const GuideScreen = () => {
    const { state } = useContext(GlobalContext);
    const insets = useSafeAreaInsets();

    const guideOption = (key: number) => {
        switch (key) {
            case 1:
                return <GuideProfilePart />;
            case 2:
                return <GuideNotificationPart />;
            case 3:
                return <GuideBottomSheet />;
            default:
                return null;
        }
    };

    return (
        <>
            <StatusBar barStyle="dark-content" />
            <View
                style={[
                    styles.container,
                    { paddingTop: Platform.OS === 'ios' ? Spacing.SCALE_40 : insets.top + Spacing.SCALE_10 },
                ]}>
                {guideOption(state.guideStep)}
            </View>
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    animatedView: {
        flex: 1,
    },
    bottomSheetContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        width: '100%',
    },
});

export default GuideScreen;
