import { Image, ImageBackground, StyleSheet, Text, View } from 'react-native';
import React, { useContext } from 'react';

import GuideBackdrop from './GuideBackdrop';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';
import { AuthContext } from '../../../../context/AuthContext';
import { GUIDE_DESCRIPTION_1, GUIDE_HEADER_1 } from '../../../../utils/constants/strings';
import { ArrowUp } from '../../../../assets/svg/index';

const GuideProfilePart = () => {
    const { user } = useContext(AuthContext);
    return (
        <>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    columnGap: Spacing.SCALE_12,
                    padding: Spacing.SCALE_9,
                }}>
                <ImageBackground
                    source={require('../../../../assets/images/profileBG.png')}
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        columnGap: Spacing.SCALE_12,
                        borderRadius: 60,
                        paddingHorizontal: Spacing.SCALE_5,
                        paddingVertical: Spacing.SCALE_4,
                        paddingRight: Spacing.SCALE_10,
                    }}
                    imageStyle={{
                        borderRadius: 60,
                    }}>
                    <View style={styles.profileIcon}>
                        {user?.profilePhoto ? (
                            <Image source={{ uri: user?.profilePhoto }} style={styles.profileIcon} />
                        ) : (
                            <Text
                                style={{
                                    fontSize: Typography.FONT_SIZE_18,
                                    color: colors.tealRgb,
                                    fontFamily: 'Ubuntu-Medium',
                                }}>
                                {user?.full_name[0]}
                            </Text>
                        )}
                    </View>
                    <View>
                        <Text style={styles.text}>{user?.full_name}</Text>
                    </View>
                </ImageBackground>
            </View>
            <View style={{ marginLeft: Spacing.SCALE_67 }}>
                <ArrowUp />
            </View>
            <GuideBackdrop title={GUIDE_HEADER_1} description={GUIDE_DESCRIPTION_1} />
        </>
    );
};

export default GuideProfilePart;

const styles = StyleSheet.create({
    safeAreaViewStyle: {
        backgroundColor: colors.whiteRGB,
    },
    container: {
        backgroundColor: colors.whiteRGB,
        padding: Spacing.SCALE_16,
    },
    welcomeBackStyle: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.greyRgb,
        lineHeight: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
    },
    text: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.whiteRGB,
        lineHeight: Typography.FONT_SIZE_18,
        fontFamily: 'Ubuntu-Medium',
    },
    profileIcon: {
        width: Size.SIZE_34,
        height: Size.SIZE_34,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.whiteRGB,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
