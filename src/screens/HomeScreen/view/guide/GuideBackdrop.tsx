import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext } from 'react';
import FastImage from 'react-native-fast-image';

import NotificationSwipe from '../../../../assets/images/NotificationSwipeIcon.png';
import BottomSheetDrag from '../../../../assets/images/BottomSheetDragIcon.png';
import { GlobalContext } from '../../../../context/contextApi';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';
import { Pressable } from 'react-native-gesture-handler';
import useClient from '../../../../hooks/useClient';
import { UPDATE_USER } from '../../../../graphql/mutations/user';
import { AuthContext } from '../../../../context/AuthContext';

interface GuideBackdropProps {
    title: string;
    description: string;
    showNotificationIcon?: boolean;
    showBottomSheetIcon?: boolean;
}

const GuideBackdrop: React.FC<GuideBackdropProps> = ({
    title,
    description,
    showNotificationIcon = false,
    showBottomSheetIcon = false,
}) => {
    const { state, actions } = useContext(GlobalContext);
    const { user } = useContext(AuthContext);
    const client = useClient();
    const updateUser = async () => {
        await client.request(UPDATE_USER, {
            user_id: user?.id,
            user: {
                is_tutorial_viewed: true,
            },
        });
    };
    return (
        <View style={styles.container}>
            {showNotificationIcon && (
                <FastImage
                    source={NotificationSwipe}
                    resizeMode="contain"
                    style={{
                        width: Size.SIZE_50,
                        height: Size.SIZE_50,
                        alignSelf: 'flex-start',
                        marginBottom: Spacing.SCALE_12,
                    }}
                />
            )}
            {showBottomSheetIcon && (
                <FastImage
                    source={BottomSheetDrag}
                    resizeMode="contain"
                    style={{
                        width: Size.SIZE_50,
                        height: Size.SIZE_50,
                        alignSelf: 'flex-start',
                        marginBottom: Spacing.SCALE_12,
                    }}
                />
            )}
            {/* <NotificationSwipeIcon/> */}
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.description}>{description}</Text>
            <View style={styles.footer}>
                <Text style={styles.stepText}>{state.guideStep} of 3</Text>
                <View style={styles.buttonContainer}>
                    {state.guideStep !== 3 && (
                        <TouchableOpacity
                            style={styles.skipButton}
                            onPress={() => {
                                actions.setStartGuide(false);
                                updateUser();
                            }}>
                            <Text style={styles.skipText}>Skip</Text>
                        </TouchableOpacity>
                    )}
                    <Pressable
                        style={styles.nextButton}
                        onPress={() => {
                            if (state.guideStep === 3) {
                                actions.setStartGuide(false);
                                updateUser();
                            } else {
                                actions.setGuideStep(state.guideStep + 1);
                            }
                        }}>
                        <Text style={styles.nextText}>{`${state.guideStep === 3 ? 'Done' : 'Next'}`}</Text>
                    </Pressable>
                </View>
            </View>
        </View>
    );
};

export default GuideBackdrop;

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.whiteRGB,
        padding: Spacing.SCALE_16,
        borderRadius: Size.SIZE_8,
        shadowColor: '#000',
        shadowOpacity: 0.1,
        shadowOffset: { width: 0, height: 4 },
        shadowRadius: 5,
        elevation: 5,
        width: '90%',
        alignSelf: 'center',
    },
    title: {
        fontSize: Typography.FONT_SIZE_16,
        marginBottom: Spacing.SCALE_8,
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
    },
    description: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.greyRgb,
        fontFamily: 'Ubuntu-Regular',
        marginBottom: Spacing.SCALE_16,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    stepText: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.darkGreyRgba,
        fontFamily: 'Ubuntu-Regular',
    },
    buttonContainer: {
        flexDirection: 'row',
    },
    skipButton: {
        backgroundColor: '#E0E0E0',
        paddingVertical: 8,
        paddingHorizontal: 20,
        borderRadius: 8,
        marginRight: 10,
    },
    nextButton: {
        backgroundColor: '#007C89',
        paddingVertical: 8,
        paddingHorizontal: 20,
        borderRadius: 8,
    },
    skipText: {
        color: '#333',
        fontSize: 14,
    },
    nextText: {
        color: 'white',
        fontSize: 14,
    },
});
