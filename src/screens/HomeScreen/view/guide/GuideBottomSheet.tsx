import { View, StyleSheet, Platform } from 'react-native';
import React, { useEffect } from 'react';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import FastImage from 'react-native-fast-image';

import GuideBackdrop from './GuideBackdrop';
import { GUIDE_DESCRIPTION_3, GUIDE_HEADER_3 } from '../../../../utils/constants/strings';
import { Size } from '../../../../utils/responsiveUI';
import { ArrowDown } from '../../../../assets/svg';

const GuideBottomSheet = () => {
    const bottomSheetOpacity = useSharedValue(0);
    const position = useSharedValue(300);
    useEffect(() => {
        setTimeout(() => {
            bottomSheetOpacity.value = withTiming(1, { duration: 500 });
            position.value = withTiming(0, { duration: 500 });
        }, 500);
    }, []);
    const bottomSheetAnimatedStyle = useAnimatedStyle(() => ({ opacity: bottomSheetOpacity.value }));
    const bottomSheetPositionStyle = useAnimatedStyle(() => ({
        transform: [{ translateY: position.value }],
    }));

    return (
        <View style={styles.container}>
            <View style={styles.content}>
                <Animated.View style={bottomSheetPositionStyle}>
                    <GuideBackdrop title={GUIDE_HEADER_3} description={GUIDE_DESCRIPTION_3} showBottomSheetIcon />
                </Animated.View>
                <Animated.View style={{ ...bottomSheetAnimatedStyle }}>
                    <View style={{ alignItems: 'center' }}>
                        <ArrowDown />
                    </View>
                    <FastImage
                        source={require('../../../../assets/images/bottomSheetNavBar.png')}
                        style={[
                            {
                                width: Platform.OS === 'ios' ? Size.SIZE_360 : Size.SIZE_360,
                                height: Platform.OS === 'ios' ? Size.SIZE_270 : Size.SIZE_280,
                            },
                        ]}
                    />
                </Animated.View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        // justifyContent: 'flex-end', // Moves content to the bottom
        // alignItems: 'center', // Centers the content horizontally
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
    },
    content: {
        alignItems: 'center',
        paddingBottom: 0, // Optional padding from the bottom
    },
});

export default GuideBottomSheet;
