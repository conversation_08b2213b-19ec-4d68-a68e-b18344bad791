import { Dimensions, StyleSheet, Text, View } from 'react-native';
import React, { useEffect } from 'react';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

import { colors } from '../../../../theme/theme';
import GuideBackdrop from './GuideBackdrop';
import { GUIDE_DESCRIPTION_2, GUIDE_HEADER_2 } from '../../../../utils/constants/strings';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { ArrowUp, ArrowDown, NotificationCards } from '../../../../assets/svg';

const GuideNotificationPart = () => {
    const translateY = useSharedValue(-420);
    const opacity = useSharedValue(0);
    const cardOpacity = useSharedValue(0);
    useEffect(() => {
        setTimeout(() => {
            translateY.value = withTiming(0, { duration: 500 });
            opacity.value = withTiming(1, { duration: 600 });
        }, 500);
    }, []);
    useEffect(() => {
        cardOpacity.value = withTiming(1, { duration: 300 });
    }, []);
    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ translateY: translateY.value }],
    }));
    const animatedOpacity = useAnimatedStyle(() => ({
        opacity: opacity.value,
    }));
    const animatedCardOpacity = useAnimatedStyle(() => ({
        opacity: cardOpacity.value,
    }));

    return (
        <>
            {Dimensions.get('window').height < 700 && (
                <View style={{ marginTop: Spacing.SCALE_40 }}>
                    <GuideBackdrop title={GUIDE_HEADER_2} description={GUIDE_DESCRIPTION_2} showNotificationIcon />
                    <View style={{ alignItems: 'center' }}>
                        <ArrowDown />
                    </View>
                </View>
            )}
            <Animated.View
                style={[
                    styles.container,
                    {
                        marginTop: Dimensions.get('window').height < 700 ? Spacing.SCALE_10 : Spacing.SCALE_250,
                    },
                    animatedOpacity,
                ]}>
                <NotificationCards width={Size.SIZE_340} />
            </Animated.View>
            {Dimensions.get('window').height > 700 && (
                <>
                    <Animated.View style={[{ alignItems: 'center', marginTop: Spacing.SCALE_10 }, animatedOpacity]}>
                        <ArrowUp />
                    </Animated.View>
                    <Animated.View style={[animatedStyle, animatedCardOpacity]}>
                        <GuideBackdrop title={GUIDE_HEADER_2} description={GUIDE_DESCRIPTION_2} showNotificationIcon />
                    </Animated.View>
                </>
            )}
        </>
    );
};

export default GuideNotificationPart;

const styles = StyleSheet.create({
    container: {
        borderRadius: Size.SIZE_12,
        width: '100%',
        alignSelf: 'center',
        marginTop: Spacing.SCALE_250,
        alignItems: 'center',
    },
    text: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.dark_charcoal,
        lineHeight: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_12,
    },
});
