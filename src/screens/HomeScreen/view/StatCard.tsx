import React, { memo, useContext } from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native';

import { PeopleSvg, ClubIcon, RequestIcon, RequestAcceptedIcon } from '../../../assets/svg/index';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { GlobalContext } from '../../../context/contextApi';
import { StatItem } from '../../../interface';

const StatCard: React.FC<{ item: StatItem }> = ({ item }) => {
    const { value, label } = item;
    return (
        <TouchableOpacity style={styles.card} disabled={true}>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    columnGap: Spacing.SCALE_8,
                    marginBottom: Spacing.SCALE_6,
                }}>
                {item.id === '1' ? (
                    <PeopleSvg />
                ) : item.id === '2' ? (
                    <ClubIcon />
                ) : item.id === '3' ? (
                    <RequestIcon />
                ) : (
                    <RequestAcceptedIcon />
                )}
                <Text style={styles.value}>{value}</Text>
            </View>
            <Text style={styles.label}>{label}</Text>
        </TouchableOpacity>
    );
};

const StatsGrid: React.FC = () => {
    const { state } = useContext(GlobalContext);
    return (
        <View>
            <FlatList
                data={state?.stats}
                numColumns={2}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => <StatCard item={item} />}
                columnWrapperStyle={styles.row}
                contentContainerStyle={styles.container}
                scrollEnabled={false}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: Spacing.SCALE_10,
    },
    row: {
        justifyContent: 'space-between',
    },
    card: {
        flex: 1,
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_10,
        padding: Spacing.SCALE_12,
        margin: Spacing.SCALE_4,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1.5,
    },
    value: {
        fontSize: Typography.FONT_SIZE_20,
        color: colors.dark_charcoal,
        lineHeight: Size.SIZE_22,
        fontFamily: 'Ubuntu-Medium',
    },
    label: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.darkGreyRgba,
        lineHeight: Size.SIZE_13,
        fontFamily: 'Ubuntu-Medium',
    },
});

export default memo(StatsGrid);
