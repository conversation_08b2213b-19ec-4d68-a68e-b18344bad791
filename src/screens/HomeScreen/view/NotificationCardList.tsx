import React, { useCallback, useContext, useEffect } from 'react';
import { Dimensions, View, Platform, StyleSheet, Pressable } from 'react-native';
import {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    withSpring,
    withSequence,
    withDelay,
    runOnJS,
} from 'react-native-reanimated';
import { Gesture, GestureDetector, Text } from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';
const CleverTap = require('clevertap-react-native');

import { NotificationItem } from '../../../interface';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import messageType from '../action/handleMessageType';
import { NotificationContext } from './NotificationCard';
import { colors } from '../../../theme/theme';
import { tiers } from '../../../utils/constants/constants';
import { GlobalContext } from '../../../context/contextApi';
import { readNotifications } from '../action/readNotification';
import constants from '../../../utils/constants/constants';
import { AuthContext } from '../../../context/AuthContext';
import { StreamChatContext } from '../../../context/StreamChatContext';
import handleNotificationPress from '../../../utils/notification/NotficationRedirection';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const SWIPE_THRESHOLD = SCREEN_WIDTH * 0.3;

const RenderItem: React.FC<{ item: NotificationItem; index: number }> = ({ item, index }) => {
    const context = useContext(NotificationContext);
    if (!context) throw new Error('RenderItem must be used within NotificationContext');
    const { cardHeight, firstCardLines } = context;
    const { state, actions } = useContext(GlobalContext);
    const { user } = useContext(AuthContext);
    const { notifications, expanded } = state;
    const { chatClient, setChannel } = useContext(StreamChatContext);
    const navigation = useNavigation();
    const translateX = useSharedValue(0);
    const translateY = useSharedValue(20);
    const itemHeight = useSharedValue(expanded ? 'auto' : cardHeight);
    const opacityY = useSharedValue(1);
    const opacity = useSharedValue(1);
    useEffect(() => {
        translateY.value = withTiming(1, { duration: 150 + index * 100 });
        opacityY.value = withTiming(1, { duration: 10 + index * 50 });
    }, []);
    const animatedItemStyle = useAnimatedStyle(() => ({
        transform: [{ translateY: translateY.value }],
        opacity: opacityY.value,
    }));

    const removeNotification = useCallback(
        (id: string) => {
            actions.setNotifications(notifications.filter((item: any) => item.id !== id));
            readNotification(id);
        },
        [notifications],
    );

    const readNotification = async (id: string) => {
        await readNotifications(user?.id, id);
    };

    const handleNotificationClick = (item: NotificationItem) => {
        CleverTap.recordEvent(constants.CLEVERTAP.CLICK_ON_PLAYING_CARD, {
            'User email': user?.email,
            'Current Account Status': user?.muted ? 'Muted' : 'Unmuted',
            Membership: user?.membership_plan?.name,
            Tier: tiers[user?.tier],
            'Playing Card Module': item?.type,
            'Playing Card Notification Content': item?.message,
        });
        actions?.setAppLoader(true);
        handleNotificationPress(item?.type, item.data, navigation, user, actions, chatClient, setChannel);
        removeNotification(item?.id);
    };

    const swipeGesture = Gesture.Pan()
        .activeOffsetX([-10, 10])
        .onUpdate((event) => {
            translateX.value = event.translationX;
        })
        .onEnd((event) => {
            const shouldBeDismissed = Math.abs(translateX.value) > SWIPE_THRESHOLD;

            if (shouldBeDismissed) {
                const direction = translateX.value > 0 ? SCREEN_WIDTH : -SCREEN_WIDTH;
                translateX.value = withSequence(
                    withTiming(direction, { duration: 200 }),
                    withDelay(100, withTiming(0)),
                );
                opacity.value = withTiming(0, { duration: 200 });
                // @ts-ignore
                itemHeight.value = withTiming(0, { duration: 200 }, () => {
                    runOnJS(removeNotification)(item.id);
                });
            } else {
                translateX.value = withSpring(0);
            }
        });

    const rStyle = useAnimatedStyle(() => ({
        transform: [{ translateX: translateX.value }],
        opacity: opacity.value,
        // @ts-ignore
        height: itemHeight.value,
        marginBottom: Spacing.SCALE_5,
    }));

    return (
        <GestureDetector gesture={swipeGesture}>
            <Animated.View
                style={[
                    styles.container,
                    rStyle,
                    expanded && animatedItemStyle,
                    !expanded && {
                        position: 'absolute',
                        top: index * Spacing.SCALE_10, // Adjust overlap amount
                        zIndex: 3 - index, // Higher index means it will be on top
                        width: `${100 - index * 8}%`,
                        alignSelf: 'center',
                    },
                ]}>
                <Pressable
                    onPress={() => handleNotificationClick(item)}
                    style={[
                        styles.card,
                        !expanded && {
                            height: cardHeight,
                            overflow: 'hidden',
                            paddingVertical: Spacing.SCALE_12,
                        },
                    ]}>
                    <Text style={styles.notificationTypeTextStyle}>{messageType(item?.type)}</Text>
                    <View style={{ rowGap: Spacing.SCALE_4 }}>
                        <Text
                            numberOfLines={expanded ? 0 : firstCardLines}
                            style={[
                                styles.notificationMessageTextStyle,
                                !expanded && {
                                    lineHeight: Size.SIZE_20,
                                    minHeight: Size.SIZE_20,
                                },
                            ]}>
                            {item?.message}
                        </Text>
                    </View>
                </Pressable>
            </Animated.View>
        </GestureDetector>
    );
};

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: Spacing.SCALE_16,
    },
    card: {
        backgroundColor: colors.whiteRGB,
        padding: Spacing.SCALE_12,
        borderRadius: Spacing.SCALE_12,
        columnGap: Spacing.SCALE_16,
        ...Platform.select({
            ios: {
                elevation: 3,
                shadowColor: 'rgba(0, 0, 0, 0.6)',
                shadowOffset: { width: 0, height: Platform.OS === 'ios' ? 0.5 : 2 },
                shadowOpacity: 0.1,
                shadowRadius: Platform.OS === 'ios' ? 1 : 2,
                boxShadow: '0px 0px 0px rgba(0, 0, 0, 0.6)',
            },
            android: {
                elevation: 0.4,
            },
        }),
    },
    notificationTypeTextStyle: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.darkGreyRgba,
        lineHeight: Size.SIZE_18,
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_4,
        textTransform: 'uppercase',
    },
    notificationMessageTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.dark_charcoal,
        lineHeight: Size.SIZE_22,
        fontFamily: 'Ubuntu-Regular',
    },
});

export default RenderItem;
