import { StyleSheet, Text, View, TouchableOpacity, Platform } from 'react-native';
import React, { memo, useContext, useState, useEffect, useMemo, createContext } from 'react';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { FlatList } from 'react-native-gesture-handler';

import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { GlobalContext } from '../../../context/contextApi';
import { ArrowIcon, NotificationFrameIcon } from '../../../assets/svg';
import { NotificationContextType } from '../../../interface';
import RenderItem from './NotificationCardList';

const NotificationContext = createContext<NotificationContextType | null>(null);

const NotificationCard: React.FC<{ getNotification: () => void }> = ({ getNotification }) => {
    const { state, actions } = useContext(GlobalContext);
    const { notifications, expanded } = state;
    const [firstCardLines, setFirstCardLines] = useState(1);
    const [cardHeight, setCardHeight] = useState(Size.SIZE_100);

    useEffect(() => {
        if (notifications.length > 0) {
            const message = notifications[0].message;
            // Calculate number of lines based on message length and width
            const averageCharsPerLine = 40;
            const textLines = Math.ceil(message.length / averageCharsPerLine);

            // If text ends with ellipsis (...), add one line
            const hasEllipsis = message.endsWith('...');
            const actualLines = hasEllipsis ? Math.min(textLines + 1, 4) : Math.min(textLines, 4);

            setFirstCardLines(actualLines);

            // Calculate precise height based on content
            const headerHeight = Size.SIZE_30; // Height for type text and padding
            const lineHeight = Size.SIZE_20; // Line height for message text
            const verticalPadding = Spacing.SCALE_24; // Total vertical padding

            const calculatedHeight = headerHeight + actualLines * lineHeight + verticalPadding;
            setCardHeight(calculatedHeight);
        }
    }, [notifications]);

    const height = useSharedValue(0);

    const toggleExpand = () => {
        height.value = withTiming(expanded ? 0 : 1, { duration: 500 });
        actions.setExpanded(!expanded);
    };

    const animatedStyle = useAnimatedStyle(() => ({
        opacity: height.value,
        overflow: 'hidden',
    }));
    const androidAnimatedStyle = useAnimatedStyle(() => ({
        height: expanded ? 'auto' : height.value === 1 ? 'auto' : 0,
        opacity: 1,
        overflow: 'hidden',
    }));

    const ListEmptyState = () => (
        <View style={[styles.container, { marginTop: Spacing.SCALE_12 }]}>
            <View style={styles.emptyStateCard}>
                <NotificationFrameIcon />
                <View style={{ rowGap: Spacing.SCALE_4 }}>
                    <Text style={styles.text1}>You have no new notifications</Text>
                    <Text style={styles.text2}>
                        Check back here for new updates and notifications on your TG Account.
                    </Text>
                </View>
            </View>
        </View>
    );

    const contextValue = useMemo(
        () => ({
            cardHeight,
            firstCardLines,
        }),
        [cardHeight, firstCardLines],
    );

    return (
        <NotificationContext.Provider value={contextValue}>
            <View style={styles.header}>
                <Text style={styles.lastWeekTextStyle}>Updates & Notifications</Text>
                {expanded && notifications.length && (
                    <TouchableOpacity onPress={toggleExpand}>
                        <Text style={styles.showLessTextStyle}> Show less</Text>
                    </TouchableOpacity>
                )}
            </View>

            {notifications?.length > 0 ? (
                <>
                    <View
                        style={[
                            styles.container,
                            {
                                padding: expanded ? Spacing.SCALE_1 : Spacing.SCALE_12,
                                paddingBottom: Spacing.SCALE_8,
                                paddingHorizontal: 0,
                            },
                        ]}>
                        {!expanded && (
                            <View style={{ position: 'relative', height: cardHeight + Size.SIZE_80 }}>
                                <FlatList
                                    data={notifications.slice(0, 3)}
                                    keyExtractor={(item, index) => item?.id?.toString() || index.toString()}
                                    renderItem={({ item, index }) => {
                                        return <RenderItem item={item} index={index} />;
                                    }}
                                    contentContainerStyle={{ flexGrow: 1 }}
                                    scrollEnabled={false}
                                />
                            </View>
                        )}
                        {/* View More Button */}
                        {!expanded && notifications.length > 1 && (
                            <TouchableOpacity
                                onPress={toggleExpand}
                                style={[
                                    styles.viewMoreContainerStyle,
                                    {
                                        zIndex: 999,
                                        top:
                                            notifications.length === 2
                                                ? cardHeight + Size.SIZE_15
                                                : cardHeight + Size.SIZE_25,
                                    },
                                ]}>
                                <View style={styles.viewMoreInnerContainerStyle}>
                                    <Text style={styles.viewMoreTextStyle}>+{notifications.length - 1} more</Text>
                                    <ArrowIcon />
                                </View>
                            </TouchableOpacity>
                        )}
                    </View>

                    {/* Animated Notification List */}
                    {expanded && (
                        <Animated.View
                            style={[
                                Platform.OS === 'ios' ? animatedStyle : androidAnimatedStyle,
                                { flex: 1 },
                                !expanded && { position: 'relative', height: Size.SIZE_140 },
                            ]}>
                            <FlatList
                                data={notifications}
                                keyExtractor={(item, index) => item?.id?.toString() || index.toString()}
                                renderItem={({ item, index }) => <RenderItem item={item} index={index} />}
                                contentContainerStyle={{
                                    flexGrow: 1,
                                    paddingBottom: Spacing.SCALE_20,
                                }}
                                showsVerticalScrollIndicator={false}
                            />
                        </Animated.View>
                    )}
                </>
            ) : (
                <ListEmptyState />
            )}
        </NotificationContext.Provider>
    );
};

export { NotificationContext };

export default memo(NotificationCard);

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: Spacing.SCALE_16,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_16,
    },
    text1: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.dark_charcoal,
        lineHeight: Size.SIZE_18,
        fontFamily: 'Ubuntu-Medium',
    },
    text2: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.greyRgb,
        lineHeight: Size.SIZE_18,
        fontFamily: 'Ubuntu-Regular',
        width: Spacing.SCALE_230,
    },
    emptyStateCard: {
        backgroundColor: colors.whiteRGB,
        paddingHorizontal: Spacing.SCALE_10,
        paddingVertical: Spacing.SCALE_20,
        borderRadius: Spacing.SCALE_12,
        columnGap: Spacing.SCALE_16,
        flexDirection: 'row',
        alignItems: 'center',
    },
    viewMoreContainerStyle: {
        alignItems: 'center',
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_50,
        height: Size.SIZE_18,
        width: Size.SIZE_80,
        borderWidth: 1,
        borderColor: 'rgba(242, 242, 242, 1)',
        alignSelf: 'center',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 4,
        shadowColor: 'rgba(0, 0, 0, 0.11)',
        position: 'absolute',
    },
    viewMoreInnerContainerStyle: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_4,
    },
    viewMoreTextStyle: {
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        textAlign: 'center',
        lineHeight: Size.SIZE_14,
        fontWeight: '500',
    },
    lastWeekTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        lineHeight: Size.SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
    },
    showLessTextStyle: {
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        textAlign: 'center',
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
    },
});
