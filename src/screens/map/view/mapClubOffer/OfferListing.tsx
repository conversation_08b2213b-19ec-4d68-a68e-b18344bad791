import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// components and context imports
import { AuthContext } from '../../../../context/AuthContext';

// interface, utils, theme and assets imports
import { MapClubOffers } from '../../../../interface';
import { RootStackParamList } from '../../../../interface/type';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';
import { CreateRequestIconTeal, DeleteGreyIcon, EditIcon, GiftBoxIconTeal } from '../../../../assets/svg';
import { handleTimeFormat } from '../../../../components/timeFormatComponent/handleTimeFormat';

const OfferListing = ({
    item,
    navigation,
    index,
    handleEditOffer,
    handleCreateRequest,
    handleDeleteOffer,
    setSelectedOffer,
    handleOfferPress,
}: {
    item: MapClubOffers;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    index: number;
    handleEditOffer: (offer: MapClubOffers) => void;
    handleCreateRequest: (isRequestAgainstOffer: boolean, offer: MapClubOffers) => void;
    handleDeleteOffer: (offer: MapClubOffers) => void;
    setSelectedOffer: (offer: MapClubOffers) => void;
    handleOfferPress: (item: MapClubOffers) => void;
}) => {
    const { user } = useContext(AuthContext);
    return (
        <TouchableOpacity
            style={styles.container}
            onPress={() => handleOfferPress(item)}>
            <View style={styles.headerContainer}>
                <View style={styles.offerIdContainer}>
                    <View style={styles.iconContainer}>
                        <GiftBoxIconTeal />
                    </View>
                    <View style={{ rowGap: Spacing.SCALE_4, width: '65%' }}>
                        <Text style={styles.offerIdText}>
                            Offer ID <Text style={styles.offerIdNumber}>#{item.offer_id}</Text>
                        </Text>
                        <Text style={styles.dateText}>
                            {handleTimeFormat(item.start_date) === handleTimeFormat(item.end_date)
                                ? handleTimeFormat(item.start_date)
                                : `${handleTimeFormat(item.start_date)} - ${handleTimeFormat(item.end_date)}`}
                        </Text>
                    </View>
                </View>
                {item.user_id === user?.id ? (
                    <View style={styles.btnContainer}>
                        <TouchableOpacity
                            style={styles.btnWrapper}
                            onPress={() => {
                                handleEditOffer(item);
                            }}>
                            <EditIcon />
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.btnWrapper}
                            onPress={() => {
                                handleDeleteOffer(item);
                            }}>
                            <DeleteGreyIcon />
                        </TouchableOpacity>
                    </View>
                ) : item.requested ? (
                    <View style={styles.requestedTextWrapper}>
                        <Text style={styles.requestedTextStyles}>Requested</Text>
                    </View>
                ) : (
                    <TouchableOpacity
                        style={styles.createRequestContainer}
                        onPress={() => {
                            setSelectedOffer(item);
                            handleCreateRequest(true, item);
                        }}>
                        <CreateRequestIconTeal />
                    </TouchableOpacity>
                )}
            </View>
            <Text style={styles.createdByText}>Created by - {item.creatorName}</Text>
            <Text style={styles.descriptionText}>{item.details}</Text>
        </TouchableOpacity>
    );
};

export default OfferListing;

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_10,
        padding: Spacing.SCALE_12,
        borderWidth: 1,
        borderColor: colors.greyRgba,
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    offerIdContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Spacing.SCALE_10,
        width: '80%',
    },
    iconContainer: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        backgroundColor: colors.tealRGBAColor,
        borderRadius: Size.SIZE_50,
        justifyContent: 'center',
        alignItems: 'center',
    },
    offerIdText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
    },
    offerIdNumber: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.tealRgb,
    },
    dateText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.greyRgb,
    },
    createRequestContainer: {
        height: Size.SIZE_32,
        width: Size.SIZE_32,
        borderRadius: Size.SIZE_8,
        borderWidth: 1,
        borderColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
    },
    createdByText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.greyRgb,
        marginTop: Spacing.SCALE_14,
    },
    descriptionText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
        marginTop: Spacing.SCALE_14,
        lineHeight: Size.SIZE_20,
    },
    requestedTextWrapper: {
        backgroundColor: colors.tealRGBAColor,
        borderRadius: Size.SIZE_6,
        paddingHorizontal: Spacing.SCALE_8,
        paddingVertical: Spacing.SCALE_6,
    },
    requestedTextStyles: {
        fontSize: Typography.FONT_SIZE_10,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.tealRgb,
    },
    btnWrapper: {
        height: Size.SIZE_24,
        width: Size.SIZE_24,
        alignItems: 'center',
        justifyContent: 'center',
    },
    btnContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Spacing.SCALE_10,
    },
});
