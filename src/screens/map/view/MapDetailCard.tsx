import { StyleSheet, View } from 'react-native';
import React, { useContext } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// Component imports
import ClubInfoCard from './ClubInfoCard';
import MyTgCard from './MyTgCard';
import ReviewCard from './ReviewCard';
import MapsClubOffer from './mapClubOffer/MapsClubOffer';
import { AuthContext } from '../../../context/AuthContext';

// context and interface imports.
import { MapClub, MapClubDetail, MapClubOffers } from '../../../interface';
import { RootStackParamList } from '../../../interface/type';

const MapDetailCard = ({
    clubDetail,
    handleFavoriteClub,
    handlePlayedClub,
    ownClub,
    club,
    navigation,
    clubOffers,
    handleEditOffer,
    handleCreateRequest,
    handleDeleteOffer,
    setSelectedOffer,
    gameReviewCount,
    handleOfferPress,
    prevScreenCallBack,
}: {
    clubDetail: MapClubDetail;
    handleFavoriteClub: (active: boolean) => void;
    handlePlayedClub: (active: boolean) => void;
    ownClub: any[];
    club: MapClub;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    clubOffers: MapClubOffers[];
    handleEditOffer: (offer: MapClubOffers) => void;
    handleCreateRequest: (isRequestAgainstOffer: boolean, offer: MapClubOffers | null) => void;
    handleDeleteOffer: (offer: MapClubOffers) => void;
    setSelectedOffer: (offer: MapClubOffers) => void;
    gameReviewCount: number;
    handleOfferPress: (item: MapClubOffers) => void;
    prevScreenCallBack?: () => void;
}) => {
    const { user } = useContext(AuthContext);
    return (
        <View style={styles.container}>
            <ClubInfoCard
                clubDetail={clubDetail}
                user={user}
                handleFavoriteClub={handleFavoriteClub}
                handlePlayedClub={handlePlayedClub}
                ownClub={ownClub}
                club={club}
                navigation={navigation}
            />
            <MyTgCard clubDetail={clubDetail} user={user} navigation={navigation} club={club} />
            <MapsClubOffer
                clubOffers={clubOffers}
                navigation={navigation}
                handleEditOffer={handleEditOffer}
                handleCreateRequest={handleCreateRequest}
                handleDeleteOffer={handleDeleteOffer}
                setSelectedOffer={setSelectedOffer}
                club={club}
                handleOfferPress={handleOfferPress}
                prevScreenCallBack={prevScreenCallBack}
            />
            <ReviewCard club={club} gameReviewCount={gameReviewCount} />
        </View>
    );
};

export default MapDetailCard;

const styles = StyleSheet.create({
    container: {},
});
