import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// Components and context imports
import { AuthContext } from '../../../context/AuthContext';
import GameReviewListingCard from './gameReview/GameReviewListingCard';

// Theme, Interfaces and responsive UI imports
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { RootStackParamList } from '../../../interface/type';
import { GlobalContext } from '../../../context/contextApi';
import { GameReview, MapClub } from '../../../interface';

const ReviewCard = ({ club, gameReviewCount }: { club: MapClub; gameReviewCount: number }) => {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { user } = useContext(AuthContext);
    const { state } = useContext(GlobalContext);
    const handleViewAll = () => {
        navigation.navigate('GameReview', { club: club });
    };
    return state.gameReview.length ? (
        <View style={styles.container}>
            <View style={styles.reviewHeaderContainer}>
                <Text style={styles.reviewText}>Game Reviews ({gameReviewCount})</Text>
                {gameReviewCount > 3 && (
                    <TouchableOpacity onPress={handleViewAll}>
                        <Text style={styles.viewAll}>View All</Text>
                    </TouchableOpacity>
                )}
            </View>
            <FlatList
                data={state.gameReview.slice(0, 3)}
                renderItem={({ item }: { item: GameReview }) => (
                    <GameReviewListingCard item={item} navigation={navigation} />
                )}
                keyExtractor={(item: GameReview) => item.game_id.toString()}
                showsVerticalScrollIndicator={false}
            />
        </View>
    ) : null;
};

export default ReviewCard;

const styles = StyleSheet.create({
    container: {
        marginTop: Spacing.SCALE_20,
    },
    reviewContentContainer: {
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_10,
        paddingVertical: Spacing.SCALE_10,
    },
    reviewText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_10,
    },
    viewAll: {
        color: colors.tealRgb,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: 400,
        fontFamily: 'Ubuntu-Regular',
        marginBottom: Spacing.SCALE_10,
    },
    reviewHeaderContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
});
