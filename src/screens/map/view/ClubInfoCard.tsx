import { Platform, StyleSheet, Text, View } from 'react-native';
import React, { useEffect, useMemo } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// Component and context imports
import TGText from '../../../components/fields/TGText';
import TierCard from '../../../components/map/TierCard';
import PlayedButton from '../../../components/map/PlayedButton';
import FavoriteButton from '../../../components/buttons/FavoriteButton';

// Interface, theme, utils, svg imports
import { MapClub, MapClubDetail, User } from '../../../interface';
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import {
    ClubGolfIconTeal,
    HighlyRequestedIcon,
    MemberIcon,
    RequestAcceptedIcon,
    UnexploredIcon,
} from '../../../assets/svg';
import { MapLocationIconNew } from '../../../assets/svg';
import { RootStackParamList } from '../../../interface/type';

const ClubInfoCard = ({
    clubDetail,
    user,
    handleFavoriteClub,
    handlePlayedClub,
    ownClub,
    club,
    navigation,
}: {
    clubDetail: MapClubDetail;
    user: User;
    handleFavoriteClub: (active: boolean) => void;
    handlePlayedClub: (active: boolean) => void;
    ownClub: number[];
    club: MapClub;
    navigation: NativeStackNavigationProp<RootStackParamList>;
}) => {
    const {
        clubs,
        request,
        contacts,
        clubMemberCount,
        pnCount,
        friends,
        friendsPlayed,
        userPlayedAtClub,
        isEligibleForCreateRequest,
        tgGroupMembersCount,
        totalMemberCount,
        requestCreated,
    } = useMemo(() => {
        return clubDetail || {};
    }, [clubDetail]);
    const [contactOnlyClub, setContactOnlyClub] = React.useState(false);

    const { id, lat, lng, address, name, lowest_visible_tier, club_type, closurePeriods, guest_time_restrictions } =
        useMemo(() => {
            return clubs || {};
        }, [clubs]);

    useEffect(() => {
        handleSetContactClub();
    }, [clubDetail]);

    /**
     * Determine if the club is a contact-only club for the current user.
     * This means the user is not a part of the club and either has contacts or friends in the club
     * or the club is a MY TG club and has no members.
     */
    const handleSetContactClub = () => {
        if (
            !ownClub.includes(clubs?.id) && // if the user is not a part of this club
            (contacts || friends) && // and the user has contacts or friends
            ((!user?.visibleToPublic && club?.properties?.color === 'teal_contact') || //this means it is a MY TG club
                !clubMemberCount) // and there is no member in this club
        ) {
            setContactOnlyClub(true);
        } else {
            setContactOnlyClub(false);
        }
    };

    return (
        <>
            {/* Highly Requested Label */}
            {clubDetail?.clubs?.club_demand_type ? (
                <View style={styles.highlyRequestedContainer}>
                    {clubDetail?.clubs?.club_demand_type === 1 ? <HighlyRequestedIcon /> : <UnexploredIcon />}
                </View>
            ) : null}
            <View
                style={[
                    styles.detailsContainer,
                    {
                        width: clubDetail?.clubs?.club_demand_type ? '99%' : '100%',
                    },
                ]}>
                <View style={styles.tierCardContainer}>
                    <TierCard
                        clubType={clubDetail?.clubs?.club_type}
                        tier={clubDetail?.clubs?.lowest_visible_tier}
                        wrapperStyle={[
                            styles.tierCard,
                            { marginTop: clubDetail?.clubs?.club_demand_type ? Spacing.SCALE_10 : 0 },
                        ]}
                    />
                    <View style={styles.buttonContainer}>
                        {clubs?.club_type !== 2 && (
                            <PlayedButton
                                onPress={handlePlayedClub}
                                isPlayed={user.playedClubs.includes(clubDetail.clubs.id)}
                            />
                        )}
                        {clubs?.club_type !== 2 && !contactOnlyClub && !ownClub.includes(clubs?.id) && (
                            <View style={styles.favoriteButtonContainer}>
                                <FavoriteButton
                                    onPress={handleFavoriteClub}
                                    size={Size.SIZE_14}
                                    isFavorited={
                                        user.favorite_clubs.filter(({ club_id }: { club_id: number }) => club_id === id)
                                            .length === 1
                                    }
                                />
                            </View>
                        )}
                    </View>
                </View>
                <View>
                    <View style={[styles.buttonContainer, styles.clubNameContainer]}>
                        <ClubGolfIconTeal />
                        <View style={{ maxWidth: '80%' }}>
                            <TGText style={styles.clubNameText}>{name}</TGText>
                        </View>
                    </View>
                    <View style={styles.addressDetailSection}>
                        <View style={styles.locationIconWrapper}>
                            <MapLocationIconNew />
                        </View>
                        <Text style={styles.addressText}>{address}</Text>
                    </View>
                </View>
                {!contactOnlyClub && <View style={styles.divider} />}
                {!contactOnlyClub && (
                    <View style={styles.memberIconContainer}>
                        <MemberIcon />
                        <Text style={styles.memberText}>
                            Total Members : <Text style={{ color: colors.lightBlack }}>{totalMemberCount}</Text>
                        </Text>
                    </View>
                )}
                {!contactOnlyClub && requestCreated && (
                    <View style={[styles.memberIconContainer, { marginBottom: 0 }]}>
                        <RequestAcceptedIcon />
                        <Text style={styles.memberText}>
                            Requests Accepted :{' '}
                            <Text style={{ color: colors.brightGreen }}>
                                {request === '0.00' ? 'NA' : `${request}% of ${requestCreated} Requests`}
                            </Text>
                        </Text>
                    </View>
                )}
            </View>
        </>
    );
};

export default ClubInfoCard;

const styles = StyleSheet.create({
    detailsContainer: {
        flexDirection: 'column',
        padding: Spacing.SCALE_12,
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_10,
        marginVertical: Spacing.SCALE_4,
        borderWidth: 1,
        borderColor: colors.lightgray,
        alignSelf: 'flex-end',
    },
    topDetailSection: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
    },
    addressDetailSection: {
        flexDirection: 'row',
        marginTop: Spacing.SCALE_8,
        columnGap: Spacing.SCALE_10,
        width: '90%',
    },
    detailSection: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    see_offer: {
        paddingVertical: 2.5,
        paddingHorizontal: 10,
        borderColor: 'teal',
        borderWidth: 1,
        borderRadius: 5,
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 10,
        height: 25,
    },
    imageStyle: {
        height: Size.SIZE_14,
        width: Size.SIZE_13,
    },
    locationIconWrapper: {
        marginTop: Platform.OS === 'ios' ? Spacing.SCALE_1 : Spacing.SCALE_3,
    },
    tierCard: {
        borderRadius: Size.SIZE_6,
    },
    tierCardContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    buttonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_12,
    },
    favoriteButtonContainer: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_22,
        borderWidth: 1,
        borderColor: colors.lightgray,
        alignItems: 'center',
        justifyContent: 'center',
        padding: Spacing.SCALE_5,
    },
    clubNameContainer: {
        columnGap: Spacing.SCALE_8,
        marginTop: Spacing.SCALE_12,
    },
    clubNameText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    addressText: {
        color: colors.fadeBlack,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
    },
    clubDetailsText: {
        color: colors.tealRgb,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_16,
    },
    divider: {
        height: 1,
        backgroundColor: colors.lightgray,
        marginVertical: Spacing.SCALE_10,
        width: '100%',
        alignSelf: 'center',
    },
    memberIconContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_8,
    },
    memberText: {
        color: colors.fadeBlack,
        fontSize: Typography.FONT_SIZE_13,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
    },
    highlyRequestedContainer: {
        position: 'absolute',
        zIndex: 2,
        alignItems: 'center',
        justifyContent: 'center',
        top: Spacing.SCALE_4,
    },
});
