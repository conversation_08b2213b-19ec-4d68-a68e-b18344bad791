import { User } from '../../../interface';
import { CLUB_DETAILS_MAP } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';
import constants from '../../../utils/constants/constants';

const CleverTap = require('clevertap-react-native');

export const getMapClubDetail = async (id: number, color: string, user: User) => {
    CleverTap.recordEvent(constants.CLEVERTAP.EVENTS.GOLF_CLUBS, { clubId: id });
    const body = {
        userId: user?.id,
        clubId: Number(id),
        clubColor: color,
    };
    return fetcher({
        endpoint: CLUB_DETAILS_MAP,
        method: 'POST',
        body,
    })
        .then((data) => {
            const { lat, lng } = data?.clubs;
            if (lat && lng) {
                return data;
            }
            return {};
        })
        .catch(console.log);
};
