import { GET_MAP_CLUB_OFFERS } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';

interface GetMapClubOffersPayload {
    userId: string;
    clubId: number;
    page: number;
    limit: number;
}

export const getMapClubOffers = async (payload: GetMapClubOffersPayload) => {
    return fetcher({
        endpoint: GET_MAP_CLUB_OFFERS,
        method: 'POST',
        body: payload,
    })
        .then((data) => {
            return data;
        })
        .catch(console.log);
};
