import { GET_GAME_REVIEW } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';

interface GetGameReviewPayload {
    userId: string;
    clubId: number;
    page: number;
    limit: number;
}

export const getGameReview = async (payload: GetGameReviewPayload) => {
    return fetcher({
        endpoint: GET_GAME_REVIEW,
        method: 'POST',
        body: payload,
    })
        .then((data) => {
            return data;
        })
        .catch(console.log);
};
