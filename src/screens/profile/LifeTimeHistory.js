import React, { useContext, useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, Linking, StatusBar, StyleSheet, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';

import ProfileHeaderNew from '../../components/layout/ProfilrHeaderNew';
import { lifeTimeNGV } from '../../graphql/queries/membership';
import useQuery from '../../hooks/useQuery';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import LogsListing from './LogsListing';
import { colors } from '../../theme/theme';
import { fetcher } from '../../service/fetcher';
import { ngvCount } from '../../service/EndPoint';
import showToast from '../../components/toast/CustomToast';
import CommonBottomSheet from '../../components/commonBottomSheet/CommonBottomSheet';
import { GlobalContext } from '../../context/contextApi';
import OfflineLogStepScreen from '../logofflineGame/view/OfflineLogStep1';
import OfflineLogSecondStepScreen from '../logofflineGame/view/OfflineLogStep2';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import WarningScreen from '../logofflineGame/view/WarningScreen';
import ProfileHeader from '../../components/layout/ProfileHeader';

const LifeTimeHistory = ({ route }) => {
    const { state, actions } = useContext(GlobalContext);
    const [modalPosition, setModalPosition] = useState(-1);
    const [lastId, setLastId] = useState(null);
    const [logsData, setLogsData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [nextExists, setNextExists] = useState(null);
    const [addFriendModal, setAddFriendModal] = useState(false);
    const [successfullSentRequest, setSuccessfullSentRequest] = useState(false);
    const [showWarning, setShowWarning] = useState(false);
    const navigation = useNavigation();
    const { data } = useQuery(lifeTimeNGV, {
        user_id: route.params?.user?.id,
    });
    const opacity = useSharedValue(0);
    useEffect(() => {
        if (state.offlineLogGameStep === 2) {
            setTimeout(() => {
                opacity.value = withTiming(1, { duration: 300 });
            }, 200);
        }
        return () => {
            opacity.value = 0;
        };
    }, [state.offlineLogGameStep]);
    const animatedOpacity = useAnimatedStyle(() => ({
        opacity: opacity.value,
    }));

    let lNgv = data?.user_membership_logs_aggregate?.aggregate?.sum?.ngv_change;
    let count = null;
    count = data?.user_membership_logs_aggregate?.aggregate?.count;

    const handleReload = () => {
        if (nextExists) {
            setLastId(logsData[49]?.id);
            getNGVData(logsData[49]?.id);
        }
    };
    useEffect(() => {
        getNGVData(lastId);
    }, []);

    function getNGVData(lastId) {
        setLoading(true);
        const body = {
            user_id: route.params?.user?.id,
            lastId: lastId,
        };
        fetcher({
            endpoint: ngvCount,
            method: 'POST',
            body,
        })
            .then(async (res) => {
                if (res?.status) {
                    setLoading(false);
                    let temp = [];
                    let flag = false;
                    res?.data.map((data) => {
                        if (logsData !== null && !logsData.includes(data?.module_id)) {
                            temp.push(data);
                            flag = true;
                        } else {
                            temp.push(data);
                        }
                    });
                    if (logsData === null) {
                        setLogsData([...temp]);
                        setNextExists(res?.nextExists);
                    } else {
                        setLogsData([...logsData, ...temp]);
                        setNextExists(res?.nextExists);
                    }
                } else {
                    showToast({});
                }
            })
            .catch((err) => console.log('ngv api error ==>>', err));
    }

    React.useEffect(() => {
        const unsubscribe = navigation.addListener('focus', handleCall);
        // Return the function to unsubscribe from the event so it gets removed on unmount
        return unsubscribe;
    }, [navigation]);

    const handleCall = () => {
        getNGVData(lastId);
    };

    return (
        <>
            <StatusBar barStyle="light-content" />
            <View style={{ flex: 1, backgroundColor: 'white' }}>
                <View style={{ marginBottom: Spacing.SCALE_16 }}>
                    <ProfileHeader
                        title={'NGV Assessment Logs'}
                        headerTitleStyle={styles.headerTitleStyle}
                        backButtonFillColor={colors.lightBlack}
                        containerStyle={{
                            backgroundColor: colors.whiteRGB,
                            paddingBottom: Spacing.SCALE_10,
                        }}
                    />
                </View>
                {logsData?.length !== 0 && logsData !== null && !loading ? (
                    <View
                        style={{
                            backgroundColor: 'rgba(246, 246, 246, 1)',
                            flex: 1,
                            borderTopRightRadius: 20,
                            borderTopLeftRadius: 20,
                        }}>
                        <View style={{ flexDirection: 'row', justifyContent: 'space-around' }}>
                            <Text style={styles.txt}>
                                Lifetime NGV:{' '}
                                <Text style={styles.innerTxt}>{count ? lNgv : count === null && 'NA'}</Text>
                            </Text>
                            <Text style={styles.txt}>
                                Current NGV:
                                <Text style={styles.innerTxt}>
                                    {' '}
                                    {route.params?.ngv !== null ? route.params?.ngv : 'NA'}
                                </Text>{' '}
                            </Text>
                        </View>
                        <FlatList
                            data={logsData}
                            renderItem={(item, index) => (
                                <LogsListing
                                    modalOpenState={[modalPosition, setModalPosition]}
                                    data={item}
                                    index={index}
                                />
                            )}
                            showsVerticalScrollIndicator={false}
                            onEndReachedThreshold={0.5}
                            onEndReached={handleReload}
                            keyExtractor={(item, index) => index.toString()}
                        />
                    </View>
                ) : (
                    !loading && (
                        <View style={{ flex: 1, alignItems: 'center' }}>
                            <Text
                                style={{
                                    color: 'black',
                                    fontSize: Typography.FONT_SIZE_14,
                                    lineHeight: Size.SIZE_21,
                                    fontWeight: '500',
                                    paddingTop: Spacing.SCALE_70,
                                }}>
                                No NGV logs found
                            </Text>
                        </View>
                    )
                )}
            </View>
            {loading && (
                <View
                    style={{
                        position: 'absolute',
                        top: 0,
                        bottom: 0,
                        right: 0,
                        left: 0,
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                    <ActivityIndicator color={colors.tealRgb} size="large" />
                </View>
            )}
        </>
    );
};

export default LifeTimeHistory;

const styles = StyleSheet.create({
    txt: {
        color: 'black',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_21,
        fontWeight: '500',
        paddingVertical: Spacing.SCALE_5,
    },
    innerTxt: {
        color: 'rgba(9, 128, 137, 1)',
    },
    txt2: {
        color: 'rgba(9, 128, 137, 1)',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '500',
    },
    txtWrapper: {
        flexDirection: 'row',
        paddingVertical: Spacing.SCALE_6,
    },
    txtWrapper1: {
        flexDirection: 'row',
    },
    txtData: {
        fontSize: Typography.FONT_SIZE_11,
        fontWeight: '500',
        lineHeight: Size.SIZE_16,
        color: 'rgba(51, 51, 51, 1)',
        paddingLeft: Spacing.SCALE_30,
    },
    warningPopupContainer: {
        flex: 1,
        backgroundColor: colors.transparentRgba,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        padding: Spacing.SCALE_16,
        alignItems: 'center',
        justifyContent: 'center',
    },
    warningHeader: {
        fontSize: Typography.FONT_SIZE_24,
        lineHeight: Size.SIZE_32,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        textAlign: 'center',
        marginTop: Spacing.SCALE_12,
        color: colors.lightBlack,
    },
    warningBody: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_21,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        textAlign: 'center',
        marginTop: Spacing.SCALE_12,
        color: colors.systemMessageText,
        paddingHorizontal: Spacing.SCALE_20,
    },
    buttonStyle: {
        backgroundColor: colors.lightgray,
        height: Size.SIZE_45,
        borderRadius: Size.SIZE_8,
        width: Size.SIZE_150,
    },
    btnText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        textAlign: 'center',
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
});
