import { User } from '../../../interface';
import { SEARCH_CLUBS } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';

export async function getClubsName(body: object, user: User) {
    return fetcher({
        endpoint: SEARCH_CLUBS,
        method: 'POST',
        body,
    }).then((res) => {
        if (res?.clubs?.length > 0) {
        const filterClubs = user
            ? res.clubs.filter(({ id }: { id: any }) => {
                  return user.clubs.filter(({ club }) => club.id === id).length === 0;
              })
            : res.clubs;
        return filterClubs;
        }
        return [];
    }).catch((err) => {
        console.log("err", err)
        return [];
    });
}
