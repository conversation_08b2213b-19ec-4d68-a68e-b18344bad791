import showToast from '../../../components/toast/CustomToast';
import { LOG_OFFLINE_GAME, SEARCH_USER } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';

export const handleSearchUser = (body: any) => {
    try {
        return fetcher({
            endpoint: SEARCH_USER,
            method: 'POST',
            body,
        }).then((res) => res);
    } catch (error) {
        showToast({});
    }
};

export const handleLogOfflineGame = (body: any) => {
    try {
        return fetcher({
            endpoint: LOG_OFFLINE_GAME,
            method: 'POST',
            body,
        }).then((res) => res);
    } catch (error) {
        showToast({});
    }
};
