import { StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import React, { useState, useEffect, useContext } from 'react';
import { Calendar, DateData } from 'react-native-calendars';
import moment from 'moment';

import { colors } from '../../../theme/theme';
import { DateRangeModal } from '../../../interface';
import { FormContext } from '../../../forms/FormContext';

interface CalenderComponentProps {
    modal: DateRangeModal;
    setModal: (value: DateRangeModal) => void;
}

const CalenderComponent = ({ modal, setModal }: CalenderComponentProps) => {
    const { form, updateForm } = useContext(FormContext);

    useEffect(() => {
        if (modal.date || form.date) {
            updateForm('date', form.date);
        }
    }, [modal.date]);

    function setDate(day: DateData) {
        if (day.dateString !== moment().format('YYYY-MM-DD')) {
            updateForm('date', day.dateString);
        }
    }

    function getDates() {
        if (form.date) {
            return {
                [form.date]: {
                    selected: true,
                    selectedColor: colors.darkteal,
                },
            };
        }
        return {};
    }

    function closeModal() {
        setModal({
            ...modal,
            visible: false,
        });
    }

    return (
        <View style={styles.container}>
            <TouchableWithoutFeedback onPress={closeModal}>
                <View
                    style={{
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        top: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0,0,0,0.5)',
                    }}
                />
            </TouchableWithoutFeedback>
            <View
                style={{
                    backgroundColor: 'white',
                    borderRadius: 10,
                    width: '100%',
                    overflow: 'hidden',
                    paddingTop: 10,
                }}>
                <Calendar
                    maxDate={moment().subtract(1, 'day').format()}
                    current={moment().format('YYYY-MM-DD')}
                    onDayPress={setDate}
                    markedDates={getDates()}
                    minDate={moment().subtract(1, 'year').format()}
                />
                <View
                    style={{
                        flexDirection: 'row',
                        padding: 30,
                        justifyContent: 'space-between',
                    }}>
                    <TouchableOpacity
                        onPress={closeModal}
                        style={{
                            paddingHorizontal: 30,
                            paddingVertical: 10,
                            borderRadius: 10,
                        }}>
                        <Text
                            style={{
                                fontFamily: 'Ubuntu-Regular',
                                fontSize: 16,
                                color: colors.darkgray,
                            }}>
                            Cancel
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => {
                            if (form.date) {
                                setModal({
                                    ...modal,
                                    visible: false,
                                    date: form.date,
                                });
                                updateForm('date', form.date);
                            }
                        }}
                        style={{
                            backgroundColor: colors.darkteal,
                            paddingHorizontal: 30,
                            paddingVertical: 10,
                            borderRadius: 10,
                        }}>
                        <Text
                            style={{
                                fontFamily: 'Ubuntu-Regular',
                                fontSize: 16,
                                color: 'white',
                            }}>
                            Save
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

export default CalenderComponent;

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        paddingHorizontal: 30,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 100,
    },
});
