import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { Typography } from '../../../utils/responsiveUI';
import { Size } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { Spacing } from '../../../utils/responsiveUI';
import { GolfClubTeal } from '../../../assets/svg';
import { OFFLINE_GAME_LOG_SUCCESS_STRING } from '../../../utils/constants/strings';
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../interface/type';

const LogFinalPopup = () => {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    return (
        <View style={styles.overlay}>
            <View style={styles.popupWrapper}>
                <View style={styles.golfClubTealWrapper}>
                    <GolfClubTeal />
                </View>
                <Text style={styles.offlineText}>{OFFLINE_GAME_LOG_SUCCESS_STRING}</Text>
                <TealButtonNew
                    text="Dismiss"
                    onPress={() => {
                        navigation.goBack();
                    }}
                    disabled={false}
                    btnStyle={styles.cancelButtonStyle}
                    textStyle={styles.cancelButtonText}
                    loading={false}
                />
            </View>
        </View>
    );
};

export default LogFinalPopup;

const styles = StyleSheet.create({
    overlay: {
        position: 'absolute',
        bottom: 0,
        top: 0,
        left: 0,
        right: 0,
        backgroundColor: colors.transparentRgba,
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    popupWrapper: {
        padding: Spacing.SCALE_16,
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_8,
        width: '90%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    offlineText: {
        fontFamily: 'Ubuntu-Regular',
        textAlign: 'center',
        marginTop: Spacing.SCALE_16,
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_21,
        fontWeight: '400',
        color: colors.systemMessageText,
    },
    golfClubTealWrapper: {
        width: Size.SIZE_60,
        height: Size.SIZE_60,
        alignSelf: 'center',
        backgroundColor: colors.greyRgba,
        borderRadius: Size.SIZE_50,
        alignItems: 'center',
        justifyContent: 'center',
    },
    cancelButtonStyle: {
        backgroundColor: colors.tealRgb,
        paddingVertical: Spacing.SCALE_16,
        borderRadius: Size.SIZE_10,
        height: Size.SIZE_50,
        alignItems: 'center',
        marginTop: Spacing.SCALE_16,
    },
    cancelButtonText: {
        color: colors.whiteRGB,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
    },
});
