import { FlatList, Keyboard, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext } from 'react';

import { Size } from '../../../utils/responsiveUI';
import { Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { Spacing } from '../../../utils/responsiveUI';
import { DownArrowIcon } from '../../../assets/svg';
import { PeopleModal } from '../../../interface';
import { FormContext } from '../../../forms/FormContext';

interface PeopleDropDownProps {
    peopleModal: PeopleModal;
    setPeopleModal: (modal: PeopleModal) => void;
}
const PeopleDropDown = ({ peopleModal, setPeopleModal }: PeopleDropDownProps) => {
    const { form, updateForm } = useContext(FormContext);
    return (
        <View>
            <Text style={styles.label}>Number of People</Text>
            <Pressable
                style={styles.inputWrapper}
                onPress={() => setPeopleModal({ ...peopleModal, visible: !peopleModal.visible })}>
                <Text
                    style={[
                        styles.input,
                        { color: form?.number_of_players?.length > 0 ? colors.lightBlack : colors.darkgray },
                    ]}>
                    {form?.number_of_players ? form?.number_of_players : 'Select'}
                </Text>
                <TouchableOpacity style={{ padding: Spacing.SCALE_10 }}>
                    <DownArrowIcon />
                </TouchableOpacity>
            </Pressable>
            {peopleModal.visible && (
                <View style={styles.clubContainer}>
                    <FlatList
                        data={['1', '2', '3']}
                        showsVerticalScrollIndicator={false}
                        keyExtractor={(item) => item.toString()}
                        renderItem={({ item, index }) => (
                            <Text
                                onPress={() => {
                                    Keyboard.dismiss();
                                    setPeopleModal({
                                        visible: false,
                                        number_of_players: item,
                                    });
                                    updateForm('number_of_players', item);
                                }}
                                style={[
                                    styles.clubText,
                                    {
                                        marginBottom: index === 2 ? 0 : Spacing.SCALE_18,
                                    },
                                ]}>
                                {item}
                            </Text>
                        )}
                        scrollEnabled
                    />
                </View>
            )}
        </View>
    );
};

export default PeopleDropDown;

const styles = StyleSheet.create({
    fieldLabelStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_14,
        fontWeight: '400',
        color: colors.darkGreyRgba,
    },
    label: {
        color: colors.systemMessageText,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_24,
        marginBottom: Spacing.SCALE_8,
    },
    input: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
    },
    inputWrapper: {
        borderBottomWidth: 1,
        borderBottomColor: colors.borderGray,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingBottom: Spacing.SCALE_8,
    },
    clubContainer: {
        backgroundColor: colors.whiteRGB,
        padding: Spacing.SCALE_20,
        borderRadius: Size.SIZE_8,
        zIndex: 50,
        left: 0,
        right: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.23,
        shadowRadius: 2.62,
        elevation: 4,
        maxHeight: Size.SIZE_130,
        borderWidth: 1,
        borderColor: colors.divider,
        top: Spacing.SCALE_1,
    },
    clubText: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        marginBottom: Spacing.SCALE_18,
    },
});
