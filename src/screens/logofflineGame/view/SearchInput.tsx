import { FlatList, StyleSheet, Text, TouchableOpacity, View, Keyboard } from 'react-native';
import React, { useContext, useEffect } from 'react';

import { colors } from '../../../theme/theme';
import { DownArrowIcon } from '../../../assets/svg';
import { Size } from '../../../utils/responsiveUI';
import { Typography } from '../../../utils/responsiveUI';
import { Club } from '../../../interface';
import { Spacing } from '../../../utils/responsiveUI';
import { FormContext } from '../../../forms/FormContext';
import { Pressable } from 'react-native-gesture-handler';

interface SearchInputProps {
    dropdownVisible: boolean;
    setDropdownVisible: (visible: boolean) => void;
    clubs: Club[];
}
const SearchInput = ({ dropdownVisible, setDropdownVisible, clubs }: SearchInputProps) => {
    const { form, updateForm } = useContext(FormContext);
    useEffect(() => {
        if (clubs.length === 1) {
            updateForm('club', clubs[0]);
        }
    }, [clubs]);
    return (
        <View>
            <Text style={styles.label}>Select Golf Club</Text>
            <Pressable
                style={styles.inputWrapper}
                onPress={() => {
                    clubs.length === 1 ? null : setDropdownVisible(!dropdownVisible);
                }}>
                <Text
                    style={[
                        styles.input,
                        { color: form.club || clubs.length === 1 ? colors.lightBlack : colors.darkgray },
                    ]}>
                    {clubs.length === 1 ? clubs[0].name : form.club?.name || 'Enter club name'}
                </Text>
                {clubs.length > 1 && (
                    <TouchableOpacity style={{ padding: Spacing.SCALE_8 }}>
                        <DownArrowIcon />
                    </TouchableOpacity>
                )}
            </Pressable>
            <View style={styles.divider} />
            {dropdownVisible && clubs.length > 0 && (
                <View style={styles.clubContainer}>
                    <FlatList
                        data={clubs}
                        showsVerticalScrollIndicator={false}
                        keyExtractor={(item) => item.id.toString()}
                        renderItem={({ item, index }) => (
                            <Text
                                id={item.id.toString()}
                                onPress={() => {
                                    Keyboard.dismiss();
                                    setDropdownVisible(false);
                                    updateForm('club', item);
                                }}
                                style={[
                                    styles.clubText,
                                    { marginBottom: index === clubs.length - 1 ? 0 : Spacing.SCALE_18 },
                                ]}>
                                {item.name}
                            </Text>
                        )}
                        scrollEnabled
                    />
                </View>
            )}
        </View>
    );
};

export default SearchInput;

const styles = StyleSheet.create({
    fieldLabelStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_14,
        fontWeight: '400',
        color: colors.darkGreyRgba,
    },
    label: {
        color: colors.systemMessageText,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_24,
    },
    input: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
    },
    inputWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: Spacing.SCALE_8,
    },
    clubContainer: {
        marginTop: Spacing.SCALE_60,
        backgroundColor: colors.whiteColor,
        padding: Spacing.SCALE_20,
        borderRadius: Size.SIZE_8,
        position: 'absolute',
        top: Spacing.SCALE_15,
        zIndex: 9999,
        left: 0,
        right: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.23,
        shadowRadius: 2.62,
        elevation: 4,
        maxHeight: Size.SIZE_130,
        borderWidth: 1,
        borderColor: colors.divider,
    },
    clubText: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        marginBottom: Spacing.SCALE_18,
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: colors.borderGray,
        paddingBottom: Spacing.SCALE_10,
    },
});
