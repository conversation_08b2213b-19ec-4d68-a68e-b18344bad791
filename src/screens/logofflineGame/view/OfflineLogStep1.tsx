import { Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext } from 'react';
import { GlobalContext } from '../../../context/contextApi';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';

const OfflineLogStepScreen = () => {
    const { state, actions } = useContext(GlobalContext);
    const options = [
        {
            title: 'Log A Game',
            onPress: () => actions.setOfflineLogGameStep(4),
        },
        {
            title: 'Cancel',
            onPress: () => actions.setOfflineLogGameStep(0),
        },
    ];
    return (
        <>
            {options.map((item, index) => (
                <Pressable
                    onPress={item.onPress}
                    key={index}
                    style={[styles.container, index === 0 && { borderRadius: Size.SIZE_10 }]}>
                    <Text style={styles.buttonText}>{item.title}</Text>
                </Pressable>
            ))}
        </>
    );
};

export default OfflineLogStepScreen;

const styles = StyleSheet.create({
    container: {
        padding: Spacing.SCALE_10,
        backgroundColor: colors.whiteRGB,
        width: '90%',
        alignSelf: 'center',
        marginBottom: Spacing.SCALE_10,
        borderRadius: Spacing.SCALE_14,
        height: Size.SIZE_50,
        justifyContent: 'center',
        alignItems: 'center',
    },
    buttonText: {
        color: colors.Dark_Azure,
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_24,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
});
