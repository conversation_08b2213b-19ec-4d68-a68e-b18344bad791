import showToast from '../../../components/toast/CustomToast';
import { apiServices } from '../../../service/apiServices';

export const withdrawRequest = async (
    userId,
    requestId,
    handleCreatedRequest,
    getContactList = () => {},
    setWithdrawFriendRequestRes = () => {},
) => {
    apiServices
        .withDrawFriendRequest(userId, requestId)
        .then((res) => {
            if (res?.status) {
                setWithdrawFriendRequestRes(res);
                handleCreatedRequest(true);
                getContactList('', 0);
                return res;
            } else {
                showToast({});
            }
        })
        .catch((err) => {
            setLoading(false);
            console.log('Error----->', err);
        });
};
