import React, { useContext } from 'react';
import { Platform, Pressable, StyleProp, StyleSheet, TextInput, TouchableOpacity, View, ViewStyle } from 'react-native';

//context imports
import { GlobalContext } from '../../../context/contextApi';

//utils, theme and assets imports
import { Size, Spacing } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { CrossIcon, SearchIconGrey } from '../../../assets/svg';

interface TGSearchProps {
    searchState: [string, (data: string) => void];
    searchBoxWrapperStyle?: StyleProp<ViewStyle>;
    searchBoxStyle?: StyleProp<ViewStyle>;
    iconStyle?: StyleProp<ViewStyle>;
    setCurrentPage?: () => void;
    setResultsVisible?: () => void;
    handleCrossIcon?: () => void;
    isCrossIconFunctionPassed?: boolean;
    placeholder?: string;
    editable?: boolean;
    onPress?: () => void;
    setShowSearchResult?: (showSearchResult: boolean) => void;
}

const TGSearchNew = ({
    searchState,
    searchBoxWrapperStyle = {},
    searchBoxStyle = {},
    iconStyle = {},
    setCurrentPage = () => {},
    setResultsVisible = () => {},
    handleCrossIcon = () => {},
    isCrossIconFunctionPassed = false,
    placeholder = '',
    editable = true,
    onPress = () => {},
    setShowSearchResult = () => {},
}: TGSearchProps) => {
    const [searchData, setSearchData] = searchState;
    const { actions } = useContext(GlobalContext);

    const handleChangeText = (data: string) => {
        if (data !== ' ') {
            setShowSearchResult(true);
            actions?.setIsMapSearchActive(data?.length ? true : false);
            setSearchData(data);
        }
    };

    return (
        <TouchableOpacity style={[styles.searchBoxWrapper, searchBoxWrapperStyle]} onPress={!editable ? onPress : () => {}} activeOpacity={1}>
            <View style={styles.searchBoxContainer}>
                <View style={styles.searchIconWrapper}>
                    <SearchIconGrey style={styles.searchIconStyle} />
                </View>
                <TextInput
                    placeholder={placeholder || 'Search...'}
                    placeholderTextColor={colors.darkgray}
                    style={[styles.searchBox, searchBoxStyle]}
                    value={searchData}
                    onChangeText={handleChangeText}
                    returnKeyType="go"
                    //@ts-ignore
                    onFocus={() => setResultsVisible(true)}
                    editable={editable}
                    onPress={!editable ? onPress : () => {}}
                />
            </View>
            {searchData?.length > 0 && (
                <Pressable
                    onPress={() => {
                        if (isCrossIconFunctionPassed) {
                            handleCrossIcon();
                        } else {
                            actions?.setIsMapSearchActive(false);
                            setSearchData('');
                        }
                    }}>
                    <CrossIcon style={[styles.crossIconStyle, iconStyle]} height={Size.SIZE_20} width={Size.SIZE_20} />
                </Pressable>
            )}
        </TouchableOpacity>
    );
};

export default TGSearchNew;

const styles = StyleSheet.create({
    searchBoxWrapper: {
        height: Size.SIZE_40,
        borderWidth: Size.SIZE_1,
        borderColor: colors.lightgray,
        marginVertical: Spacing.SCALE_10,
        alignItems: 'center',
        backgroundColor: colors.white,
        borderRadius: Size.SIZE_10,
        marginTop: Spacing.SCALE_10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: Spacing.SCALE_10,
        width: Size.SIZE_340,
    },
    searchBox: {
        marginLeft: Spacing.SCALE_12,
        width: Size.SIZE_250,
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        fontSize: Size.SIZE_12,
    },
    searchIconStyle: {
        marginLeft: Spacing.SCALE_12,
    },
    searchBoxContainer: {
        flexDirection: 'row',
    },
    crossIconStyle: {
        marginRight: Spacing.SCALE_15,
    },
    searchIconWrapper: {
        alignItems: 'center',
        justifyContent: 'center',
    },
});
