import React, { useContext, useEffect, useState } from 'react';
import {
    Modal,
    Pressable,
    StyleSheet,
    TouchableWithoutFeedback,
    View,
    Text,
    TextInput,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';

import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { SIZE_18 } from '../../../utils/responsiveUI/size';
import { FONT_SIZE_11, FONT_SIZE_14 } from '../../../utils/responsiveUI/typography';
import Cross from '../../../assets/svg/cross.svg';
import { AuthContext } from '../../../context/AuthContext';
import MyFriendButton from '../../../components/buttons/MyFriendButton';
import { NOTES_TEXT_LENGTH } from '../../my-TG-Stream-Chat/client';
import { MY_CONTACT, MY_TG_GROUP_LISTING } from '../../../utils/constants/strings';

const DeclineScreenAddPopup = ({ popupState, data, handleAddFriend, screenName = '' }) => {
    const [editNotePopup, setEditNotePopup] = popupState;
    const [loading, setLoading] = useState(false);
    const [phoneNumber, setPhoneNumber] = useState();
    const [requestNote, setRequestNote] = useState('');
    const [counter, setCounter] = useState(NOTES_TEXT_LENGTH);
    const { user } = useContext(AuthContext);

    useEffect(() => {
        setCounter(requestNote?.length);
    }, [requestNote]);

    const handleOnChange = (data) => {
        if (data != ' ' && data?.length <= NOTES_TEXT_LENGTH) {
            setRequestNote(data);
        }
    };

    return (
        <Modal
            animationIn="fadeInUp"
            transparent={true}
            isVisible={editNotePopup}
            customBackdrop={
                <TouchableWithoutFeedback style={{ flex: 1 }}>
                    <View style={{ flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.3)' }} />
                </TouchableWithoutFeedback>
            }
            backdropTransitionOutTiming={1}
            style={{ paddingHorizontal: 0, marginHorizontal: 0, paddingVertical: 0, marginVertical: 0 }}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}
                keyboardShouldPersistTaps="handled">
                <View style={{ flex: 1 }}>
                    <Pressable style={{ flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.3)' }} />
                    <View style={styles.popupWrapper}>
                        <View style={styles.headerWrapper}>
                            <Pressable
                                style={styles.crossIconWrapper}
                                onPress={() => {
                                    setEditNotePopup(false);
                                }}>
                                <Cross />
                            </Pressable>
                        </View>
                        <Text style={styles.popupHeaderText}>Add a Note</Text>
                        <Text style={styles.popupBodyText}>Notes (Optional)</Text>
                        <View style={styles.inputWrapperStyle}>
                            <TextInput
                                placeholder="Type the note here"
                                placeholderTextColor={'rgba(153, 153, 153, 1)'}
                                value={requestNote}
                                style={styles.inputTextStyle}
                                onChangeText={handleOnChange}
                                multiline={true}
                            />
                        </View>
                        {(screenName === MY_CONTACT || screenName === MY_TG_GROUP_LISTING) && (
                            <View style={styles.counterWrapper}>
                                <Text style={styles.counterText}>
                                    {counter}/{NOTES_TEXT_LENGTH}
                                </Text>
                            </View>
                        )}
                        <View style={styles.btnWrapper}>
                            <MyFriendButton
                                text="Search"
                                label="Send Request"
                                loading={loading}
                                btn={{ backgroundColor: 'rgba(9, 128, 137, 1)' }}
                                onPress={() => {
                                    setEditNotePopup(false);
                                    if (screenName === 'MyContact') {
                                        handleAddFriend(data?.tg_contact_id, requestNote);
                                    } else if (screenName === 'Profile Info') {
                                        handleAddFriend(data?.id, requestNote);
                                    } else if (screenName === 'MyTgGroupInfo') {
                                        handleAddFriend(requestNote);
                                    } else if (screenName === 'MyTgGroupListing') {
                                        handleAddFriend(requestNote);
                                    } else {
                                        handleAddFriend(data?.item?.sender_id, requestNote);
                                    }
                                }}
                            />
                        </View>
                    </View>
                </View>
            </KeyboardAvoidingView>
        </Modal>
    );
};
export default DeclineScreenAddPopup;

const styles = StyleSheet.create({
    popupWrapper: {
        width: '100%',
        minHeight: Spacing.SCALE_250,
        position: 'absolute',
        bottom: 0,
        backgroundColor: 'white',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    listHeaderText: {
        fontSize: FONT_SIZE_14,
        color: 'white',
        fontFamily: 'Ubuntu-Medium',
    },
    listHeaderText1: {
        fontSize: FONT_SIZE_11,
        color: 'white',
        fontFamily: 'Ubuntu-Medium',
    },
    logo: {
        width: SIZE_18,
        height: SIZE_18,
        tintColor: 'white',
    },
    popupHeaderText: {
        color: 'rgba(51, 51, 51, 1)',
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '400',
        lineHeight: Size.SIZE_27,
        paddingHorizontal: Spacing.SCALE_18,
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
    },
    headerWrapper: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingTop: Spacing.SCALE_18,
        paddingBottom: Spacing.SCALE_12,
    },
    crossIconWrapper: {
        paddingHorizontal: 20,
        paddingTop: 5,
    },
    popupWrapperData: {
        flexDirection: 'row',
        paddingHorizontal: Spacing.SCALE_20,
        paddingVertical: 20,
    },
    clubIconWrapper: {
        width: Size.SIZE_45,
        height: Size.SIZE_45,
        backgroundColor: '#F2F2F2',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: Size.SIZE_4,
    },
    popupTextWrapper: {
        marginLeft: Spacing.SCALE_13,
        marginTop: Spacing.SCALE_10,
    },
    popupTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    popupBodyText: {
        color: 'rgba(102, 102, 102, 1)',
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        lineHeight: Size.SIZE_14,
        paddingHorizontal: Spacing.SCALE_18,
        marginVertical: Spacing.SCALE_10,
        fontFamily: 'Ubuntu-Medium',
    },
    btnWrapper: {
        paddingHorizontal: 30,
        marginTop: Spacing.SCALE_40,
        marginBottom: Spacing.SCALE_20,
    },
    inputWrapperStyle: {
        borderWidth: 0.4,
        borderColor: 'rgba(153, 153, 153, 1)',
        marginHorizontal: 16,
        paddingHorizontal: 14,
        paddingVertical: Platform.OS === 'ios' ? 14 : 0,
        borderRadius: 4,
    },
    inputTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
        color: 'rgba(51, 51, 51, 1)',
        fontFamily: 'Ubuntu-Medium',
        minHeight: Size.SIZE_80,
        maxHeight: Size.SIZE_150,
    },
    counterWrapper: {
        marginHorizontal: Spacing.SCALE_15,
        alignItems: 'flex-end',
    },
    counterText: {
        color: 'rgba(204, 204, 204, 1)',
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        lineHeight: Size.SIZE_14,
        marginVertical: Spacing.SCALE_5,
        fontFamily: 'Ubuntu-Regular',
    },
});
