import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Linking } from 'react-native';

// Importing SVG icons
import { FaceBookIcon, GreyArrowIcon, LinkedInIcon } from '../../../assets/svg/index';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { SocialButtonProps } from '../../../interface';

const SocialButton: React.FC<SocialButtonProps> = ({ Icon, text, fullWidth, link }) => {
    return (
        <TouchableOpacity
            style={[styles.button, fullWidth && styles.fullWidth]}
            onPress={() => {
                link ? Linking.openURL(link) : null;
            }}>
            <View style={styles.content}>
                <Icon width={24} height={24} />
                <Text style={styles.text}>{text}</Text>
            </View>
            <GreyArrowIcon width={Size.SIZE_18} height={Size.SIZE_18} />
        </TouchableOpacity>
    );
};

interface SocialLinkComponentProps {
    socialLinkTextStyle?: any;
    linkedinLink?: string;
    facebookLink?: string;
}

const SocialLinkComponent: React.FC<SocialLinkComponentProps> = ({ socialLinkTextStyle = {}, linkedinLink, facebookLink }) => {
    const isSingleSocial = facebookLink && linkedinLink ? false : true;
    return (
        <>
            {(facebookLink || linkedinLink) && (
                <Text style={[styles.socialLinkText, socialLinkTextStyle]}>
                    {isSingleSocial ? 'Social Link' : 'Social Links'}
                </Text>
            )}
            <View style={styles.container}>
                {facebookLink && (
                    <SocialButton
                        //@ts-ignore
                        Icon={FaceBookIcon}
                        text="Facebook"
                        fullWidth={isSingleSocial}
                        link={facebookLink}
                    />
                )}
                {linkedinLink && (
                    <SocialButton
                        //@ts-ignore
                        Icon={LinkedInIcon}
                        text="LinkedIn"
                        fullWidth={isSingleSocial}
                        link={linkedinLink}
                    />
                )}
            </View>
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
    },
    singleButtonContainer: {
        justifyContent: 'center',
    },
    button: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: Size.SIZE_10,
        padding: Spacing.SCALE_8,
        justifyContent: 'space-between',
        borderColor: colors.lineDividerColor,
        width: '49%', // Default width when there are two buttons
    },
    fullWidth: {
        width: '100%', // Takes full width when only one button is present
    },
    content: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    text: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
        marginLeft: Spacing.SCALE_4,
    },
    socialLinkText: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        color: colors.darkGreyRgba,
        lineHeight: Size.SIZE_12,
        fontWeight: '500',
        marginBottom: Spacing.SCALE_8,
        marginTop: Spacing.SCALE_10,
    },
});

export default SocialLinkComponent;
