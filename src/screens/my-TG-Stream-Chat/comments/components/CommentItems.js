import React, { useContext, useMemo } from 'react';
import { StyleSheet, Text, View, FlatList, Platform, TouchableOpacity, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';

import { GlobalContext } from '../../../../context/contextApi';
import { AuthContext } from '../../../../context/AuthContext';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';

const CommentCard = React.memo(({ item, state, onUserPress }) => {
    const { user } = useContext(AuthContext);
    return (
        <View style={styles.commentCard}>
            <View style={styles.userInfoContainer}>
                <TouchableOpacity
                    style={styles.userAvatarSection}
                    onPress={() => {
                        if (user?.id === item?.user?.id || item?.user?.deleted_at) return;
                        onUserPress(item.user);
                    }}>
                    {item?.user?.deleted_at ? (
                        <View style={[styles.avatarWrapper, { backgroundColor: colors.lightGrey }]}>
                            <Text style={[styles.avatarText, { color: colors.lightShadeGray }]}>?</Text>
                        </View>
                    ) : item?.user?.image ? (
                        <View style={styles.avatar}>
                            <Image
                                source={{
                                    uri: item?.user?.image,
                                }}
                                style={styles.avatar}
                            />
                        </View>
                    ) : (
                        <View style={styles.avatarWrapper}>
                            {Object.keys(state?.allFriendsId || {})?.includes(item.user?.id) ||
                            item?.user?.id === user?.id ? (
                                <Text style={styles.imageText}>{item?.user?.name?.trim()[0]}</Text>
                            ) : (
                                <Text style={styles.imageText}>{item?.user?.username?.trim()[0]}</Text>
                            )}
                        </View>
                    )}
                    <View style={styles.userTextInfo}>
                        {item?.user?.deleted_at ? (
                            <Text style={[styles.userName, { color: colors.lightShadeGray }]}>Deleted User</Text>
                        ) : (
                            <Text style={styles.userName}>
                                {Object.keys(state?.allFriendsId || {})?.includes(item?.user?.id) ||
                                item?.user?.id === user?.id
                                    ? item?.user?.name
                                    : item?.user?.username}
                                {item?.user?.id === user?.id && <Text style={styles.youText}> (You)</Text>}
                            </Text>
                        )}
                    </View>
                </TouchableOpacity>
            </View>
            <View style={styles.commentContent}>
                <Text style={styles.commentText}>{item.answer_text}</Text>
            </View>
        </View>
    );
});

const CommentItems = ({ pollComments }) => {
    const { state } = useContext(GlobalContext);
    const navigation = useNavigation();
    
    // // Sort comments by created_at in ascending order (oldest first)
    const sortedComments = useMemo(() => {
        return [...pollComments].sort((a, b) => {
            return new Date(a.updated_at) - new Date(b.updated_at);
        });
    }, [pollComments]);
    
    const handleUserPress = (user) => {
        if (user?.id) {
            navigation.navigate('UserProfileScreen', { selectedUser: { id: user.id } });
        }
    };

    const renderItem = ({ item }) => {
        return <CommentCard item={item} state={state} onUserPress={handleUserPress} />;
    };

    const ListEmptyComponent = () => (
        <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No comments yet</Text>
        </View>
    );

    return (
        <FlatList
            data={sortedComments}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            ListEmptyComponent={ListEmptyComponent}
            contentContainerStyle={styles.container}
            showsVerticalScrollIndicator={false}
        />
    );
};

const styles = StyleSheet.create({
    container: {
        flexGrow: 1,
        paddingBottom: Spacing.SCALE_10,
    },
    commentCard: {
        marginBottom: Spacing.SCALE_8,
        padding: Spacing.SCALE_12,
        backgroundColor: colors.white,
        borderRadius: Size.SIZE_12,
        ...Platform.select({
            ios: {
                shadowColor: colors.shadow,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: Platform.OS === 'ios' ? 1 : 3,
            },
            android: {
                elevation: 1,
            },
        }),
    },
    userInfoContainer: {
        marginBottom: Spacing.SCALE_8,
    },
    userAvatarSection: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    avatar: {
        width: Size.SIZE_34,
        height: Size.SIZE_34,
        borderRadius: Size.SIZE_20,
    },
    avatarWrapper: {
        width: Size.SIZE_34,
        height: Size.SIZE_34,
        borderRadius: Size.SIZE_20,
        backgroundColor: colors.tealRgba02,
        justifyContent: 'center',
        alignItems: 'center',
    },
    avatarText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '700',
        color: colors.darkteal,
    },
    userTextInfo: {
        marginLeft: Spacing.SCALE_10,
        flex: 1,
    },
    userName: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    youText: {
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    commentContent: {
        marginTop: Spacing.SCALE_4,
    },
    commentText: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
        lineHeight: Size.SIZE_18,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: Spacing.SCALE_24,
    },
    emptyText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        color: colors.textSecondary,
    },
});

export default React.memo(CommentItems);
