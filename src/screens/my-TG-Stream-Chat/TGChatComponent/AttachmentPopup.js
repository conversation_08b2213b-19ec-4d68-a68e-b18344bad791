import { Alert, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useEffect, useLayoutEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';

import { Size, Spacing } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import GalleryIcon from '../../../assets/svg/gallery.svg';
import CreateEventIcon from '../../../assets/svg/calendar-add.svg';
import OfferIcon from '../../../assets/svg/offer-outline 1.svg';
import PollIcon from '../../../assets/svg/align-left.svg';
import DocumentIcon from '../../../assets/svg/document-text.svg';
import { GlobalContext } from '../../../context/contextApi';
import { StreamChatContext } from '../../../context/StreamChatContext';
import config from '../../../config';
import { canCreateOfferURL, GET_MY_TG_GROUP_DETAIL } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';
import { AuthContext } from '../../../context/AuthContext';
import { MY_TG_GROUP, ONE_TO_ONE, USER_CREATED_GROUP } from '../client';

const AttachmentPopup = ({ route }) => {
    const { toggleAttachmentPicker, openFilePicker, openPollCreationDialog } = route.params;
    const {
        actions,
        state: { updatedChannel: channel },
    } = useContext(GlobalContext);
    const [groupId, setGroupId] = useState(null);
    const { user } = useContext(AuthContext);
    const navigation = useNavigation();
    const [options, setOptions] = useState([
        {
            name: 'Photo & Videos',
            onPress: () => {
                setTimeout(() => {
                    toggleAttachmentPicker();
                }, 1000);
            },
            Icon: GalleryIcon,
        },
        {
            name: 'Document',
            onPress: () => {
                setTimeout(() => {
                    openFilePicker();
                }, 1000);
            },
            Icon: DocumentIcon,
        },
    ]);

    useEffect(() => {
        return () => {
            actions.setIsCrossIconToggle(false);
        };
    }, []);

    useLayoutEffect(() => {
        fetcher({
            endpoint: GET_MY_TG_GROUP_DETAIL,
            method: 'POST',
            body: {
                userId: user?.id,
                groupId: channel?.data?.id,
            },
        }).then((response) => {
            if (response?.status === 1) {
                setGroupId(response?.id);
            }
        });
        if (channel?.data?.type === MY_TG_GROUP) {
            let additionalOption = [
                {
                    name: 'Create Event',
                    onPress: () => {
                        setTimeout(() => {
                            navigation.navigate(config.routes.CREATE_NEW_EVENTS, {
                                type: 'Chat',
                                groupName: channel?.data?.name,
                                groupId: channel?.data?.id,
                            });
                        }, 1000);
                    },
                    Icon: OfferIcon,
                },
                {
                    name: 'Create Offer',
                    onPress: () => {
                        setTimeout(() => {
                            handleCreateOffer();
                        }, 1000);
                    },
                    Icon: CreateEventIcon,
                },
            ];
            setOptions((prev) => [...prev, ...additionalOption]);
        }
        if (channel?.data?.type !== ONE_TO_ONE)
            setOptions((prev) => [
                ...prev,
                {
                    name: 'Create Poll',
                    onPress: () => {
                        setTimeout(() => {
                            navigation.navigate(config.routes.CREATE_POLL);
                        }, 1000);
                    },
                    Icon: PollIcon,
                },
            ]);
    }, [channel]);

    const handleCreateOffer = () => {
        const canCreateOffer = () => {
            fetcher({
                endpoint: canCreateOfferURL,
                method: 'POST',
                body: {
                    user_id: user?.id,
                },
            }).then((res) => {
                if (res?.canCreate) {
                    navigation.navigate(config.routes.CREATE_OFFER_SCREEN, {
                        createFrom: 'Chat',
                        my_tg_group_id: groupId,
                        my_tg_group: channel?.data,
                    });
                } else Alert.alert('', res?.message);
            });
        };
        canCreateOffer();
    };

    return (
        <View style={styles.container}>
            <Pressable style={styles.popupContainer} onPress={() => navigation.pop(1)}>
                <View style={styles.popupWrapper}>
                    {options.map((item, index) => {
                        return (
                            <TouchableOpacity
                                onPress={() => {
                                    navigation.pop(1);
                                    if (item.name === 'Create Offer') return handleCreateOffer();
                                    else item.onPress();
                                }}
                                style={styles.optionsStyle}
                                key={index}>
                                <item.Icon />
                                <Text>{item.name}</Text>
                            </TouchableOpacity>
                        );
                    })}
                </View>
            </Pressable>
        </View>
    );
};

export default AttachmentPopup;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    popupContainer: {
        flex: 1,
        // backgroundColor: colors.transparentRgba,
        padding: Spacing.SCALE_16,
        zIndex: 1,
    },
    popupWrapper: {
        width: Size.SIZE_175,
        backgroundColor: colors.white,
        borderRadius: Size.SIZE_8,
        position: 'absolute',
        bottom: Spacing.SCALE_70,
        left: Spacing.SCALE_20,
        zIndex: 100,
        paddingVertical: Spacing.SCALE_17,
        boxShadow: ' 0px 4px 10px rgba(0, 0, 0, 0.09)',
    },
    optionsStyle: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: Spacing.SCALE_7,
        paddingHorizontal: Spacing.SCALE_14,
        columnGap: Spacing.SCALE_9,
    },
});
