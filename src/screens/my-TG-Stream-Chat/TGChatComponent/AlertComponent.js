import { Alert } from "react-native"

export const handleOnPress = (channel, handlePress, alertBoxText, btnText, navigation) => {
    navigation.navigate('DeleteChannelConfirmationPopup', {
        handleYesButton: async () => {
            await handlePress();
        },
        popupHeader: btnText,
        popupSubText: alertBoxText,
        firstBtnLabel: 'Cancel',
        secondBtnLabel: btnText,
    });
}
export const handleOnPress1 = (channel, handlePress) => {
    let alertBoxText = (channel?.data?.blockedBy === null || channel?.data?.blockedBy === undefined) ? 'Are you sure you want to block this user?' : 'Are you sure you want to unblock this user?'
    let btnText = (channel?.data?.blockedBy === null || channel?.data?.blockedBy === undefined) ? 'Block' : 'Unblock'
    Alert.alert('',
        `${alertBoxText}`,
        [
            {
                text: 'Cancel',
                onPress: () => {},
            },
            {
                text: `${btnText}`,
                onPress: () => handlePress(),
            }
        ],
        { cancelable: false },
    )
}