import { useNavigation } from '@react-navigation/native';
import { useContext, useState } from 'react';
import Animated, { useSharedValue, withTiming, useAnimatedStyle } from 'react-native-reanimated';
import { Pressable, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';

import { Size, Spacing } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { MessageInputContext } from 'stream-chat-react-native';
import { GlobalContext } from '../../../context/contextApi';
import config from '../../../config';

const CustomAttachButton = () => {
    const navigation = useNavigation();
    const { toggleAttachmentPicker, openFilePicker, openPollCreationDialog } = useContext(MessageInputContext);
    const { state, actions } = useContext(GlobalContext);

    const scale = useSharedValue(1);
    const opacityPlus = useSharedValue(1);
    const opacityCross = useSharedValue(1);

    const toggleIcon = () => {
        navigation.push(config.routes.ATTACHMENT_POPUP, { toggleAttachmentPicker, openFilePicker, openPollCreationDialog });
        actions.setIsCrossIconToggle(true);
        scale.value = withTiming(state?.isCrossIconToggle ? 1.2 : 1, { duration: 200 });
        opacityPlus.value = withTiming(state?.isCrossIconToggle ? 0 : 1, { duration: 200 });
        opacityCross.value = withTiming(state?.isCrossIconToggle ? 1 : 0, { duration: 200 });
    };

    const plusStyle = useAnimatedStyle(() => ({
        opacity: opacityPlus.value,
        transform: [{ scale: scale.value }],
    }));

    const crossStyle = useAnimatedStyle(() => ({
        opacity: opacityCross.value,
        transform: [{ scale: scale.value }],
    }));

    return (
        <Pressable
            style={styles.iconWrapper}
            onPress={() => {
                toggleIcon();
            }}>
            {!state?.isCrossIconToggle ? (
                <Animated.View style={[styles.icon, plusStyle]}>
                    <Icon name="pluscircleo" size={Size.SIZE_24} color={colors.systemMessageText} />
                </Animated.View>
            ) : (
                <Animated.View style={[styles.icon, crossStyle]}>
                    <Icon name="closecircle" size={Size.SIZE_24} color={colors.tealRgb} />
                </Animated.View>
            )}
        </Pressable>
    );
};

export default CustomAttachButton;

const styles = StyleSheet.create({
    iconWrapper: {
        justifyContent: 'center',
        alignItems: 'flex-end',
        height: Size.SIZE_40,
        width: Size.SIZE_40,
    },
    icon: {
        position: 'absolute',
    },
});
