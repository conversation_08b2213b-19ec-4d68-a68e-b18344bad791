import { memo, useState, useCallback, useEffect, useContext } from 'react';
import {
    StyleSheet,
    View,
    Text,
    TextInput,
    ScrollView,
    TouchableOpacity,
    Switch,
    ActivityIndicator,
    KeyboardAvoidingView,
    TouchableWithoutFeedback,
    Keyboard,
    FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
const CleverTap = require('clevertap-react-native');

import StreamHeader from '../../../components/layout/StreamHeader';
import TGLoader from '../../../components/layout/TGLoader';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import DraggableFlatList, { OpacityDecorator, ScaleDecorator, ShadowDecorator } from 'react-native-draggable-flatlist';
import DotGraph from '../../../assets/svg/DotGraph.svg';
import CrossIcon from '../../../assets/svg/cross.svg';
import { StreamChatContext } from '../../../context/StreamChatContext';
import StreamToggleButton from '../../../components/buttons/StreamToggleButton';
import showToast from '../../../components/toast/CustomToast';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import constants from '../../../utils/constants/constants';
import { AuthContext } from '../../../context/AuthContext';

const CreatePoll = memo(() => {
    const navigation = useNavigation();
    const { client, channel } = useContext(StreamChatContext);
    const { user } = useContext(AuthContext);
    const [loading, setLoading] = useState(false);

    const [question, setQuestion] = useState('');
    const [options, setOptions] = useState([
        { id: '1', text: '' },
        { id: '2', text: '' },
    ]);
    const [multipleAnswers, setMultipleAnswers] = useState(false);
    const [addComment, setAddComment] = useState(false);

    const validateForm = useCallback(() => {
        let isValid = true;

        // Validate question
        if (question.trim() === '') {
            isValid = false;
        }

        // Validate options
        const validOptions = options.filter((opt) => opt.text.trim() !== '');
        if (validOptions.length < 2) {
            isValid = false;
        }

        return isValid;
    }, [question, options]);

    const updateOption = useCallback((text, id) => {
        setOptions((currentOptions) => {
            const newOptions = currentOptions.map((option) => (option.id === id ? { id: option.id, text } : option));

            // Find empty options
            const emptyOptions = newOptions.filter((option) => option.text.trim() === '');

            // If there's more than one empty option and we have more than 2 options total
            if (emptyOptions.length > 1 && newOptions.length > 2) {
                // Remove the last empty option, keeping at least 2 options
                const finalOptions = newOptions.filter((option, index) => {
                    if (option.text.trim() !== '') return true;
                    const emptyIndex = newOptions.filter((opt, idx) => idx < index && opt.text.trim() === '').length;
                    return emptyIndex === 0;
                });
                return finalOptions.length >= 2 ? finalOptions : newOptions;
            }

            // Check if all options have text and we're under the limit
            const allOptionsFilled = newOptions.every((option) => option.text.trim() !== '');
            if (allOptionsFilled && newOptions.length < 10) {
                return [...newOptions, { id: Date.now().toString(), text: '' }];
            }

            return newOptions;
        });
    }, []);

    const removeOption = useCallback(
        (id) => {
            if (options.length > 2) {
                setOptions((current) => {
                    const newOptions = current.filter((option) => option.id !== id);
                    return newOptions.length >= 2 ? newOptions : current;
                });
            }
        },
        [options.length],
    );

    const handleCreatePoll = useCallback(async () => {
        if (loading) {
            return;
        }
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const validOptions = options
                .filter((opt) => opt.text.trim() !== '')
                .map((opt) => ({
                    text: opt.text.trim(),
                }));

            const pollInstance = await client.polls.createPoll({
                name: question.trim(),
                options: validOptions,
                allow_answers: addComment,
                enforce_unique_vote: !multipleAnswers,
            });

            await channel.sendMessage({
                text: question.trim(),
                poll_id: pollInstance.id,
            });
            CleverTap.recordEvent(constants.CLEVERTAP.CREATE_POLLS, {
                'Poll Created': pollInstance.id ? 'Success' : 'Failed',
                'User email': user?.email,
                'Group Name': channel?.data?.name,
                'Group member count': channel?.data?.member_count,
                'Poll options': validOptions.map((opt) => opt.text).join(', '),
                Comments: addComment ? 'On' : 'Off',
                'Answer type': multipleAnswers ? 'multi-select' : 'single-select',
            });

            navigation.goBack();
        } catch (error) {
            showToast({ message: error?.message?.split(':')[2] });
        } finally {
            setLoading(false);
        }
    }, [question, options, multipleAnswers, addComment, navigation, validateForm]);

    const renderItem = ({ item }) => {
        const showRemoveButton = options.length > 2;

        return (
            <View
                // disabled={isActive}
                style={styles.optionRow}>
                <DotGraph height={Size.SIZE_20} width={Size.SIZE_8} />
                <TextInput
                    style={styles.optionInput}
                    placeholder="+ Add option"
                    value={item.text}
                    onChangeText={(text) => updateOption(text, item.id)}
                    maxLength={100}
                    multiline={true}
                    placeholderTextColor={colors.darkGreyRgba}
                />
                {showRemoveButton && (
                    <TouchableOpacity style={styles.removeButton} onPress={() => removeOption(item.id)}>
                        <CrossIcon height={Size.SIZE_12} width={Size.SIZE_12} />
                    </TouchableOpacity>
                )}
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <StreamHeader screenName={'Create Poll'} headerAligned={'left'} />
            <KeyboardAwareScrollView style={styles.scrollContainer}>
                <View>
                    <Text style={styles.label}>Question</Text>
                    <TextInput
                        style={[styles.input]}
                        placeholder="Ask a question"
                        maxLength={250}
                        value={question}
                        onChangeText={(text) => {
                            setQuestion(text);
                        }}
                        placeholderTextColor={colors.darkgray}
                        multiline={true}
                    />
                </View>

                <View>
                    <Text style={styles.label}>Options</Text>
                    {options?.map((item) => {
                        return renderItem({ item });
                    })}
                </View>

                {/* Toggle Switches */}
                <View style={styles.toggleRow}>
                    <View>
                        <Text style={styles.label_2}>Multiple Answers</Text>
                        <Text style={styles.subText}>If turned on, users can vote multiple choice.</Text>
                    </View>
                    <StreamToggleButton
                        name="multipleAnswers"
                        disabled={false}
                        isToggled={multipleAnswers}
                        onPress={() => {
                            setMultipleAnswers(!multipleAnswers);
                        }}
                    />
                </View>
                <View style={styles.toggleRow}>
                    <View style={styles.toggleText}>
                        <Text style={styles.label_2}>Add Comment</Text>
                        <Text style={styles.subText}>
                            If turned on then other people will be able to add comment on the poll.
                        </Text>
                    </View>
                    <StreamToggleButton
                        name="addComment"
                        disabled={false}
                        isToggled={addComment}
                        onPress={() => {
                            setAddComment(!addComment);
                        }}
                    />
                </View>
            </KeyboardAwareScrollView>
            <View style={styles.buttonContainer}>
                <TouchableOpacity style={styles.cancelButton} onPress={() => navigation.goBack()}>
                    <Text style={styles.cancelText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.createButton,
                        (!question.trim() || options.filter((opt) => opt.text.trim() !== '').length < 2) &&
                            styles.disabled,
                    ]}
                    onPress={handleCreatePoll}>
                    {loading ? (
                        <ActivityIndicator color={colors.white} />
                    ) : (
                        <Text style={styles.createText}>Create</Text>
                    )}
                </TouchableOpacity>
            </View>
        </View>
    );
});

export default CreatePoll;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
    },
    scrollContainer: {
        flex: 1,
        backgroundColor: colors.greyRgba,
        paddingTop: Spacing.SCALE_24,
        paddingHorizontal: Spacing.SCALE_16,
    },
    label: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: 'bold',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        paddingBottom: Spacing.SCALE_10,
    },
    label_2: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: 'bold',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        paddingBottom: Spacing.SCALE_4,
    },
    subText: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: '#808485',
    },
    input: {
        flex: 1,
        padding: 0,
        margin: 0,
        borderBottomWidth: 1,
        borderColor: colors.borderGray,
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        minHeight: 25,
        paddingBottom: Spacing.SCALE_4,
        marginBottom: Size.SIZE_20,
    },
    inputError: {
        borderColor: colors.error,
    },
    textContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: Spacing.SCALE_4,
        marginBottom: Spacing.SCALE_16,
    },
    errorContainer: {
        flex: 1,
    },
    errorText: {
        color: colors.error,
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        flex: 1,
    },
    optionsErrorText: {
        marginBottom: Spacing.SCALE_8,
    },
    characterCount: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        color: colors.fadeBlack,
    },
    optionRow: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: Size.SIZE_8,
        borderBottomWidth: 1,
        borderColor: colors.borderGray,
        marginBottom: Spacing.SCALE_20,
        paddingTop: Spacing.SCALE_8,
        paddingBottom: Spacing.SCALE_4,
    },
    optionInput: {
        flex: 1,
        marginHorizontal: Spacing.SCALE_8,
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        padding: 0,
    },
    dragging: {
        backgroundColor: colors.greyRgba,
        elevation: 3,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    removeButton: {
        marginRight: Spacing.SCALE_8,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: Spacing.SCALE_20,
        marginBottom: Spacing.SCALE_20,
        alignContent: 'flex-end',
        paddingHorizontal: Spacing.SCALE_16,
    },
    cancelButton: {
        padding: Spacing.SCALE_12,
        borderRadius: Size.SIZE_8,
        flex: 1,
        marginRight: Spacing.SCALE_8,
        borderWidth: 1,
        borderColor: colors.gray99,
    },
    cancelText: {
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
    },
    createButton: {
        padding: Spacing.SCALE_12,
        backgroundColor: colors.darkteal,
        borderRadius: Size.SIZE_8,
        flex: 1,
    },
    disabled: {
        backgroundColor: colors.gray99,
    },
    createText: {
        color: colors.white,
        fontFamily: 'Ubuntu-Medium',

        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
    },
    toggleText: {
        flex: 1,
        flexDirection: 'column',
    },
    toggleRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: Spacing.SCALE_24,
    },
});
