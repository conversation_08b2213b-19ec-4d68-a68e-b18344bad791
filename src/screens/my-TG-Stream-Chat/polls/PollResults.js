import React, { useContext, useMemo, memo, useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, BackHandler } from 'react-native';
import { useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import StreamHeader from '../../../components/layout/StreamHeader';
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { StreamChatContext } from '../../../context/StreamChatContext';
import VotesItems from './components/VotesItems';
import CommentItems from '../comments/components/CommentItems';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import LinearGradient from 'react-native-linear-gradient';

const TABS = {
    VOTES: 'Votes',
    COMMENTS: 'Comments',
};

const PollShimmer = () => (
    <View style={styles.shimmerContainer}>
        {/* Question Shimmer */}
        <ShimmerPlaceholder LinearGradient={LinearGradient} style={styles.questionShimmer} />
        <ShimmerPlaceholder LinearGradient={LinearGradient} style={styles.subTextShimmer} />

        {/* Tab Buttons Shimmer */}
        <View style={styles.optionsContainer}>
            {[1, 2].map((item) => (
                <ShimmerPlaceholder key={item} LinearGradient={LinearGradient} style={styles.tabShimmer} />
            ))}
        </View>

        {/* Vote Items Shimmer */}
        {[1, 2, 3, 4].map((item) => (
            <View key={item} style={styles.voteItemShimmer}>
                <ShimmerPlaceholder LinearGradient={LinearGradient} style={styles.avatarShimmer} />
                <View style={styles.voteTextContainer}>
                    <ShimmerPlaceholder LinearGradient={LinearGradient} style={styles.nameShimmer} />
                    <ShimmerPlaceholder LinearGradient={LinearGradient} style={styles.timeShimmer} />
                </View>
            </View>
        ))}
    </View>
);

const PollResults = () => {
    const route = useRoute();
    const navigation = useNavigation();
    const { pollData, tab = 'Votes' } = route.params || { pollData };
    const { client } = useContext(StreamChatContext);
    const [activeTab, setActiveTab] = useState(tab);
    const [pollVotes, setPollVotes] = useState([]);
    const [pollComments, setPollComments] = useState([]);
    const [loading, setLoading] = useState(true);

    // Handle hardware back button
    useFocusEffect(
        useCallback(() => {
            const onBackPress = () => {
                navigation.goBack();
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () => {
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
            };
        }, [navigation])
    );

    const getPollVotes = useCallback(async () => {
        try {
            setLoading(true);
            const response = await client.queryPollVotes(pollData?.id, { is_answer: false });
            setPollVotes(response?.votes || []);
        } catch (error) {
            console.log('Error fetching poll votes:', error);
        } finally {
            setLoading(false);
        }
    }, [client, pollData?.id]);

    const getPollComments = useCallback(async () => {
        try {
            setLoading(true);
            const response = await client.queryPollVotes(pollData?.id, { is_answer: true });
            setPollComments(response?.votes || []);
        } catch (error) {
            console.log('Error fetching poll comments:', error);
        } finally {
            setLoading(false);
        }
    }, [client, pollData?.id]);

    useEffect(() => {
        getPollVotes();
        getPollComments();
    }, [getPollVotes]);

    const renderContent = () => {
        switch (activeTab) {
            case TABS.VOTES:
                return <VotesItems poll={pollData} votesMap={votesMap} />;
            case TABS.COMMENTS:
                return (
                    // Render comments content here
                    <CommentItems poll={pollData} pollComments={pollComments} />
                );
            default:
                return null;
        }
    };

    const votesMap = useMemo(() => {
        return pollVotes?.reduce((acc, vote) => {
            if (!acc[vote.option_id]) {
                acc[vote.option_id] = [];
            }
            acc[vote.option_id].push({
                userId: vote.user_id,
                user: vote.user,
                createdAt: vote.created_at,
                voteId: vote.id,
            });
            return acc;
        }, {});
    }, [pollVotes]);

    const uniqueVotersCount = useMemo(() => {
        const uniqueVoters = new Set(pollVotes?.map((vote) => vote.user_id));
        return uniqueVoters.size;
    }, [pollVotes]);

    return (
        <View style={styles.container}>
            <StreamHeader screenName="Poll Results" headerAligned="left" />
            <View style={styles.scrollContainer}>
                <View style={styles.content}>
                    <Text style={styles.question} numberOfLines={3}>
                        {pollData?.name}
                    </Text>
                    {loading ? (
                        <PollShimmer />
                    ) : (
                        <>
                            <Text style={styles.totalVotes}>
                                {`${uniqueVotersCount} members voted & ${pollComments?.length} commented`}
                            </Text>

                            {/* Toggle Button Group */}
                            <View style={styles.optionsContainer}>
                                {Object.values(TABS).map((tab) => (
                                    <TouchableOpacity
                                        key={tab}
                                        style={[
                                            styles.toggleButtonContainer,
                                            activeTab !== tab && styles.inactiveButton,
                                        ]}
                                        onPress={() => setActiveTab(tab)}>
                                        <Text style={[styles.toggleBtnText, activeTab !== tab && styles.inactiveText]}>
                                            {tab}
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </View>

                            {/* Content based on active tab */}
                            <View style={styles.tabContent}>{renderContent()}</View>
                        </>
                    )}
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
    },
    scrollContainer: {
        flex: 1,
        backgroundColor: colors.lightgray,
    },
    content: {
        flex: 1,
        paddingVertical: Spacing.SCALE_20,
        paddingHorizontal: Spacing.SCALE_16,
    },

    question: {
        fontSize: Typography.FONT_SIZE_18,
        fontWeight: '500',
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_8,
        lineHeight: Typography.SCALE_24,
    },
    totalVotes: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        color: colors.fadeBlack,
        marginBottom: Spacing.SCALE_20,
    },
    optionsContainer: {
        gap: Spacing.SCALE_10,
        flexDirection: 'row',
        marginBottom: Spacing.SCALE_20,
    },
    toggleButtonContainer: {
        flex: 1,
        backgroundColor: colors.darkteal,
        padding: Spacing.SCALE_8,
        borderRadius: Size.SIZE_40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    inactiveButton: {
        backgroundColor: colors.lightGrey,
        borderWidth: 1,
        borderColor: colors.darkgray,
    },
    toggleBtnText: {
        color: colors.white,
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '500',
    },
    inactiveText: {
        color: colors.lightBlack,
    },
    tabContent: {
        flex: 1,
        marginTop: Spacing.SCALE_12,
    },
    shimmerContainer: {
        flex: 1,
        padding: Spacing.SCALE_16,
        backgroundColor: colors.white,
    },
    questionShimmer: {
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_4,
        marginBottom: Spacing.SCALE_8,
    },
    subTextShimmer: {
        width: '60%',
        height: Size.SIZE_16,
        borderRadius: Size.SIZE_4,
        marginBottom: Spacing.SCALE_20,
    },
    tabShimmer: {
        flex: 1,
        height: Size.SIZE_36,
        borderRadius: Size.SIZE_40,
    },
    voteItemShimmer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: Spacing.SCALE_8,
    },
    avatarShimmer: {
        width: Size.SIZE_40,
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_20,
        marginRight: Spacing.SCALE_12,
    },
    voteTextContainer: {
        flex: 1,
    },
    nameShimmer: {
        height: Size.SIZE_16,
        width: '40%',
        borderRadius: Size.SIZE_4,
        marginBottom: Spacing.SCALE_4,
    },
    timeShimmer: {
        height: Size.SIZE_12,
        width: '20%',
        borderRadius: Size.SIZE_4,
    },
});

export default memo(PollResults);
