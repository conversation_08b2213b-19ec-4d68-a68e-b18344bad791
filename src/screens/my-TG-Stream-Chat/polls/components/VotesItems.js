import React, { useMemo, useContext, useCallback } from 'react';
import { StyleSheet, Text, View, FlatList, Platform, Pressable, TouchableOpacity, Image } from 'react-native';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';
import { GlobalContext } from '../../../../context/contextApi';
import { useNavigation } from '@react-navigation/native';
import { AuthContext } from '../../../../context/AuthContext';

const VoteCard = React.memo(({ item, state, onClickUser = () => {}, poll, votesMap }) => {
    const { user } = useContext(AuthContext);

    return (
        <View style={styles.voteCard} key={item.option}>
            <View
                style={[
                    styles.headerContainer,
                    poll?.latest_votes_by_option[item?.id]?.length > 0 && styles.showBottomLine,
                ]}>
                <Text style={styles.optionText}>{item.text}</Text>
                <Text style={styles.votesCount}>
                    {`${votesMap?.[item?.id]?.length ?? 0} ${
                        (votesMap?.[item?.id]?.length ?? 0) <= 1 ? 'vote' : 'votes'
                    }`}
                </Text>
            </View>
            {votesMap[item?.id]?.length > 0 &&
                votesMap[item?.id]?.map((vote) => (
                    <TouchableOpacity
                        key={vote?.user?.id}
                        style={styles.userRow}
                        onPress={() => {
                            if (user?.id === vote?.user?.id) return;
                            onClickUser(vote?.user);
                        }}>
                        {vote?.user?.image ? (
                            <View style={styles.imageContainer}>
                                <Image
                                    source={{
                                        uri: vote?.user?.image,
                                    }}
                                    style={styles.image}
                                />
                            </View>
                        ) : (
                            <View style={styles.imageWrapper}>
                                {Object.keys(state?.allFriendsId || {})?.includes(vote?.user?.id) ||
                                vote?.user?.id === user?.id ? (
                                    <Text style={styles.imageText}>{vote?.user?.name?.trim()[0]}</Text>
                                ) : (
                                    <Text style={styles.imageText}>{vote?.user?.username?.trim()[0]}</Text>
                                )}
                            </View>
                        )}
                        <Text style={styles.userName}>
                            {Object.keys(state?.allFriendsId || {})?.includes(vote?.user?.id) ||
                            vote?.user?.id === user?.id
                                ? vote?.user?.name
                                : vote?.user?.username}
                            {vote?.user?.id === user?.id && <Text style={styles.youText}> (You)</Text>}
                        </Text>
                    </TouchableOpacity>
                ))}
        </View>
    );
});

const VotesItems = ({ poll, votesMap }) => {
    const { state } = useContext(GlobalContext);
    const navigation = useNavigation();

    const onClickUser = (user) => {
        if (user?.id) {
            navigation.navigate('UserProfileScreen', { selectedUser: { id: user?.id } });
        }
    };

    const renderItem = ({ item }) => <VoteCard item={item} state={state} onClickUser={onClickUser} poll={poll} votesMap={votesMap} />;

    const keyExtractor = (item) => item.option;

    const ListEmptyComponent = () => (
        <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No votes yet</Text>
        </View>
    );

    return (
        <FlatList
            data={poll.options}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            ListEmptyComponent={ListEmptyComponent}
            contentContainerStyle={styles.container}
            showsVerticalScrollIndicator={false}
        />
    );
};

const styles = StyleSheet.create({
    container: {
        flexGrow: 1,
        paddingBottom: Spacing.SCALE_10,
    },
    voteCard: {
        marginBottom: Spacing.SCALE_8,
        padding: Spacing.SCALE_12,
        backgroundColor: colors.white,
        borderRadius: Size.SIZE_12,
        ...Platform.select({
            ios: {
                shadowColor: colors.shadow,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: Platform.OS === 'ios' ? 1 : 3.84,
            },
            android: {
                elevation: 1,
            },
        }),
    },
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_4,
    },
    showBottomLine: {
        borderBottomWidth: 1,
        borderColor: colors.lightgray,
        paddingBottom: Spacing.SCALE_12,
    },
    optionText: {
        flex: 1,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    votesCount: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        color: colors.darkteal,
    },
    userRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: Spacing.SCALE_12,
    },
    imageContainer: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_16,
        marginRight: Spacing.SCALE_8,
        overflow: 'hidden',
    },
    image: {
        width: '100%',
        height: '100%',
    },
    imageWrapper: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_16,
        backgroundColor: colors.tealRgba02,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: Spacing.SCALE_8,
    },
    imageText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        color: colors.darkteal,
    },
    userName: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
    },
    youText: {
        color: colors.textSecondary,
        fontFamily: 'Ubuntu-Regular',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: Spacing.SCALE_24,
    },
    emptyText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        color: colors.textSecondary,
    },
});

export default React.memo(VotesItems);
