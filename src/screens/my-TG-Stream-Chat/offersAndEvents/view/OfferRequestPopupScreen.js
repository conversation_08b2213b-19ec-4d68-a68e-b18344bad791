import React, { useContext, useState, useLayoutEffect } from 'react';
import { Pressable, StyleSheet, TouchableWithoutFeedback, View, Text, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment';

import Cross from '../../../../assets/svg/cross.svg';
import GiftBoxIconTeal from '../../../../assets/svg/GiftBoxIconTeal.svg';
import { colors } from '../../../../theme/theme';
import { getOfferDetails } from '../action/getOfferDetails';
import { AuthContext } from '../../../../context/AuthContext';
import { OFFER_CREATION_NOTE } from '../../../../utils/constants/strings';
import ShowToolTip from '../../../myGolfClub/ToolTip';
import MyFriendButton from '../../../../components/buttons/MyFriendButton';
import TGLoader from '../../../../components/layout/TGLoader';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { tokenCheckURL } from '../../../../service/EndPoint';
import { fetcher } from '../../../../service/fetcher';
import CheckNew from '../../../../assets/svg/checkNew.svg';
import dateFormatter from '../../../../utils/helpers/dateFormatter';

const OfferRequestPopupScreen = ({ route }) => {
    const { id } = route?.params;
    const { user } = useContext(AuthContext);
    // Identify that selected offer is mine or not
    const [isMyOffer, setIsMyOffer] = useState();
    const [offerDetails, setOfferDetails] = useState(null);
    const [toolTip, setToolTip] = useState(false);
    const [loading, setLading] = useState({
        screenLoader: false,
        btnLoader: false,
    });
    const [isMyClubOffer, setIsMyClubOffer] = useState(false);
    const navigation = useNavigation();

    useLayoutEffect(() => {
        handleGetOfferDetails();
    }, []);

    const checkIsMyClubOffer = (offerDetails) => {
        setIsMyClubOffer(
            user?.clubs?.some(
                (club) => club?.club?.name?.trim().toLowerCase() === offerDetails?.club_name?.trim().toLowerCase(),
            ),
        );
    };

    // Check user can create request or not
    const matchTokenActive = () => {
        fetcher({
            endpoint: tokenCheckURL,
            method: 'POST',
            body: {
                user_id: user?.id,
            },
        }).then((res) => {
            if (res?.canCreate) checkRequest();
            else Alert.alert('', res?.message);
        });
    };

    // Navigate user to create request against offer
    const checkRequest = () => {
        navigation.goBack();
        navigation.navigate('Request Against Offer', {
            offer: offerDetails,
        });
    };

    const handleGetOfferDetails = async () => {
        setLading((prev) => ({ ...prev, screenLoader: true }));
        const payload = {
            userId: user?.id,
            offerId: id,
        };
        const offerDetailsRes = await getOfferDetails(payload);
        if (offerDetailsRes?.status) {
            checkIsMyClubOffer(offerDetailsRes?.data);
            setIsMyOffer(offerDetailsRes?.data?.user_id == user?.id);
            setLading((prev) => ({ ...prev, screenLoader: false }));
            setOfferDetails(offerDetailsRes?.data);
        } else {
            setLading((prev) => ({ ...prev, screenLoader: false }));
            Alert.alert(
                '404',
                'The resource you were trying to find does not exist or has been deleted.',
                [
                    {
                        text: 'OK',
                        onPress: () => navigation.goBack(),
                    },
                ],
                { cancelable: false },
            );
        }
    };

    return (
        <>
            <View style={{ flex: 1 }}>
                <Pressable style={{ flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.3)' }} />
                {/* Popup body style */}
                {!loading?.screenLoader && (
                    <View style={styles.popupWrapper}>
                        <View style={styles.headerWrapper}>
                            {/* Cross button */}
                            <Pressable
                                style={styles.crossIconWrapper}
                                onPress={() => {
                                    navigation.goBack();
                                }}>
                                <Cross />
                            </Pressable>
                        </View>
                        {/* Popup sub body UI */}
                        <View style={styles.subBody}>
                            <View style={styles.svgIconWrapper}>
                                <GiftBoxIconTeal width={30} height={30} />
                            </View>
                            <Text style={styles.offerIdText}>Offer #{offerDetails?.offer_id}</Text>
                            <View style={styles.clubNameWrapper}>
                                <Text style={styles.clubTextStyle}>Club</Text>
                                <Text style={styles.clubNameText}>{offerDetails?.club_name}</Text>
                            </View>
                            <View style={styles.clubNameWrapper}>
                                <Text style={styles.clubTextStyle}>Date Range</Text>
                                <Text style={styles.clubNameText}>
                                    {dateFormatter(moment.utc(offerDetails?.start_date), moment.utc(offerDetails?.end_date))}
                                </Text>
                            </View>
                            {offerDetails?.details && (
                                <View style={styles.clubNameWrapper}>
                                    <Text style={styles.clubTextStyle}>Additional Notes</Text>
                                    <View style={styles.additionalNotesWrapper}>
                                        <Text style={styles.additionalNotes}>{offerDetails?.details}</Text>
                                    </View>
                                </View>
                            )}
                            {offerDetails?.isSingleGroupOffer ? (
                                <View
                                    style={[
                                        styles.notesStyle,
                                        {
                                            marginBottom: isMyOffer ? Spacing.SCALE_20 : 0,
                                        },
                                    ]}>
                                    <View style={styles.toolTipWrapper}>
                                        <ShowToolTip
                                            show={toolTip}
                                            description={OFFER_CREATION_NOTE}
                                            iconColor={colors.darkteal}
                                            posiition="bottom"
                                            iconSize={14}
                                            marginLeft={5}
                                        />
                                    </View>
                                    <Text style={styles.text}>
                                        {`Offer created for: Members of ${offerDetails?.group_name}`}
                                    </Text>
                                </View>
                            ) : null}
                            {/* If selected offer is mine then create request against offer button will not visible */}
                            {!isMyOffer && !isMyClubOffer ? (
                                !offerDetails?.requested ? (
                                    <MyFriendButton
                                        text="Request Against Offer"
                                        label="Request Against Offer"
                                        loading={loading?.btnLoader}
                                        btn={styles.btn}
                                        onPress={matchTokenActive}
                                    />
                                ) : (
                                    <View style={styles.requestedBtnWrapper}>
                                        <CheckNew />
                                        <Text style={styles.requestedBtnText}>Requested</Text>
                                    </View>
                                )
                            ) : null}
                        </View>
                    </View>
                )}
            </View>
            <TGLoader loading={loading?.screenLoader} loaderColor={colors.darkteal} />
        </>
    );
};
export default OfferRequestPopupScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    popupWrapper: {
        width: '100%',
        minHeight: Size.SIZE_120,
        position: 'absolute',
        bottom: 0,
        backgroundColor: colors.whiteRGB,
        borderTopRightRadius: Size.SIZE_18,
        borderTopLeftRadius: Size.SIZE_18,
        paddingBottom: Spacing.SCALE_10,
        paddingHorizontal: Spacing.SCALE_10,
    },
    headerWrapper: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingVertical: Spacing.SCALE_20,
    },
    crossIconWrapper: {
        paddingHorizontal: 20,
    },
    modalStyle: {
        paddingHorizontal: 0,
        marginHorizontal: 0,
        paddingVertical: 0,
        marginVertical: 0,
    },
    subBody: {
        paddingHorizontal: Spacing.SCALE_10,
    },
    svgIconWrapper: {
        alignItems: 'center',
        justifyContent: 'center',
        width: Size.SIZE_56,
        height: Size.SIZE_56,
        alignSelf: 'center',
        borderRadius: Size.SIZE_74,
        backgroundColor: colors.giftBoxCircleColor,
    },
    offerIdText: {
        fontSize: Typography.FONT_SIZE_20,
        fontWeight: '500',
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_22,
        textAlign: 'center',
        paddingVertical: Spacing.SCALE_10,
    },
    clubNameWrapper: {
        paddingVertical: Spacing.SCALE_10,
    },
    clubTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        color: colors.greyRgb,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_16,
    },
    clubNameText: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_18,
        marginTop: Spacing.SCALE_5,
    },
    additionalNotesWrapper: {
        backgroundColor: colors.greyRgba,
        borderRadius: Size.SIZE_12,
        marginTop: Spacing.SCALE_10,
    },
    additionalNotes: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        padding: Spacing.SCALE_10,
    },
    notesStyle: {
        backgroundColor: colors.tealRGBAColor,
        padding: Spacing.SCALE_10,
        borderRadius: Size.SIZE_8,
        flexDirection: 'row',
        marginTop: Spacing.SCALE_20,
    },
    text: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_21,
        fontWeight: '400',
        color: colors.dark_charcoal,
        marginLeft: Spacing.SCALE_6,
    },
    toolTipWrapper: {
        marginTop: Spacing.SCALE_4,
    },
    requestedBtnText: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
        color: colors.tealRgb,
        marginLeft: Spacing.SCALE_6,
    },
    requestedBtnWrapper: {
        backgroundColor: colors.requestedBtnColor,
        height: Size.SIZE_45,
        marginVertical: Spacing.SCALE_20,
        borderRadius: Size.SIZE_8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    btn: {
        backgroundColor: colors.tealRgb,
        marginVertical: Spacing.SCALE_20,
    },
});
