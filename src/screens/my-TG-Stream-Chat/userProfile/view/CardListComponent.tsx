import { FlatList, StyleSheet, Text, View } from 'react-native';
import React from 'react';

//Assets, theme, utils imports
import { colors } from '../../../../theme/theme';
import { Spacing } from '../../../../utils/responsiveUI';
import { ClubIcon, ProfileUserIcon } from '../../../../assets/svg';

interface UserClubs {
    name: string;
    muted: boolean;
    lowest_visible_tier: string;
    id: number;
}

const CardListComponent = ({
    userClubs,
    isMutualFriends = false,
}: {
    userClubs: UserClubs[];
    isMutualFriends?: boolean;
}) => {
    return (
        <View style={styles.container}>
            <FlatList
                data={userClubs.slice(0, 3)}
                renderItem={({ item }) => (
                    <View style={styles.itemContainer} key={item?.id}>
                        <View style={styles.iconContainer}>
                            {isMutualFriends ? (
                                <ProfileUserIcon width={Spacing.SCALE_16} height={Spacing.SCALE_16} />
                            ) : (
                                <ClubIcon width={Spacing.SCALE_16} height={Spacing.SCALE_16} />
                            )}
                        </View>
                        <Text>{item.name}</Text>
                    </View>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
                keyExtractor={(item) => item.id.toString()}
            />
        </View>
    );
};

export default CardListComponent;

const styles = StyleSheet.create({
    container: {
        borderWidth: 1.5,
        borderRadius: 14,
        borderColor: colors.lightGrey,
        paddingVertical: Spacing.SCALE_10,
    },
    itemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_12,
        paddingHorizontal: Spacing.SCALE_12,
    },
    iconContainer: {
        height: Spacing.SCALE_24,
        width: Spacing.SCALE_24,
        backgroundColor: colors.lightGrey,
        borderRadius: Spacing.SCALE_100,
        alignItems: 'center',
        justifyContent: 'center',
    },
    separator: {
        height: 1.5,
        backgroundColor: colors.lightGrey,
        marginVertical: Spacing.SCALE_10,
    },
});
