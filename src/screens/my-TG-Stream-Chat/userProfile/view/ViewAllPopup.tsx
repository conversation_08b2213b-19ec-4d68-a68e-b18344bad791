import { FlatList, Modal, Pressable, StyleSheet, Text, View } from 'react-native';
import React from 'react';

//theme, utils, assets imports
import { Spacing } from '../../../../utils/responsiveUI';
import { Typography } from '../../../../utils/responsiveUI';
import { Size } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';
import { ClubIcon, CrossIcon, ProfileUserIcon } from '../../../../assets/svg';
import { ALL_GOLF_CLUBS, ALL_MUTUAL_TG_GROUPS, GOLF_CLUBS } from '../../../../utils/constants/strings';

interface ItemInterface {
    name: string;
    muted: boolean;
    id: string;
}

const ViewAllPopup = ({
    popupState,
    popupData,
}: {
    popupState: [string, (value: string) => void];
    popupData: ItemInterface[];
}) => {
    const [tgModal, setTgModal] = popupState;

    const renderItem = ({ item }: { item: ItemInterface }) => {
        return (
            <View style={styles.renderItemContainer} key={item?.id}>
                <View style={styles.iconWrapper}>{tgModal === GOLF_CLUBS ? <ClubIcon /> : <ProfileUserIcon />}</View>
                <Text style={styles.groupNameStyle}>{item?.name}</Text>
            </View>
        );
    };

    return (
        <Modal transparent={true} visible={true} animationType="fade" onRequestClose={() => setTgModal('')}>
            <View style={{ flex: 1 }}>
                <Pressable style={{ flex: 1, backgroundColor: colors.transparentRgba }} />
                <View style={styles.popupWrapper}>
                    <View style={styles.headerWrapper}>
                        <Text style={styles.popupHeaderText}>
                            {tgModal === GOLF_CLUBS ? ALL_GOLF_CLUBS : ALL_MUTUAL_TG_GROUPS}
                        </Text>
                        <Pressable style={styles.crossIconWrapper} onPress={() => setTgModal('')}>
                            <CrossIcon />
                        </Pressable>
                    </View>
                    <FlatList data={popupData} renderItem={renderItem} keyExtractor={(item) => item?.id.toString()} />
                </View>
            </View>
        </Modal>
    );
};

export default ViewAllPopup;

const styles = StyleSheet.create({
    popupWrapper: {
        width: '100%',
        minHeight: Spacing.SCALE_220,
        maxHeight: Spacing.SCALE_350,
        position: 'absolute',
        bottom: 0,
        backgroundColor: colors.white,
        borderTopLeftRadius: Size.SIZE_20,
        borderTopRightRadius: Size.SIZE_20,
        padding: Spacing.SCALE_16,
    },
    popupHeaderText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_20,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    headerWrapper: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: Spacing.SCALE_10,
        alignItems: 'center',
    },
    crossIconWrapper: {},
    divider: {
        height: 0.8,
        backgroundColor: colors.greyRgba,
    },
    renderItemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_10,
        marginVertical: Spacing.SCALE_6,
    },
    groupNameStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
    },
    initialsStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.tealRgb,
        textTransform: 'uppercase',
    },
    iconWrapper: {
        width: Size.SIZE_40,
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_60,
        backgroundColor: colors.lightgray,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
