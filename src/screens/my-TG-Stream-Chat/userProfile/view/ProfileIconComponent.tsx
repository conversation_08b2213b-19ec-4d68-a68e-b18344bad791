import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { colors } from '../../../../theme/theme';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import TGAmbassadorLable from '../../../../forms/profile/views/TGAmbassadorLable';
import FastImage from 'react-native-fast-image';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../../interface/type';

interface ProfileIconProps {
    profilePhoto: string;
    name: string;
    tg_ambassador_visibility?: boolean;
    width?: number;
    height?: number;
    wrapperStyle?: any;
}

const ProfileIcon = ({
    profilePhoto,
    name,
    tg_ambassador_visibility = false,
    width = Size.SIZE_70,
    height = Size.SIZE_70,
    wrapperStyle = {},
}: ProfileIconProps) => {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const [showBadgeText, setShowBadgeText] = useState(false);

    const handlePress = () => {
        if (profilePhoto) {
            navigation.navigate('ShowUserDp', { profilePhoto: profilePhoto });
        }
    };

    const showFullTitle = (value: boolean) => {
        setShowBadgeText(value);

        if (value && !showBadgeText) {
            setTimeout(() => {
                setShowBadgeText(false);
            }, 2000);
        }
    };

    return (
        <TouchableOpacity
            style={[
                styles.profileWrapper,
                {
                    width: width,
                    height: height,
                    borderRadius: Math.max(width, height) / 2, // Keep it circular
                },
                wrapperStyle,
            ]}
            onPress={handlePress}>
            {tg_ambassador_visibility && (
                <TouchableOpacity
                    onPress={() => {
                        showFullTitle(true);
                    }}
                    style={{ zIndex: 100 }}>
                    <TGAmbassadorLable
                        containerStyle={[
                            {
                                marginVertical: 0,
                                position: 'absolute',
                            },
                            showBadgeText
                                ? {
                                      paddingVertical: Spacing.SCALE_3,
                                      paddingHorizontal: Spacing.SCALE_3,
                                      paddingRight: Spacing.SCALE_10,
                                  }
                                : { paddingVertical: Spacing.SCALE_3 },
                            profilePhoto
                                ? {
                                      left: Spacing.SCALE_10,
                                      top: -Spacing.SCALE_10,
                                  }
                                : {
                                      left: Spacing.SCALE_10,
                                      top: -Spacing.SCALE_31,
                                  },
                        ]}
                        showBadgeText={showBadgeText}
                        iconHeight={18}
                        iconWidth={18}
                    />
                </TouchableOpacity>
            )}
            {profilePhoto ? (
                <FastImage
                    source={{ uri: profilePhoto, priority: FastImage.priority.high }}
                    style={[
                        styles.imageStyle,
                        {
                            width: width,
                            height: height,
                            borderRadius: Math.max(width, height) / 2,
                        },
                    ]}
                />
            ) : (
                <>
                    {name ? (
                        <View style={[styles.imageTextWrapper, { width: width }]}>
                            <Text style={styles.imageText}>{name?.trim()[0]}</Text>
                        </View>
                    ) : null}
                </>
            )}
        </TouchableOpacity>
    );
};

export default ProfileIcon;

const styles = StyleSheet.create({
    imageTextWrapper: {
        width: Size.SIZE_70,
        alignItems: 'center',
    },
    imageText: {
        fontSize: Typography.FONT_SIZE_40,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        color: colors.tealRgb,
        textAlign: 'center',
        textTransform: 'capitalize',
    },
    profileWrapper: {
        borderRadius: Size.SIZE_100,
        width: Size.SIZE_70,
        height: Size.SIZE_70,
        backgroundColor: colors.lightgray,
        alignSelf: 'center',
        marginTop: Spacing.SCALE_24,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: Size.SIZE_3,
        borderColor: colors.whiteColor,
        zIndex: 999,
    },
    imageStyle: {
        height: Size.SIZE_70,
        width: Size.SIZE_70,
        borderRadius: Size.SIZE_100,
    },
    loaderWrapper: {
        position: 'absolute',
        left: 0,
        right: 0,
    },
    name: {
        fontSize: Size.SIZE_16,
        fontWeight: '500',
        lineHeight: Size.SIZE_30,
        fontFamily: 'Ubuntu',
        color: 'rgba(15, 24, 40, 1)',
        alignSelf: 'center',
        marginTop: Spacing.SCALE_9,
    },
    memberShip: {
        fontSize: Size.SIZE_13,
        fontWeight: '400',
        lineHeight: Size.SIZE_15,
        fontFamily: 'Ubuntu',
        color: 'rgba(128, 128, 128, 1)',
        alignSelf: 'center',
        marginTop: Spacing.SCALE_6,
    },
});
