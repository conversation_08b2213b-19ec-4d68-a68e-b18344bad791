import React, { useContext } from 'react';
import { StyleSheet, View, Text } from 'react-native';

import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import StreamChatButton from '../../../components/buttons/StreamChatButton';
import { GlobalContext } from '../../../context/contextApi';
import { useNavigation } from '@react-navigation/native';
import { colors } from '../../../theme/theme';

const DeleteChannelConfirmationPopup = ({ route }) => {
    const {
        handleYesButton = () => {},
        popupHeader,
        popupTitle,
        firstBtnLabel = '',
        secondBtnLabel = '',
        handleFirstBtnPress = () => {},
        popupSubText = '',
    } = route?.params;
    const { actions, state } = useContext(GlobalContext);
    const navigation = useNavigation();
    return (
        <View style={styles.modal}>
            <View style={styles.container}>
                <View style={styles.box}>
                    <View style={styles.popupWrapper}>
                        {popupHeader && <Text style={styles.header}>{popupHeader}</Text>}
                        {popupTitle && (
                            <View style={styles.bodyTextWrapper}>
                                <Text style={styles.bodyText}>{popupTitle}</Text>
                            </View>
                        )}
                        {popupSubText && (
                            <View style={styles.bodyTextWrapper}>
                                <Text style={styles.bodySubText}>{popupSubText}</Text>
                            </View>
                        )}
                        <View style={styles.btnContainer}>
                            <StreamChatButton
                                text="No"
                                label={firstBtnLabel || 'NO'}
                                loading={false}
                                buttonContainer={styles.buttonContainer}
                                btn={styles.btn}
                                btnText={styles.buttonText}
                                onPress={() => {
                                    handleFirstBtnPress();
                                    navigation.pop(1);
                                    actions?.setDeleteChannelPopupState(false);
                                }}
                            />
                            <StreamChatButton
                                text="Yes"
                                label={secondBtnLabel || 'Yes'}
                                loading={state?.deletePopupLoader}
                                buttonContainer={styles.buttonContainer}
                                btn={styles.btn1}
                                onPress={() => {
                                    navigation.goBack();
                                    handleYesButton();
                                }}
                            />
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default DeleteChannelConfirmationPopup;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    box: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_30,
    },
    popupWrapper: {
        width: Spacing.SCALE_329,
        position: 'absolute',
        backgroundColor: 'white',
        borderRadius: Size.SIZE_8,
        paddingHorizontal: Spacing.SCALE_10,
    },
    modal: {
        flex: 1,
    },
    header: {
        fontSize: Typography.FONT_SIZE_21,
        fontWeight: '500',
        lineHeight: Size.SIZE_25,
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(51, 51, 51, 1)',
        textAlign: 'center',
        marginTop: Spacing.SCALE_20,
    },
    bodyText: {
        fontSize: Typography.FONT_SIZE_18,
        fontWeight: '400',
        lineHeight: Size.SIZE_22,
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(51, 51, 51, 1)',
        textAlign: 'center',
    },
    bodySubText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        lineHeight: Size.SIZE_22,
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
        textAlign: 'center',
    },
    bodyTextWrapper: {
        width: Spacing.SCALE_250,
        marginVertical: Spacing.SCALE_10,
        alignSelf: 'center',
    },
    btnContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingVertical: Spacing.SCALE_15,
        paddingHorizontal: Spacing.SCALE_5,
    },
    buttonContainer: {
        width: Size.SIZE_140,
    },
    btn: {
        backgroundColor: 'rgba(242, 242, 242, 1)',
        height: Size.SIZE_40,
    },
    btn1: {
        backgroundColor: 'rgba(224, 94, 82, 1)',
        height: Size.SIZE_40,
    },
    buttonText: {
        color: 'rgba(51, 51, 51, 1)',
    },
});
