import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, StatusBar, View } from 'react-native';
import {
    Chat,
    OverlayProvider,
    Streami18n,
    useMessageContext,
} from 'stream-chat-react-native';
import { messageActions as defaultMessageActions } from 'stream-chat-react-native';
import Share from 'react-native-share';
import RNFetchBlob from 'rn-fetch-blob';
import Clipboard from '@react-native-clipboard/clipboard';

import StreamMessageScreenHeader from '../../../components/layout/StreamMessageScreenHeader';
import { AuthContext } from '../../../context/AuthContext';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { getOneToOneChannelName } from '../action';
import StreamEmptyScreen from './StreamEmptyScreen';
import ForwardMessage from '../../../assets/svg/ForwardMessage.svg';
import Copy from '../../../assets/svg/Copy.svg';
import ShareIcon from '../../../assets/images/share.svg';
import ForwardMessageScreen from '../forwardMessage/view/ForwardMessageScreen';
import DeleteConfirmationPopup from './DeleteConfirmationPopup';
import { SYSTEM_THOUSAND_GREENS_PUBLIC } from '../client';
import MessageViewScreen from '../TGChatComponent/MessageViewScreen';
import { GlobalContext } from '../../../context/contextApi';
import getAllChannelMembers from '../groupInfo/action/getAllChannelMembers';
import sortByLastName from '../groupInfo/action/sortingGroupMembers';
import HandleDateSeperator from '../TGChatComponent/DateSeperatorComponent';
import HandleDateHeader from '../TGChatComponent/DateHeaderComponent';
import StreamButton from '../TGChatComponent/StreamSendCustomButton';
import { CustomMessageActionListItem } from '../TGChatComponent/CustomMessageActionListItem';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import ShowChatMessage from './ShowChatMessage';
import config from '../../../config';
import { colors } from '../../../theme/theme';

const MessageScreen = ({ route, navigation }) => {
    const { user } = useContext(AuthContext);
    const {
        client,
        chatClient,
        channel,
        setChannel,
        updateChannel,
        updateBlockedChannel,
    } = useContext(StreamChatContext);
    const { state, actions } = useContext(GlobalContext);
    const [online, setOnline] = useState(false);
    const [showForwardPopup, setShowForwardPopup] = useState(false);
    const [deletePopup, setDeletePopup] = useState(false);
    const [loading, setLoading] = useState(false);
    const [shareLoading, setShareLoading] = useState(false);
    const [selectedData, setSelectedData] = useState([]);
    const { updatedChannel, selectedMessageId } = state;
    const { channelMembersAction, selectedMessageIdAction } = actions;
    const isFocused = useIsFocused();

    const streami18n = new Streami18n({
        disableDateTimeTranslations: true,
    });

    let members;
    const getAllMembers = async () => {
        members = await getAllChannelMembers(channel, {});
        members = members.sort(sortByLastName);
        channelMembersAction(members);
        return members;
    };

    useState(() => {
        getAllMembers()
    }, [channel?.id])

    useFocusEffect(
        useCallback(() => {
            const onBackPress = () => {
                if (route.params?.screen === config.routes.TG_CHAT) {
                    navigation.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
                        screen: config.routes.TG_CHAT,
                    });
                } else {
                    navigation.goBack()
                }
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener(
                    'hardwareBackPress',
                    onBackPress,
                );
        }, [navigation, route.params?.screen, isFocused]),
    );

    useEffect(() => {
        actions?.setCurrentScreenName('MessageScreen');
        updateBlockedChannel(channel);
    }, [isFocused]);

    useEffect(() => {
        const channelUpdatedEventListener = async (event) => {
            await channel?.watch();
            updateBlockedChannel(channel);
            actions?.updatedChannelAction(channel);
            actions?.setOtherUser(
                state?.channelMembers?.find((member) => member?.user_id !== user?.id)
            );
            updateChannel(channel);
            setChannel(channel);
        };

        //Checking if the channel.on is a function
        if (typeof channel?.on === 'undefined') {
            return
        }

        const channelUpdatedListener = channel?.on(
            'channel.updated',
            channelUpdatedEventListener,
        );
        const memberRemovedListener = channel?.on(
            'member.removed',
            async (event) => {
                const members = await getAllMembers();
                if (event?.member?.user?.id === user?.id) {
                    navigation.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
                        screen: config.routes.TG_CHAT,
                    });
                }
            },
        );

        return () => {
            memberRemovedListener.unsubscribe();
            channelUpdatedListener.unsubscribe();
        };
    }, [
        channel,
        client,
        user?.full_name,
        user?.first_name,
        user?.username,
        user?.profilePhoto,
        state?.channelMembers?.find(
            (member) => member?.user_id !== user?.id,
        )?.user?.name,
    ]);

    const CustomPreviewTitle = (channel) => {
        const name = getOneToOneChannelName(channel, user, state?.allFriendsId);
        return name;
    };

    const handleScreenName = () => {
        if (
            channel?.type === 'system_channel' ||
            channel?.type === 'user_created_group'
        ) {
            return channel?.data.name;
        } else {
            return CustomPreviewTitle(channel);
        }
    };

    const handleMessageAction = (param) => {
        const {
            isMyMessage,
            dismissOverlay,
            message,
        } = param;
        let actions = defaultMessageActions({ ...param });
        actions = actions.filter((action) =>
            ['quotedReply', 'deleteMessage'].includes(action?.actionType),
        );
        if (isMyMessage) {
        }
        actions.push({
            action: async () => {
                dismissOverlay();
                navigation.navigate('ForwardMessage', {
                    setShowForwardPopup: setShowForwardPopup,
                    selectedMessageId: message.id,
                    selectedState: [selectedData, setSelectedData],
                });
            },
            actionType: 'forwardMessage',
            icon: <ForwardMessage />,
            title: 'Forward Message',
        });

        if (message?.attachments?.length > 0) {
            actions.push({
                action: async () => {
                    dismissOverlay();
                    if (message?.attachments[0]?.type === 'image') {
                        if (message?.attachments?.length >= 1) {
                            let attachmentArray = [];
                            message?.attachments?.map((data) => {
                                attachmentArray.push(data?.image_url);
                            });
                            sharePost(
                                attachmentArray,
                                message?.attachments[0]?.type,
                            );
                        }
                    } else if (message?.attachments[0]?.type === 'video') {
                        sharePost(
                            message?.attachments[0]?.asset_url,
                            message?.attachments[0]?.type,
                        );
                    } else if (message?.attachments[0]?.type === 'file') {
                        sharePost(
                            message?.attachments[0]?.asset_url,
                            message?.attachments[0]?.type,
                        );
                    }
                },
                actionType: 'share',
                icon: <ShareIcon />,
                title: 'Share Message',
            });
        }
        if (
            message?.attachments?.length === 0 ||
            !message?.attachments[0]?.type
        ) {
            actions.push({
                action: async () => {
                    dismissOverlay();
                    Clipboard.setString(message?.text);
                },
                actionType: 'copyMessage',
                icon: <Copy />,
                title: 'Copy Message',
            });
        }

        return actions;
    };

    async function sharePost(post, type) {
        let options;
        setShareLoading(true);
        if (!shareLoading) {
            if (type === 'image') {
                let imageUrls = [];
                let link = await Promise.all(
                    post?.map(async (imageUrl) => {
                        const res = await RNFetchBlob.config({
                            fileCache: true,
                        }).fetch('GET', imageUrl);
                        const { type } = await res.blob();
                        const photoBase64 = await res.readFile('base64');
                        let link = `data:${type};base64,${photoBase64}`;
                        imageUrls.push(link);
                        return link;
                    }),
                );
                options = {
                    title: 'pdf',
                    message: 'pdf',
                    urls: imageUrls,
                };
            } else if (type === 'file') {
                const res = await RNFetchBlob.config({
                    fileCache: true,
                    appendExt: 'pdf',
                }).fetch('GET', post);
                const { type } = await res.blob();
                let filePath = res.path();
                const photoBase64 = res.readFile('base64');
                options = {
                    type: type,
                    url: filePath,
                };
            } else if (type === 'video') {
                const res = await RNFetchBlob.config({
                    fileCache: true,
                    appendExt: 'mp4',
                }).fetch('GET', post, {});

                options = {
                    title: '',
                    message: '',
                    url: 'file://' + res.path(),
                    type: 'video/mp4',
                };
            }

            Share.open(options)
                .then((res) => {
                    setShareLoading(false);
                })
                .catch((err) => {
                    setShareLoading(false);
                    err && console.log(err);
                });
        }
    }

    const handleYesButton = async () => {
        setLoading(true);
        await client.deleteMessage(selectedMessageId);
        setLoading(false);
        setDeletePopup(false);
    };

    const CustomMessageUIComponent = (params) => {
        const paramss = useMessageContext();
        const {
            message,
            reactions,
            onLongPress,
            alignment,
            onPress
        } = paramss
        console.log("message", message)

        return (
            <ShowChatMessage
                message={message}
                alignment={alignment}
                onLongPress={onLongPress}
                onPressClick={onPress}
                reactions={reactions}
            />
        );
    };
    const handleEmptyState = () => {
        return <StreamEmptyScreen screenText={`Send a message`} screen={''} />;
    };

    const messageInput = useMemo(() => {
        return (
            <MessageViewScreen
                StreamButton={StreamButton}
                HandleDateHeader={HandleDateHeader}
                handleEmptyState={handleEmptyState}
                handleMessageAction={handleMessageAction}
                handleDateSeperator={HandleDateSeperator}
                channel={updatedChannel}
                MessageSimple={CustomMessageUIComponent}
                setDeletePopup={setDeletePopup}
            />
        );
    }, [channel?.data?.frozen, state?.updatedChannel]);
    console.log("route.params?.screen", route.params?.screen)

    return (
        <>
            <StatusBar barStyle="dark-content" backgroundColor={colors.whiteRGB} />
            <StreamMessageScreenHeader
                online={online}
                channelData={channel}
                isdissabled={channel?.type === SYSTEM_THOUSAND_GREENS_PUBLIC}
                navigateToScreen={route.params ? route.params?.screen : ''}
                channel={updatedChannel}
            />
            <View style={{ flex: 1 }}>
                <OverlayProvider
                    i18nInstance={streami18n}
                    MessageActionListItem={(data) =>
                        CustomMessageActionListItem(data, setDeletePopup)
                    }
                >
                    <Chat client={client}>{messageInput}</Chat>
                </OverlayProvider>
            </View>
            {showForwardPopup && (
                <ForwardMessageScreen
                    showForwardPopup={showForwardPopup}
                    setShowForwardPopup={setShowForwardPopup}
                    selectedMessageId={selectedMessageId}
                    selectedState={[selectedData, setSelectedData]}
                />
            )}
            {deletePopup && (
                <DeleteConfirmationPopup
                    deletePopup={deletePopup}
                    setDeletePopup={setDeletePopup}
                    handleYesButton={handleYesButton}
                    btnLoader={[loading, setLoading]}
                    popupTitle={`Are you sure you want to delete this message ?`}
                    popupHeader={`Delete Message`}
                />
            )}
        </>
    );
};

export default MessageScreen;