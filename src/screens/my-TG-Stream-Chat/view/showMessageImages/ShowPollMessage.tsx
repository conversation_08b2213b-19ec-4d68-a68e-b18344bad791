import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useEffect, useLayoutEffect, useState, useCallback } from 'react';

//@ts-ignore
import PollIconRight from '../../../../assets/svg/PollIcon.svg';
//@ts-ignore
import PollIconLeft from '../../../../assets/svg/align-left.svg';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';
import PollRadioButton from '../../../../components/poll/PollRadioButton';
import { StreamChatContext } from '../../../../context/StreamChatContext';
import { useNavigation } from '@react-navigation/native';
import config from '../../../../config';
import { AuthContext } from '../../../../context/AuthContext';

interface PollOption {
    id: number;
    text: string;
    votes: number;
    voters?: string[];
}

interface PollData {
    question: string;
    options: PollOption[];
    allow_answers: boolean;
    enforce_unique_vote: boolean;
    id: string;
    vote_counts_by_option: any;
    latest_votes_by_option: any;
    vote_count: number;
    answers_count: number;
    name: string;
    latest_answers: any;
    is_closed: boolean;
    own_votes: any;
}

interface MessageData {
    poll?: PollData;
    text?: string;
    type: string;
}
interface ShowPollMessageProps {
    messageData: MessageData;
    alignment: string;
}

interface PollButton {
    text: string;
    onPress: (id: any) => void; // Update the type to match the onPress function
    disable: boolean;
    isShow?: boolean;
}

const ShowPollMessage: React.FC<ShowPollMessageProps> = ({ messageData, alignment }) => {
    const navigation = useNavigation();
    const { poll: messagePoll } = messageData;
    const [poll, setPoll] = useState(messagePoll);
    const { client } = useContext(StreamChatContext);
    const { user } = useContext(AuthContext);
    const [pollButtons, setPollButtons] = useState<PollButton[]>([]);
    const [pollVotes, setPollVotes] = useState([]);
    const [isEndingPoll, setIsEndingPoll] = useState(false);

    const getPollVotes = useCallback(async () => {
        const pollVotes = await client.queryPollVotes(messagePoll?.id, {is_answer: false});
        setPollVotes(pollVotes?.votes);
        
    }, [client, messagePoll?.id]);

    useEffect(() => {
        getPollVotes();
    }, [getPollVotes]);

    useEffect(() => {
        if (!poll) return;
        let btnOptions = [
            {
                text: `${
                    poll?.own_votes?.find(vote => vote.is_answer === true) ? 'Update' : 'Add'
                } a Comment`,
                onPress: () =>
                    navigation.push(config.routes.POLL_COMMENT, {
                        callBack: handleSendComment,
                        comment: poll?.own_votes?.find(vote => vote.is_answer === true)?.answer_text || '',
                    }),
                disable: false,
                isShow: (poll.allow_answers && !poll.is_closed) || false,
            },
            {
                text: `View ${poll.answers_count === 0 ? '' : poll.answers_count} ${
                    poll.answers_count === 1 ? 'Comment' : 'Comments'
                }`,
                onPress: () => {
                    navigation.navigate(config?.routes?.POLL_RESULTS, { pollData: poll, tab: 'Comments' });
                },
                disable: !poll.answers_count,
                isShow: poll.allow_answers || false,
            },
            {
                text: 'View Votes',
                onPress: () => {
                    navigation.navigate(config?.routes?.POLL_RESULTS, { pollData: poll, tab: 'Votes' });
                },
                disable: !poll.vote_count,
                isShow: true,
            },
        ];

        let option = {
            text: 'End Poll',
            onPress: (id: any) => {
                if (isEndingPoll) return;
                setIsEndingPoll(true);
                navigation.push('DeleteChannelConfirmationPopup', {
                    handleYesButton: () => endPoll(id),
                    popupHeader: 'End Poll ?',
                    popupTitle: 'Are you sure you want to end this poll?',
                    secondBtnLabel: 'End',
                    firstBtnLabel: 'Dismiss',
                    handleFirstBtnPress: () => setIsEndingPoll(false), // Reset when dismissed
                });
            },
            disable: isEndingPoll || poll.is_closed,
            isShow: poll.is_closed ? false : true,
        };

        if (alignment === 'right') {
            setPollButtons([option, ...btnOptions]);
        } else {
            setPollButtons([...btnOptions]);
        }
    }, [
        poll?.vote_count,
        poll?.answers_count,
        poll?.allow_answers,
        JSON.stringify(poll?.latest_answers),
        poll?.is_closed,
        isEndingPoll,
    ]); // Dependencies to watch

    const handleSendComment = async (comment: string) => {
        const { votes } = await client.castPollVote(messageData?.id, poll?.id, {
            answer_text: comment,
        });
        navigation.pop(1);
    };

    useLayoutEffect(() => {
        if (!client) return;

        const handlePollEvents = async (event: any) => {
            // Only handle events for this specific poll
            if (event.poll?.id !== poll?.id) return;

            switch (event.type) {
                case 'poll.created':
                case 'poll.updated':
                case 'poll.closed':
                case 'poll.deleted':
                case 'poll.vote_casted':
                case 'poll.vote_removed':
                case 'poll.vote_changed':
                    const poll = await client.getPoll(event?.poll?.id, user?.id);
                    getPollVotes();
                    setPoll(poll?.poll);

                    break;
                default:
                    break;
            }
        };

        // Listen for specific poll events instead of 'all'
        const events = [
            'poll.created',
            'poll.updated',
            'poll.closed',
            'poll.deleted',
            'poll.vote_casted',
            'poll.vote_removed',
            'poll.vote_changed',
        ];

        events.forEach((eventType) => {
            client.on(eventType, handlePollEvents);
        });

        return () => {
            events.forEach((eventType) => {
                client.off(eventType, handlePollEvents);
            });
        };
    }, [client, poll?.id]);

    const endPoll = async (id: any) => {
        try {
            await client.closePoll(id);
        } catch (error) {
            console.log('error=>', error);
        } finally {
            setIsEndingPoll(false); // Reset the state after completion
        }
    };

    return (
        <>
            <View style={styles.container}>
                {/* Poll Header */}
                <View style={styles.pollHeaderContainer}>
                    {alignment === 'left' ? <PollIconLeft /> : <PollIconRight />}
                    <Text style={[styles.text, { color: alignment === 'left' ? colors.black : colors.white }]}>
                        Poll
                    </Text>
                </View>

                {/* Poll Question */}
                <View>
                    <Text
                        style={[
                            styles.font16,
                            { color: alignment === 'left' ? colors.black : colors.white, width: '80%' },
                        ]}>
                        {messageData?.text}
                    </Text>
                    <Text
                        style={[
                            styles.text,
                            {
                                color: alignment === 'left' ? colors.darkGreyRgba : colors.greyRgba,
                                marginBottom: Spacing.SCALE_16,
                                marginTop: Spacing.SCALE_5,
                            },
                        ]}>
                        {poll?.is_closed
                            ? 'Poll ended'
                            : poll?.enforce_unique_vote
                            ? 'Select one'
                            : 'Select one or more'}
                    </Text>
                </View>

                {/* Poll Options */}
                {poll?.options?.map((option, index) => {
                    return (
                        <PollRadioButton
                            options={option}
                            key={index.toString()}
                            alignment={alignment}
                            poll={poll}
                            votes={pollVotes}
                            messageData={messageData}
                        />
                    );
                })}
            </View>
            <View style={{ height: 1, backgroundColor: colors.greyCheckBox, marginBottom: Spacing.SCALE_13 }} />

            {/* Poll Buttons */}
            <View>
                {pollButtons?.map(({ text, onPress, isShow, disable }, index) => {
                    return (
                        isShow && (
                            <TouchableOpacity
                                style={{
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    paddingHorizontal: Spacing.SCALE_12,
                                }}
                                disabled={disable}
                                onPress={() => {
                                    onPress(poll?.id);
                                }}>
                                <Text
                                    style={{
                                        color:
                                            alignment === 'left'
                                                ? colors.tealRgb
                                                : text === 'End Poll'
                                                ? colors.whiteRGB
                                                : colors.tealBrightColor,
                                        fontSize: Typography.FONT_SIZE_14,
                                        fontFamily: 'Ubuntu-Medium',
                                        opacity: disable ? 0.5 : 1,
                                    }}>
                                    {text}
                                </Text>
                                {index !== pollButtons.length - 1 && (
                                    <View
                                        style={{
                                            height: 1,
                                            backgroundColor: colors.greyCheckBox,
                                            marginBottom: Spacing.SCALE_7,
                                            width: '100%',
                                            marginVertical: Spacing.SCALE_13,
                                        }}
                                    />
                                )}
                            </TouchableOpacity>
                        )
                    );
                })}
            </View>
        </>
    );
};

export default ShowPollMessage;

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: Spacing.SCALE_12,
        paddingTop: Spacing.SCALE_16,
        width: Size.SIZE_268,
    },
    pollHeaderContainer: {
        paddingBottom: Spacing.SCALE_13,
        flexDirection: 'row',
        columnGap: Spacing.SCALE_6,
        alignItems: 'center',
    },
    text: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
    },
    font16: {
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_18,
    },
});
