import moment from 'moment';

import {
    ADD_MEMBERS_TO_GROUP,
    BLOCK_USER,
    GET_ALL_FRIENDS_ID,
    GET_CHANNEL_MEMBERS,
    REMOVE_PARTICIPANTS,
    UNBLOCK_USER,
} from '../../service/EndPoint';
import { fetcher } from '../../service/fetcher';
import {
    ADMIN_CREATED_GROUP,
    CHANNEL_LIMIT,
    CHANNEL_LIMIT_UNREAD_COUNT,
    MY_TG_GROUP,
    ONE_TO_ONE,
    STREAM_MESSAGE_LIMIT,
    SYSTEM_PRIVATE_NETWORK,
    SYSTEM_THOUSAND_GREENS_PUBLIC,
    USER_CREATED_GROUP,
} from './client';
import { handleParticipantsName } from './view/handleParticipantsName';
import showToast from '../../components/toast/CustomToast';
import routes from '../../config/routes';
import config from '../../config';
import { ALL, GROUP } from '../../utils/constants/strings';

export const getOneToOneChannelName = (client, user, allFriendsId) => {
    let otherName;
    // Fetching the members of the channel (Me and the other person), and finding the data of other user
    if (client?.type === 'messaging') {
        otherName = Object.values(client?.state?.members).find((member) => member?.user_id !== user?.id);
        otherName = handleParticipantsName(allFriendsId, otherName, user);
    }

    // If in a 1:1 client of the user deletes (gets removed), then we have to show the client like:
    // @ToDo Later
    if (!otherName && client?.data?.channel_detail?.users) {
        let otherOne = client?.data?.channel_detail?.users.find((u) => u?.id !== user?.id);
        otherName = otherOne?.id ? otherOne?.name : 'Channel';
    }
    return client?.data?.name || otherName;
};

export const getOneToOneChannelName1 = (client, user) => {
    let otherName;

    // Fetching the members of the channel (Me and the other person), and finding the data of other user
    if (client?.type === 'messaging') {
        otherName = Object.values(client?.state?.members).find((member) => member?.user_id !== user?.id);
        // otherName = otherName.user.full_name
        return otherName.user.full_name;
    }

    // If in a 1:1 client of the user deletes (gets removed), then we have to show the client like:
    // @ToDo Later
};

export const commonFilter = (searchData, user, setFilters, setSort, setOptions, archiveState, activeTab) => {
    const typeArray =
        activeTab === ALL
            ? [
                  USER_CREATED_GROUP,
                  SYSTEM_THOUSAND_GREENS_PUBLIC,
                  SYSTEM_PRIVATE_NETWORK,
                  ADMIN_CREATED_GROUP,
                  MY_TG_GROUP,
              ]
            : activeTab === GROUP
            ? [
                  USER_CREATED_GROUP,
                  SYSTEM_THOUSAND_GREENS_PUBLIC,
                  SYSTEM_PRIVATE_NETWORK,
                  ADMIN_CREATED_GROUP,
                  MY_TG_GROUP,
              ]
            : [ONE_TO_ONE];
    if (searchData == '') {
        let filters;
        
        if (activeTab === GROUP) {
            // When GROUP tab is selected, only show group chats
            filters = {
                members: { $in: [user?.id] },
                type: {
                    $in: typeArray,
                },
                hidden: { $eq: archiveState },
            };
        } else if (activeTab === ALL) {
            // When ALL tab is selected, show both groups and one-to-one chats
            filters = {
                members: { $in: [user?.id] },
                $or: [
                    {
                        last_message_at: { $lte: moment().add('1', 'hour') },
                        type: ONE_TO_ONE,
                    },
                    {
                        type: {
                            $in: typeArray,
                        },
                    },
                ],
                hidden: { $eq: archiveState },
            };
        } else {
            // When ONE_TO_ONE tab is selected, only show one-to-one chats
            filters = {
                members: { $in: [user?.id] },
                type: ONE_TO_ONE,
                hidden: { $eq: archiveState },
            };
        }
        
        setFilters(filters);
        const sort = { last_message_at: -1 };
        setSort(sort);
        const options = { limit: CHANNEL_LIMIT, messages_limit: STREAM_MESSAGE_LIMIT };
        setOptions(options);
    }
};

export const getUnreadCount = async (user, archiveState, setUnreadcount, chatClient) => {
    const filters = {
        members: { $in: [user?.id] },
        $or: [
            {
                $and: [
                    {
                        last_message_at: { $lte: moment().add('1', 'hour') },
                    },
                    {
                        type: { $eq: ONE_TO_ONE },
                    },
                ],
            },
            {
                $and: [
                    {
                        type: {
                            $in: [
                                USER_CREATED_GROUP,
                                SYSTEM_THOUSAND_GREENS_PUBLIC,
                                SYSTEM_PRIVATE_NETWORK,
                                ADMIN_CREATED_GROUP,
                                MY_TG_GROUP,
                            ],
                        },
                    },
                ],
            },
        ],
        hidden: { $eq: archiveState },
    };
    const channels = await chatClient?.queryChannels(
        filters,
        { last_message_at: -1 },
        { limit: CHANNEL_LIMIT_UNREAD_COUNT },
    );
    const counts = channels?.map((channel) => channel?.state?.unreadCount);
    const unreadCount = counts?.filter((count) => count > 0);
    if (unreadCount) {
        // setUnreadcount(unreadCount?.length)
    }
};

export default function debounce(func, wait, immediate) {
    var timeout;

    return (...args) => {
        var context = this;

        var later = () => {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };

        var callNow = immediate && !timeout;

        clearTimeout(timeout);

        timeout = setTimeout(later, wait);

        if (callNow) func.apply(context, args);
    };
}

export const handleAddParticipantsList = (
    userId,
    groupId,
    currentPage,
    participantsList,
    setParticipantsList,
    searchData,
    limit,
    setActivityLoading,
    groupType,
) => {
    let data = {
        userId: userId,
        groupId: groupId,
        search: searchData,
        limit: searchData ? limit * 2 : limit,
        page: currentPage,
        groupType: groupType,
    };
    fetcher({
        endpoint: GET_CHANNEL_MEMBERS,
        method: 'POST',
        body: data,
    }).then((res) => {
        if (res?.status === 1) {
            if (currentPage === 1) {
                setActivityLoading(false);
                setParticipantsList(res?.friends);
            } else if (participantsList === null) {
                setActivityLoading(false);
                setParticipantsList(res?.friends);
            } else {
                setActivityLoading(false);
                setParticipantsList([...participantsList, ...res?.friends]);
            }
        } else {
            showToast({});
        }
    });
};

export const handleAddParticipants = (userId, groupId, members, setLoading, navigation, groupType) => {
    let data = {
        userId: userId,
        members: members,
        groupId: groupId,
        groupType: groupType,
    };
    fetcher({
        endpoint: ADD_MEMBERS_TO_GROUP,
        method: 'POST',
        body: data,
    }).then((res) => {
        if (res?.status) {
            setLoading(false);
            navigation.goBack();
        } else {
            setLoading(false);
            showToast({});
        }
    });
};
export const handleBlockUser = (
    userId,
    channelId,
    otherUserId,
    actions,
    handleGetProfileInfo = () => {},
    handleUpdateBlockedChannel = () => {},
) => {
    let data = {
        userId: userId,
        otherUserId: otherUserId,
        channelId: channelId,
    };
    fetcher({
        endpoint: BLOCK_USER,
        method: 'POST',
        body: data,
    }).then((res) => {
        handleUpdateBlockedChannel();
        if (res?.status) {
            actions?.blockUserResAction(res);
            handleGetProfileInfo();
        } else {
            showToast({});
            alert(res?.message);
        }
    });
};
export const handleUnBlockUser = (
    userId,
    channelId,
    otherUserId,
    actions,
    handleGetProfileInfo = () => {},
    handleUpdateBlockedChannel = () => {},
) => {
    let data = {
        userId: userId,
        otherUserId: otherUserId,
        channelId: channelId,
    };
    fetcher({
        endpoint: UNBLOCK_USER,
        method: 'POST',
        body: data,
    }).then((res) => {
        if (res?.status) {
            handleUpdateBlockedChannel();
            actions?.blockUserResAction(res);
            handleGetProfileInfo();
        } else {
            showToast({});
        }
    });
};

export const removeParticipants = (
    userId,
    groupId,
    memberId,
    setScreenLoading,
    navigation,
    isNavigation = false,
    actions = () => {},
    channelType,
    getMyTGGroupMember = () => {},
    screenName = 'chat',
    setOpenPopup,
    previousScreen = '',
) => {
    actions.setAllMemberScreenLoader(true);
    let data = {
        userId: userId,
        groupId: groupId,
        memberId: memberId,
        channelType: channelType,
    };
    fetcher({
        endpoint: REMOVE_PARTICIPANTS,
        method: 'POST',
        body: data,
    }).then((res) => {
        if (res?.status) {
            setScreenLoading(false);
            if (channelType === MY_TG_GROUP && screenName == routes?.MY_TG_GROUP_INFO) {
                getMyTGGroupMember(true);
            } else if (channelType === MY_TG_GROUP && userId === memberId) {
                if (isNavigation) {
                    setOpenPopup(false);
                    if (previousScreen == 'chat') {
                        navigation.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
                            screen: config.routes.CHAT,
                        });
                    } else {
                        navigation.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
                            screen: config.routes.CHAT,
                        });
                    }
                } else {
                    getMyTGGroupMember(true);
                }
            } else if (userId === memberId) {
                actions?.isMySelfRemoveAction(true);
                navigation.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
                    screen: config.routes.CHAT,
                });
            }
        } else {
            showToast({});
        }
    });
};

export const handleGetAllFriendsId = (userId, actions) => {
    let data = {
        userId: userId,
    };
    fetcher({
        endpoint: GET_ALL_FRIENDS_ID,
        method: 'POST',
        body: data,
    }).then((res) => {
        if (res?.status) {
            actions?.setAllFriendsId(res?.friends);
        } else {
            showToast({});
        }
    });
};
