import { StyleSheet, Text, View } from 'react-native';
import React, { useContext } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { MapClub, Offer } from '../../../interface';
import { RootStackParamList } from '../../../interface/type';
import {
    ClubGolfIconTeal,
    CreateRequestIconTeal,
    DeleteGreyIcon,
    EditIcon,
    GiftBoxIconTeal,
    MapLocationIconNew,
} from '../../../assets/svg';
import { handleTimeFormat } from '../../../components/timeFormatComponent/handleTimeFormat';
import { AuthContext } from '../../../context/AuthContext';
import TierCard from '../../../components/map/TierCard';

const OfferListingCard = ({
    item,
    navigation,
    index,
    club,
    handleRequestAgainstOffer,
    handleEditOffer,
    handleDeleteOffer,
    handleOfferDetailPress,
}: {
    item: Offer;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    index: number;
    club: MapClub | null;
    handleRequestAgainstOffer: (item: Offer) => void;
    handleEditOffer: (offer: Offer) => void;
    handleDeleteOffer: (offer: Offer) => void;
    handleOfferDetailPress: (item: Offer) => void;
}) => {
    const { user } = useContext(AuthContext);

    return (
        <TouchableOpacity
            style={[styles.container, { marginBottom: index === 0 ? 0 : Spacing.SCALE_8 }]}
            onPress={() => {
                handleOfferDetailPress(item);
            }}>
            <View style={styles.headerContainer}>
                <View style={styles.offerIdContainer}>
                    <View style={styles.iconContainer}>
                        <GiftBoxIconTeal />
                    </View>
                    <View style={{ rowGap: Spacing.SCALE_4, width: '65%' }}>
                        <Text style={styles.offerIdText}>
                            Offer ID <Text style={styles.offerIdNumber}>#{item.offer_id}</Text>
                        </Text>
                        <Text style={styles.dateText}>
                            {handleTimeFormat(item.start_date) === handleTimeFormat(item.end_date)
                                ? handleTimeFormat(item.start_date)
                                : `${handleTimeFormat(item.start_date)} - ${handleTimeFormat(item.end_date)}`}
                        </Text>
                    </View>
                </View>
                {item.user_id === user?.id ? (
                    <View style={styles.btnContainer}>
                        <TouchableOpacity
                            style={styles.btnWrapper}
                            onPress={() => {
                                handleEditOffer(item);
                            }}>
                            <EditIcon />
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.btnWrapper}
                            onPress={() => {
                                handleDeleteOffer(item);
                            }}>
                            <DeleteGreyIcon />
                        </TouchableOpacity>
                    </View>
                ) : item.requested ? (
                    <View style={styles.requestedTextWrapper}>
                        <Text style={styles.requestedTextStyles}>Requested</Text>
                    </View>
                ) : (
                    <TouchableOpacity
                        style={styles.createRequestContainer}
                        onPress={() => {
                            handleRequestAgainstOffer(item);
                        }}>
                        <CreateRequestIconTeal />
                    </TouchableOpacity>
                )}
            </View>
            <TierCard
                clubType={club?.properties.ct ?? 0}
                tier={club?.properties.tier ?? 0}
                wrapperStyle={[styles.tierCard, { marginTop: Spacing.SCALE_14 }]}
            />
            <View style={styles.buttonContainer}>
                <ClubGolfIconTeal />
                <View style={{ maxWidth: '80%' }}>
                    <Text style={styles.clubNameText}>{club?.properties?.name}</Text>
                </View>
            </View>
            <View style={styles.addressDetailSection}>
                <MapLocationIconNew />
                <Text style={styles.addressText}>{club?.properties?.addr}</Text>
            </View>
            <Text style={styles.createdByText}>Created by - {item.creatorName}</Text>
            <Text style={styles.descriptionText}>
                {item.details.length > 100 ? item.details.slice(0, 100) + '...' : item.details}
                {item.details.length > 100 && <Text style={{ color: colors.tealRgb }}>Read more</Text>}
            </Text>
        </TouchableOpacity>
    );
};

export default OfferListingCard;

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_10,
        padding: Spacing.SCALE_12,
        borderWidth: Size.SIZE_1,
        borderColor: colors.lightGrey,
        marginVertical: Spacing.SCALE_4,
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    offerIdContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Spacing.SCALE_10,
    },
    iconContainer: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        backgroundColor: colors.tealRGBAColor,
        borderRadius: Size.SIZE_50,
        justifyContent: 'center',
        alignItems: 'center',
    },
    offerIdText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
    },
    offerIdNumber: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.tealRgb,
    },
    dateText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.greyRgb,
    },
    createRequestContainer: {
        height: Size.SIZE_32,
        width: Size.SIZE_32,
        borderRadius: Size.SIZE_8,
        borderWidth: 1,
        borderColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
    },
    createdByText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.greyRgb,
        marginTop: Spacing.SCALE_14,
    },
    descriptionText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
        marginTop: Spacing.SCALE_14,
        lineHeight: Size.SIZE_20,
    },
    requestedTextWrapper: {
        backgroundColor: colors.tealRGBAColor,
        borderRadius: Size.SIZE_6,
        paddingHorizontal: Spacing.SCALE_8,
        paddingVertical: Spacing.SCALE_6,
    },
    requestedTextStyles: {
        fontSize: Typography.FONT_SIZE_10,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.tealRgb,
    },
    btnWrapper: {
        height: Size.SIZE_24,
        width: Size.SIZE_24,
        alignItems: 'center',
        justifyContent: 'center',
    },
    btnContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Spacing.SCALE_10,
        zIndex: 999,
    },
    tierCard: {
        borderRadius: Size.SIZE_6,
    },
    buttonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
        marginTop: Spacing.SCALE_8,
    },
    clubNameText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    addressDetailSection: {
        flexDirection: 'row',
        marginTop: Spacing.SCALE_8,
        columnGap: Spacing.SCALE_10,
        width: '90%',
    },
    detailSection: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    addressText: {
        color: colors.fadeBlack,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
    },
});
