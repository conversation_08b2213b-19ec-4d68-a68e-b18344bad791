import { Dimensions, Platform, SafeAreaView, ScrollView, StyleSheet, Text, View } from 'react-native';
import React, { useContext, useState } from 'react';
import { colors } from '../../../../theme/theme';
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import FilterHeader from '../../../../forms/map-club/FilterHeader';
import Form from '../../../../forms/FormContext';
import TealButton from '../../../../components/buttons/TealButton';
import TealSquareCheckbox from '../../../../components/checkbox/TealSquareCheckbox';
import { ALL, CLUB_WITH_MY_TG_COMMUNITY, GENDER } from '../../../../utils/constants/strings';
import TGGroupDropDown from '../../../../forms/map-club/TGGroupDropDown';
import { AuthContext } from '../../../../context/AuthContext';

const { width } = Dimensions.get('window');

let defaultFilter = {
    fern: false,
    sage: false,
    moss: false,
    olive: false,
    friendsAndContact: false,
    myTGGroupMember: false,
    openOfferClubs: false,
    favoriteClubs: false,
    playedClubs: false,
    playAsCouple: false,
    selectedTGGroup: [],
    clubswithFemaleMembers: false,
    clubPercentage: 'All',
    clubMemberCount: 'All',
};

interface OfferFilterProps {
    filterState: any;
    setFilterActive: (active: boolean) => void;
    setModal: () => void;
}

const OfferFilter = ({ filterState, setFilterActive, setModal }: OfferFilterProps) => {
    const { user } = useContext(AuthContext);
    const [showDropDown, setShowDropDown] = useState(false);
    const [filter, setFilter] = filterState;

    const handleDefaultFilter = () => {
        setFilter(defaultFilter);
    };

    const applyFilter = (values: any) => {
        setFilter(values);
        setModal();

        if (!deepEqual(values, defaultFilter)) {
            setFilterActive(true);
        } else {
            setFilterActive(false);
        }
    };
    function deepEqual(a: any, b: any) {
        if (a === b) return true;

        if (typeof a !== 'object' || typeof b !== 'object' || a === null || b === null) {
            return false;
        }

        const keysA = Object.keys(a);
        const keysB = Object.keys(b);

        if (keysA.length !== keysB.length) return false;

        for (let key of keysA) {
            if (!keysB.includes(key) || !deepEqual(a[key], b[key])) {
                return false;
            }
        }

        return true;
    }

    return (
        <SafeAreaView style={styles.safeArea}>
            {/* @ts-ignore */}
            <Form initialValues={filter} onSubmit={applyFilter}>
                <View style={styles.mainContainer}>
                    <FilterHeader
                        onBackPress={() => setModal()}
                        defaultFilter={defaultFilter}
                        handleDefaultFilter={handleDefaultFilter}
                    />
                    <ScrollView
                        style={styles.scrollView}
                        contentContainerStyle={styles.scrollViewContent}
                        showsVerticalScrollIndicator={false}>
                        <View style={styles.container}>
                            {/* Tier */}
                            <View style={styles.card}>
                                <Text style={styles.titleStyle}>Tier</Text>
                                <View style={styles.tierContainer}>
                                    <TealSquareCheckbox
                                        name="fern"
                                        label="Fern"
                                        labelStyle={styles.lableTextStyle}
                                        checkBoxContainerStyle={{ marginBottom: 0 }}
                                    />
                                </View>
                                <View style={styles.tierContainer}>
                                    <TealSquareCheckbox
                                        name="sage"
                                        label="Sage"
                                        labelStyle={styles.lableTextStyle}
                                        checkBoxContainerStyle={{ marginBottom: 0 }}
                                    />
                                </View>
                                <View style={styles.tierContainer}>
                                    <TealSquareCheckbox
                                        name="moss"
                                        label="Moss"
                                        labelStyle={styles.lableTextStyle}
                                        checkBoxContainerStyle={{ marginBottom: 0 }}
                                    />
                                </View>
                                <View style={styles.tierContainer}>
                                    <TealSquareCheckbox
                                        name="olive"
                                        label="Olive"
                                        labelStyle={styles.lableTextStyle}
                                        checkBoxContainerStyle={{ marginBottom: 0 }}
                                    />
                                </View>
                            </View>

                            {/* My Tg Community */}
                            <View style={styles.card}>
                                <Text style={[styles.titleStyle, { marginBottom: Spacing.SCALE_12 }]}>
                                    {CLUB_WITH_MY_TG_COMMUNITY}
                                </Text>
                                <View style={styles.tierSubContainer}>
                                    <View style={{ marginTop: Spacing.SCALE_8 }}>
                                        <TealSquareCheckbox
                                            name="friendsAndContact"
                                            label="My Friends and Contacts"
                                            labelStyle={styles.lableTextStyle}
                                        />
                                        <TealSquareCheckbox
                                            name="myTGGroupMember"
                                            label="My TG Group Members"
                                            labelStyle={styles.lableTextStyle}
                                        />

                                        <TGGroupDropDown
                                            showDropDown={showDropDown}
                                            //@ts-ignore
                                            setShowDropDown={setShowDropDown}
                                            containerStyle={{ marginBottom: Spacing.SCALE_8 }}
                                        />
                                    </View>
                                </View>
                            </View>

                            {user?.playAsCouple && (
                                <View style={styles.card}>
                                    <View style={[styles.tierContainer, { marginVertical: 0 }]}>
                                        <TealSquareCheckbox
                                            name="playAsCouple"
                                            label="Play as Couple"
                                            labelStyle={[styles.lableTextStyle]}
                                            checkBoxContainerStyle={{ marginBottom: 0 }}
                                        />
                                    </View>
                                </View>
                            )}
                            {user?.gender === GENDER.FEMALE && (
                                <View style={styles.card}>
                                    <View style={[styles.tierContainer, { marginVertical: 0 }]}>
                                        <TealSquareCheckbox
                                            name="clubswithFemaleMembers"
                                            label="Clubs with Female Members"
                                            labelStyle={[styles.lableTextStyle]}
                                            checkBoxContainerStyle={{ marginBottom: 0 }}
                                        />
                                    </View>
                                </View>
                            )}
                            <View style={styles.card}>
                                <View style={[styles.tierContainer, { marginVertical: 0 }]}>
                                    <TealSquareCheckbox
                                        name="favoriteClubs"
                                        label="Favorite Clubs"
                                        labelStyle={[styles.lableTextStyle]}
                                        checkBoxContainerStyle={{ marginBottom: 0 }}
                                    />
                                </View>
                            </View>
                        </View>
                    </ScrollView>

                    <View style={styles.btnContainer}>
                        <TealButton
                            text="Apply"
                            btnStyle={styles.btnStyle}
                            fontSize={Typography.FONT_SIZE_14}
                            fontFamily="Ubuntu-Regular"
                            textStyle={{ fontWeight: '400', lineHeight: Size.SIZE_18 }}
                        />
                    </View>
                </View>
            </Form>
        </SafeAreaView>
    );
};

export default OfferFilter;

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: colors.white,
    },
    mainContainer: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
        backgroundColor: colors.screenBG,
    },
    scrollViewContent: {
        paddingBottom: Spacing.SCALE_80, // Add extra padding for button
    },
    container: {
        marginTop: Spacing.SCALE_12,
        paddingHorizontal: Spacing.SCALE_16,
    },
    tierContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginVertical: Spacing.SCALE_8,
    },
    tierSubContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'flex-start',
    },

    btnStyle: {
        width: width - 30,
        borderRadius: 5,
    },
    titleStyle: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
    },
    lableTextStyle: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        color: colors.lightBlack,
    },
    fontSize16: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
    },
    horizontalLine: {
        flex: 1,
        height: 0.5,
        backgroundColor: colors.inputFieldBorderColor,
        marginVertical: Spacing.SCALE_20,
        zIndex: 1,
    },
    card: {
        padding: Spacing.SCALE_12,
        backgroundColor: colors.white,
        borderRadius: Spacing.SCALE_10,
        borderWidth: 1,
        borderColor: colors.lightgray,
        marginVertical: Spacing.SCALE_2,
    },
    countStyle: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: colors.fadeBlack,
        fontWeight: '400',
    },
    btnContainer: {
        backgroundColor: colors.whiteRGB,
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_12,
        borderTopWidth: 1,
        borderTopColor: colors.lightgray,
        ...Platform.select({
            ios: {
                shadowColor: colors.shadowColor,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
            },
            android: {
                elevation: 10,
                shadowOffset: {
                    width: 0,
                    height: 5,
                },
                shadowColor: colors.shadowColor,
                shadowRadius: 4,
                shadowOpacity: 0.5,
            },
        }),
    },
    radioTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        color: colors.lightBlack,
    },
});
