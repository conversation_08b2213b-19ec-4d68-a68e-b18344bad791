import React from 'react';
import { Modal, Pressable, StyleSheet, View, Text, FlatList } from 'react-native';

// utils, assets and theme imports
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { CrossIcon } from '../../../assets/svg';

const OtherTgMembersPopup = ({
    popupState,
    popupData,
}: {
    popupState: [boolean, (value: boolean) => void];
    popupData: string[];
}) => {
    const [tgMemberModal, setTgMemberModal] = popupState;

    const renderItem = ({ item }: { item: string }) => {
        return (
            <View style={styles.renderItemContainer}>
                <View style={styles.iconWrapper}>
                    <Text style={styles.initialsStyle}>
                        {item?.split(' ')[0]?.charAt(0)}
                        {item?.split(' ')[1]?.charAt(0)}
                    </Text>
                </View>
                <Text style={styles.groupNameStyle}>{item}</Text>
            </View>
        );
    };

    return (
        <Modal
            transparent={true}
            visible={tgMemberModal}
            animationType="fade"
            onRequestClose={() => setTgMemberModal(false)}
            style={{
                paddingHorizontal: 0,
                marginHorizontal: 0,
                paddingVertical: 0,
                marginVertical: 0,
            }}>
            <View style={{ flex: 1 }}>
                <Pressable style={{ flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.3)' }} />
                <View style={styles.popupWrapper}>
                    <View style={styles.headerWrapper}>
                        <Text style={styles.popupHeaderText}>{'All Groups'}</Text>
                        <Pressable style={styles.crossIconWrapper} onPress={() => setTgMemberModal(false)}>
                            <CrossIcon />
                        </Pressable>
                    </View>
                    <FlatList data={popupData} renderItem={renderItem} keyExtractor={(item) => item} />
                </View>
            </View>
        </Modal>
    );
};
export default OtherTgMembersPopup;

const styles = StyleSheet.create({
    popupWrapper: {
        width: '100%',
        minHeight: Spacing.SCALE_220,
        maxHeight: Spacing.SCALE_350,
        position: 'absolute',
        bottom: 0,
        backgroundColor: colors.white,
        borderTopLeftRadius: Size.SIZE_20,
        borderTopRightRadius: Size.SIZE_20,
        padding: Spacing.SCALE_16,
    },
    popupHeaderText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_20,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    headerWrapper: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: Spacing.SCALE_10,
        alignItems: 'center',
    },
    crossIconWrapper: {},
    divider: {
        height: 0.8,
        backgroundColor: colors.greyRgba,
    },
    renderItemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_10,
        marginVertical: Spacing.SCALE_10,
    },
    groupNameStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
    },
    initialsStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.tealRgb,
        textTransform: 'uppercase',
    },
    iconWrapper: {
        width: Size.SIZE_34,
        height: Size.SIZE_34,
        borderRadius: Size.SIZE_6,
        backgroundColor: colors.lightgray,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
