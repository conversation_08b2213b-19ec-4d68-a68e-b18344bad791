import {StyleSheet, Text, View} from 'react-native';
import React from 'react';

// Assets, theme, utils imports
import { FriendIcon } from '../../../assets/svg';
import {colors} from '../../../theme/theme';
import {Size, Spacing, Typography} from '../../../utils/responsiveUI';

const FriendsTagComponent = () => {
    return (
        <View style={styles.container}>
            <FriendIcon />
            <Text style={styles.textStyle}>friends</Text>
        </View>
    );
};

export default FriendsTagComponent;

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.opacityTeal,
        borderRadius: Size.SIZE_6,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        paddingHorizontal: Spacing.SCALE_8,
        marginLeft: Spacing.SCALE_10,
        columnGap: Spacing.SCALE_4,
        paddingVertical: Spacing.SCALE_5,
    },
    textStyle: {
        fontSize: Typography.FONT_SIZE_10,
        lineHeight: Size.SIZE_10,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.tealRgb,
        textTransform: 'capitalize',
    },
});
