import { FlatList, Pressable, StatusBar, StyleSheet, Text, View } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';

//components and context imports
import MapButton from '../../../components/map/MapButton';
import TgGroupMemberList from './TgGroupMemberList';
import { AuthContext } from '../../../context/AuthContext';
import showToast from '../../../components/toast/CustomToast';
import ProfileHeader from '../../../components/layout/ProfileHeader';
import RequestScreenSkelton from '../../requests/view/RequestScreenSkelton';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { GlobalContext } from '../../../context/contextApi';

//utils, interfaces, assets, functions, services and theme imports
import fetchMemberList from './fetchMemberList';
import { PAGINATION_LIMIT } from '../../my-TG-Stream-Chat/client';
import BroadCastPopup from '../played-friend screen/BroadCastPopup';
import { handleGetAllFriendsId } from '../../my-TG-Stream-Chat/action';
import { fetcher } from '../../../service/fetcher';
import { getMyTgMembersId } from './getMyTgMemberId';
import { BtnClubMarker, Send } from '../../../assets/svg';
import { MapClubDetail, TgGroupMember, UserClub } from '../../../interface';
import { RootStackParamList } from '../../../interface/type';
import { ERROR } from '../../../utils/constants/strings';
import { TG_GROUP_MEMBER_BROADCAST_MESSAGE, tokenCheckURL } from '../../../service/EndPoint';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';

interface TgGroupMemberScreenProps {
    club?: MapClubDetail;
    setSelectedClub?: (club: MapClubDetail) => void;
}

const TgGroupMemberScreen = (props: { route: { params: TgGroupMemberScreenProps } }) => {
    const { club, setSelectedClub } = props?.route?.params;
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { user } = useContext(AuthContext);
    const { actions, state } = useContext(GlobalContext);
    const [totalPage, setTotalPage] = useState();
    const [currentPage, setCurrentPage] = useState(1);
    const [membersList, setMemberList] = useState<TgGroupMember[]>([]);
    const [totalMemberCount, setTotalMemberCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [isPopupVisible, setIsPopupVisible] = useState(false);
    const [inputText, setInputText] = useState('');
    const [error, setError] = useState<string | undefined>();
    const [isMyClub, setIsMyClub] = useState(false);
    const [isAllMemberMuted, setAllMemberMuted] = useState(false);
    const [allChannelId, setAllChannelId] = useState<string[]>([]);

    useEffect(() => {
        handleSendBroadCastMessage();
        getTGGroupMemberList(true);
        handleGetAllFriendsId(user?.id, actions);
        return () => {
            setSelectedClub && setSelectedClub(state?.selectedClub);
        };
    }, []);

    // To get this club is mine or not
    useEffect(() => {
        user?.clubs?.map((myClub: UserClub) => {
            if (myClub?.club_id === club?.clubs?.id) {
                setIsMyClub(true);
            }
        });
    }, [user]);

    useEffect(() => {
        if (membersList.length === 1) {
            membersList?.map((data: TgGroupMember) => {
                if (data?.isMuted || !data?.isGenderCompatible) {
                    setAllMemberMuted(true);
                }
            });
        }
    }, [membersList]);

    // Get members id to send broadcast message
    const handleSendBroadCastMessage = async () => {
        const payload = {
            userId: user?.id,
            clubId: club?.clubs?.id,
        };
        const response = await getMyTgMembersId(payload);
        let tempFriend = response.members.map(({ id }: { id: string }) => id);
        setAllChannelId([...tempFriend]);
    };

    const getTGGroupMemberList = async (isFirstPage = false) => {
        setLoading(true);
        const payload = {
            userId: user?.id,
            clubId: club?.clubs?.id,
            page: isFirstPage ? 1 : currentPage,
            limit: PAGINATION_LIMIT,
        };
        const membersListRes = await fetchMemberList(payload);
        if (membersListRes?.status) {
            setLoading(false);
            setTotalMemberCount(membersListRes?.totalCount);
            setMemberList([...membersList, ...membersListRes?.members]);
            setCurrentPage(membersListRes?.currentPage);
            setTotalPage(membersListRes?.totalPages);
        } else {
            setLoading(false);
            showToast({});
        }
    };
    const handleOnReachEnd = async () => {
        setLoading(true);
        const payload = {
            userId: user?.id,
            clubId: club?.clubs?.id,
            page: currentPage + 1,
            limit: PAGINATION_LIMIT,
        };
        const membersListRes = await fetchMemberList(payload);
        if (membersListRes?.status) {
            setLoading(false);
            setMemberList([...membersList, ...membersListRes?.members]);
            setCurrentPage(membersListRes?.currentPage);
            setTotalPage(membersListRes?.totalPages);
        } else {
            setLoading(false);
            showToast({});
        }
    };
    const handleSendButton = () => {
        if (inputText) {
            fetcher({
                endpoint: TG_GROUP_MEMBER_BROADCAST_MESSAGE,
                method: 'POST',
                body: {
                    userId: user?.id,
                    groupMemberIds: allChannelId,
                    message: inputText,
                },
            }).then((res) => {
                if (res?.status) {
                    setInputText('');
                    setIsPopupVisible(!isPopupVisible);
                } else {
                    showToast({});
                    setIsPopupVisible(!isPopupVisible);
                }
            });
        } else {
            setError('Please enter a valid message');
        }
    };

    // Check user can create request or not
    const checkUserCreateRequest = () => {
        setLoading(true);
        fetcher({
            endpoint: tokenCheckURL,
            method: 'POST',
            body: {
                user_id: user?.id,
            },
        }).then((res) => {
            setLoading(false);
            if (res?.canCreate)
                navigation.navigate('CreateRequestMap', {
                    club,
                    createOneToOneRequest: false,
                    friend_id: '',
                });
            else {
                showToast({ type: ERROR, message: res?.message });
            }
        });
    };

    const handleBroadCastMessage = () => {
        setIsPopupVisible((prev) => !prev);
    };

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.whiteRGB} />
            <View style={{ backgroundColor: colors.whiteRGB, flex: 1 }}>
                <ProfileHeader
                    title={'TG Group Members'}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                    containerStyle={{
                        backgroundColor: colors.whiteRGB,
                        paddingBottom: Spacing.SCALE_10,
                    }}
                />
                {loading ? (
                    <RequestScreenSkelton screen="Common" />
                ) : (
                    <View style={styles.container}>
                        {/* UI of sub header */}
                        <View style={styles.subHeader}>
                            <Text style={styles.totalFriendStyle}>Group Members: {membersList?.length}</Text>
                            <Pressable style={styles.box1} onPress={handleBroadCastMessage}>
                                <Text style={styles.sendMessageToStyle}>Send Message to all</Text>
                                <Send />
                            </Pressable>
                        </View>
                        <FlatList
                            data={membersList}
                            renderItem={({ item }) => <TgGroupMemberList friendList={item} navigation={navigation} />}
                            onEndReachedThreshold={0.5}
                            onEndReached={() => {
                                if (currentPage * PAGINATION_LIMIT === membersList?.length) {
                                    handleOnReachEnd();
                                }
                            }}
                            style={{ paddingHorizontal: Spacing.SCALE_16 }}
                        />
                    </View>
                )}
            </View>
            {!isMyClub && !isAllMemberMuted && !loading && (
                <View style={styles.btnWrapper}>
                    <MapButton
                        label="Create Request to all"
                        buttonContainer={styles.btnContainer}
                        btnText={styles.btnTextStyle}
                        onPress={checkUserCreateRequest}
                        // @ts-ignore
                        Icon={BtnClubMarker}
                        isLoading={false}
                        isIncreaseIconSize={false}
                    />
                </View>
            )}
            {isPopupVisible && (
                <BroadCastPopup
                    popupState={[isPopupVisible, setIsPopupVisible]}
                    inputState={[inputText, setInputText]}
                    handleSendButton={handleSendButton}
                    errorState={[error, setError]}
                />
            )}
        </>
    );
};

export default TgGroupMemberScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.screenBG,
        paddingTop: Spacing.SCALE_10,
    },
    box1: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
    },
    btnContainer: {
        paddingHorizontal: Spacing.SCALE_15,
        marginBottom: Spacing.SCALE_10,
    },
    btnWrapper: {
        backgroundColor: colors.whiteRGB,
        height: Size.SIZE_80,
        justifyContent: 'center',
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
    btnTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_18,
        color: colors.whiteRGB,
    },
    subHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_15,
        marginBottom: Spacing.SCALE_8,
    },
    totalFriendStyle: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    sendMessageToStyle: {
        color: colors.tealRgb,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
});
