import { Image, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useMemo, useState } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthContext } from '../../../context/AuthContext';

//components and context imports
import FriendsTagComponent from './FriendsTagComponent';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { GlobalContext } from '../../../context/contextApi';
import OtherTgMembersPopup from './OtherTgMembersPopup';

//utils, interfaces, assets, functions, services, hooks and theme imports
import { fetcher } from '../../../service/fetcher';
import { CREATE_ONE_TO_ONE_CHANNEL } from '../../../service/EndPoint';
import useThumbnail from '../../../hooks/useThumbnail';
import constants from '../../../utils/constants/constants';
import { MutedIcon, MutualFriendIcon, Send } from '../../../assets/svg';
import { TgGroupMember } from '../../../interface';
import { RootStackParamList } from '../../../interface/type';
import config from '../../../config';
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';

interface TgGroupMemberListProps {
    friendList: TgGroupMember;
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

const TgGroupMemberList = ({ friendList, navigation }: TgGroupMemberListProps) => {
    const { client, setChannel } = useContext(StreamChatContext);
    const { state } = useContext(GlobalContext);
    const { user } = useContext(AuthContext);
    const [tgMemberModal, setTgMemberModal] = useState(false);
    const { actions } = useContext(GlobalContext);

    const {
        profilePhoto,
        fullName,
        id,
        isMuted,
        tier,
        username,
        visibleToPublic,
        groupNames,
        streamChannelId,
        isGenderCompatible,
    } = useMemo(() => {
        const {
            profilePhoto,
            fullName,
            id,
            isMuted,
            tier,
            username,
            visibleToPublic,
            groupNames,
            streamChannelId,
            isGenderCompatible,
        } = friendList;
        return {
            profilePhoto,
            fullName,
            id,
            isMuted,
            tier,
            username,
            visibleToPublic,
            groupNames,
            streamChannelId,
            isGenderCompatible,
        };
    }, []);

    const createOneToOneChannel = async (otherUserId: string) => {
        return fetcher({
            endpoint: CREATE_ONE_TO_ONE_CHANNEL,
            method: 'POST',
            body: {
                userId: user?.id,
                otherUserId: otherUserId,
            },
        }).then((res) => res?.channel?.id);
    };
    const handleSendButton = async () => {
        actions.setAppLoader(true);
        if (friendList && streamChannelId) {
            handleSendOneToOneMessage(streamChannelId);
        } else {
            const channelId = await createOneToOneChannel(friendList?.id);
            handleSendOneToOneMessage(channelId);
        }
    };

    const handleSendOneToOneMessage = async (streamChannelId: string) => {
        const channel = await client.queryChannels({
            members: { $in: [user?.id] },
            id: { $eq: streamChannelId },
            $or: [{ hidden: { $eq: true } }, { hidden: { $eq: false } }],
        });
        setChannel(channel[0]);
        actions?.updatedChannelAction(channel[0]);
        actions.setAppLoader(false);
        navigation.navigate('MessageScreen', {
            screen: config.routes.GROUPS,
        });
    };

    const redirectToUserProfile = () => {
        if (id !== user?.id) {
            navigation.navigate('UserProfileScreen', { selectedUser: { id: id } });
        }
    };

    return (
        <>
            <TouchableOpacity style={styles.cardWrapper} onPress={redirectToUserProfile}>
                <View style={styles.box}>
                    <View style={styles.imageWrapper}>
                        {profilePhoto ? (
                            <Image
                                source={{ uri: useThumbnail(profilePhoto, constants.ImageSize[128])?.thumbnailUrl }}
                                style={styles.img}
                            />
                        ) : (
                            <Text style={styles.initialsStyle}>{fullName?.trim().charAt(0)}</Text>
                        )}
                    </View>
                    <View style={styles.box1}>
                        {/* View to show name and club name */}
                        <View>
                            <View style={{ flexDirection: 'row' }}>
                                <View style={styles.nameWidthContainer}>
                                    <Text style={styles.name}>{state?.allFriendsId[id] ? fullName : username}</Text>
                                </View>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}>
                                    {state?.allFriendsId[id] && (
                                        <View
                                            style={{
                                                marginRight: Spacing.SCALE_5,
                                            }}>
                                            <FriendsTagComponent />
                                        </View>
                                    )}
                                    {isMuted && (
                                        <View
                                            style={{
                                                marginLeft: Spacing.SCALE_5,
                                            }}>
                                            <MutedIcon />
                                        </View>
                                    )}
                                </View>
                            </View>
                            {groupNames?.length ? (
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        marginTop: Spacing.SCALE_6,
                                        columnGap: Spacing.SCALE_6,
                                    }}>
                                    <View style={{ marginTop: Spacing.SCALE_2 }}>
                                        <MutualFriendIcon />
                                    </View>
                                    <View style={styles.clubWrapper}>
                                        <View style={styles.textWidthContainer}>
                                            <Text style={styles.club}>
                                                {groupNames[0]}
                                                {groupNames?.length > 1 && (
                                                    <Text
                                                        onPress={() => setTgMemberModal(true)}
                                                        style={styles.otherTextStyle}>
                                                        {' '}
                                                        +{groupNames?.length - 1}
                                                        {groupNames?.length > 2 ? ' others' : ' other'}
                                                    </Text>
                                                )}
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                            ) : null}
                        </View>
                        {/* View to show create request button and send message button */}
                        <View style={styles.btnWrapper}>
                            <Pressable style={styles.rightView} onPress={handleSendButton}>
                                <Send />
                            </Pressable>
                        </View>
                    </View>
                </View>
            </TouchableOpacity>
            {tgMemberModal && (
                <OtherTgMembersPopup popupState={[tgMemberModal, setTgMemberModal]} popupData={groupNames} />
            )}
        </>
    );
};

export default TgGroupMemberList;

const styles = StyleSheet.create({
    cardWrapper: {
        flex: 1,
        marginVertical: Spacing.SCALE_4,
        justifyContent: 'center',
        backgroundColor: colors.whiteRGB,
        padding: Spacing.SCALE_12,
        borderRadius: Spacing.SCALE_12,
        borderWidth: 1,
        borderColor: colors.lightgray,
    },
    imageWrapper: {
        width: Size.SIZE_40,
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_40,
        borderColor: colors.lightgray,
        borderWidth: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.lightgray,
    },
    box: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_17,
    },
    box1: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    rightView: {
        padding: Spacing.SCALE_8,
        borderWidth: Size.SIZE_1,
        borderColor: colors.darkteal,
        borderRadius: Size.SIZE_7,
        justifyContent: 'center',
        alignItems: 'center',
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        alignSelf: 'center',
    },
    img: {
        width: Size.SIZE_40,
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_40,
    },
    initialsStyle: {
        textTransform: 'capitalize',
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_25,
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
    },
    name: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    club: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        color: colors.fadeBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    clubWrapper: {
        // flexDirection: 'row',
        // alignItems: 'center',
        // marginTop: Spacing.SCALE_6,
    },
    textWidthContainer: {
        maxWidth: Size.SIZE_180,
    },
    nameWidthContainer: {
        maxWidth: Size.SIZE_130,
    },
    btnWrapper: {
        flexDirection: 'row',
    },
    divider: {
        borderWidth: 0.5,
        borderColor: colors.lightgray,
    },
    otherTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        textDecorationLine: 'underline',
    },
});
