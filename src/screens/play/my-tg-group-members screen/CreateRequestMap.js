import React, { useContext, useEffect, useState } from 'react';
import { View, Text, SafeAreaView, ScrollView } from 'react-native';
import { StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment';

import { AuthContext } from '../../../context/AuthContext';
import Form from '../../../forms/FormContext';
import Select from '../../../components/fields/Select';
import TextArea from '../../../components/fields/TextArea';
import { colors } from '../../../theme/theme';
import Spacer from '../../../components/layout/Spacer';
import GreenButton from '../../../components/buttons/GreenButton';
import CancelButton from '../../../components/buttons/CancelButton';
import * as yup from 'yup';
import { calculateGuestTimeRestrictionDates } from '../../../utils/helpers/CreateClubHelper';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { NEW_CREATE_REQUEST, SEARCH_CLUBS } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';
import DateRangeModal from '../../../components/modals/DateRangeModal';
import ScreenHeader from '../../../components/layout/StreamHeader';
import {
    FETCH_INTERNATIONAL_CLUB_DURATION,
    FETCH_LOACAL_CLUB_DURATION,
} from '../../../graphql/queries/clubDuration';
import useQuery from '../../../hooks/useQuery';
import DateRangeFieldNew from '../../../components/fields/DateRangeFieldNew';
import showToast from '../../../components/toast/CustomToast';
import TGLoader from '../../../components/layout/TGLoader';
import SelectClubName from '../../../components/fields/SelectClubName';
import { REQUEST_NOTES_LIMIT } from '../../../utils/constants/validationConstants';
import WarningIconRed from '../../../assets/svg/WarningIconRed.svg'
import WarningIconYellow from '../../../assets/svg/WarningIconYellow.svg'

const CreateRequestMap = ({ route }) => {
    const { user } = useContext(AuthContext);
    const { club, createOneToOneRequest, friend_id, screen } = route?.params;
    const [disabledDates, setDisabledDates] = useState();
    const [clubDetails, setClubDetails] = useState(null);
    const [dateRangeModal, setDateRangeModal] = useState();
    const [loading, setLoading] = useState({
        btnLoading: false,
        screenLoading: false,
    });
    const [onChangeFormValue, setOnChangeFormValue] = useState();
    const [searchedClub, setSearchedClub] = useState();
    const navigation = useNavigation();

    // Querry to get local and international duration to create request
    let localClub = useQuery(FETCH_LOACAL_CLUB_DURATION);
    let internationalClub = useQuery(FETCH_INTERNATIONAL_CLUB_DURATION);

    useEffect(() => {
        getClubDetails();
    }, []);

    useEffect(() => {
        // Find out the time restriction and disable dates
        if (
            ((club && club?.clubs?.closurePeriods) ||
                club?.clubs?.guest_time_restrictions) &&
            !createOneToOneRequest
        ) {
            const dates = calculateGuestTimeRestrictionDates(
                club?.clubs?.closurePeriods,
                club?.clubs?.guest_time_restrictions,
            );
            setDisabledDates(dates);
        } else {
            if (
                (clubDetails && clubDetails?.closure_period) ||
                clubDetails?.guest_time_restrictions
            ) {
                const dates = calculateGuestTimeRestrictionDates(
                    clubDetails?.closurePeriods,
                    clubDetails?.guest_time_restrictions,
                );
                setDisabledDates(dates);
            }
        }
    }, [club, clubDetails]);

    // Get the club details
    const getClubDetails = (index = 0) => {
        setLoading((prev) => ({ ...prev, screenLoading: true }));
        const body = {
            searchValue: club?.clubs?.name || club[index],
            userId: user?.id,
            exactMatch: true,
        };
        fetcher({
            endpoint: SEARCH_CLUBS,
            method: 'POST',
            body,
        }).then((res) => {
            setLoading((prev) => ({ ...prev, screenLoading: false }));
            setClubDetails(res?.clubs[0]);
        });
    };

    // Get the request is created from local or international
    const handleMonthNotes = () => {
        if (clubDetails) {
            if (
                clubDetails?.country_code?.toLowerCase() ===
                user?.phone_number_details?.countryCode?.toLowerCase()
            ) {
                return `You can create a request to play for upto ${localClub?.data?.system_setting[0]?.value?.value} months only`;
            } else {
                return `You can create a request to play for upto ${internationalClub?.data?.system_setting[0]?.value?.value} months only`;
            }
        } else {
            if (
                clubDetails?.country_code?.toLowerCase() ===
                user?.phone_number_details?.countryCode?.toLowerCase()
            ) {
                return `You can create a request to play for upto ${localClub?.data?.system_setting[0]?.value?.value} months only`;
            } else {
                return `You can create a request to play for upto ${internationalClub?.data?.system_setting[0]?.value?.value} months only`;
            }
        }
    };

    // Function which handle create request
    const handleCreateRequest = (form) => {
        setLoading((prev) => ({ ...prev, btnLoading: true }));
        const createRequestPayload = {
            userId: user?.id,
            clubId: clubDetails?.id || searchedClub?.id,
            players: form.number_of_players[0],
            message: form.message,
            startDate: moment(form.start_date).format('MM/DD/YYYY'),
            endDate: moment(form.end_date).format('MM/DD/YYYY'),
            requestForAll: false,
        };
        if (createOneToOneRequest) {
            createRequestPayload.isForAFriend = true;
            createRequestPayload.friendId = friend_id;
        } else {
            if (screen === 'Friends Found') {
                createRequestPayload.isForAllFriends = true;
                createRequestPayload.isForMyTgGroupMembers = false;
                createRequestPayload.isForAFriend = false;
            } else if (screen === 'Friends Found One To One') {
                createRequestPayload.isForAFriend = true;
                createRequestPayload.friendId = friend_id;
            } else if (screen === 'MyTg Group Member One To One') {
                (createRequestPayload.isForAGroupMember = true),
                    (createRequestPayload.groupMemberId = friend_id);
            } else if (screen === 'allFriendScreen') {
                createRequestPayload.isForAllFriends = false;
                createRequestPayload.isForMyTgGroupMembers = false;
                createRequestPayload.isForAFriend = true;
                createRequestPayload.friendId = friend_id;
            } else {
                createRequestPayload.isForAllFriends = false;
                createRequestPayload.isForMyTgGroupMembers = true;
                createRequestPayload.isForAFriend = false;
            }
        }
        fetcher({
            endpoint: NEW_CREATE_REQUEST,
            method: 'POST',
            body: createRequestPayload,
        }).then((res) => {
            if (res?.status) {
                setLoading((prev) => ({ ...prev, btnLoading: false }));
                navigation.goBack();
            } else {
                showToast({ message: res?.message });
                setLoading(false);
            }
        });
    };

    return (
        <>
            <ScreenHeader screenName="Create Request" />
            <Form
                initialValues={{
                    clubId: club && club?.clubs?.id,
                    club,
                    number_of_players: '1',
                }}
                onUpdate={setOnChangeFormValue}
                onSubmit={(form) => handleCreateRequest(form)}
                validationSchema={
                    club?.length > 1
                        ? {
                            club_id: yup
                                .string()
                                .required('Club is required'),
                            number_of_players: yup
                                .string()
                                .required('Number of players is required'),
                            start_date: yup
                                .string()
                                .required('Club is required'),
                            end_date: yup
                                .string()
                                .required('Date range is required'),
                            message: yup
                                .string()
                                .required('Message is required'),
                        }
                        : {
                            number_of_players: yup
                                .string()
                                .required('Number of players is required'),
                            start_date: yup
                                .string()
                                .required('Club is required'),
                            end_date: yup
                                .string()
                                .required('Date range is required'),
                            message: yup
                                .string()
                                .required('Message is required'),
                        }
                }>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={styles.contentContainerStyle}
                    style={styles.safeAreaContainer}>
                    <View
                        style={{
                            paddingHorizontal: Spacing.SCALE_16,
                            paddingBottom: Spacing.SCALE_10,
                        }}>
                        <Text style={styles.inputLabelText}>Course</Text>
                        {!createOneToOneRequest ? (
                            <View style={styles.clubNameWrapper}>
                                <Text style={styles.inputFieldText}>
                                    {clubDetails?.name}
                                </Text>
                            </View>
                        ) : (
                            <SelectClubName
                                name="club_id"
                                title="Club"
                                placeholder="Select Club"
                                color={colors.dark_charcoal}
                                fontWeight={'400'}
                                fontFamily="Ubuntu-Medium"
                                fontSize={Typography.FONT_SIZE_14}
                                placeholderNullValue={true}
                                disableFormatting
                                isIcon={user.clubs.length > 1 ? true : false}
                                options={club.map((club, index) => ({
                                    label: club,
                                    value: index,
                                }))}
                                setClub={setSearchedClub}
                                getClubDetailsCb={getClubDetails}
                                selectedClub={clubDetails}
                            />
                        )}
                        {clubDetails?.requestRestriction?.errorMessage?.length && !friend_id && screen !== 'Friends Found' && <View style={[styles.warningMessageContainer,
                        clubDetails?.requestRestriction?.isBlocked
                            ? { backgroundColor: 'rgba(224, 94, 82, 0.12)', marginTop: Spacing.SCALE_15 }
                            : { backgroundColor: 'rgba(241, 203, 0, 0.12)', marginTop: Spacing.SCALE_8 }]}>
                            <View style={styles.warningIconContainer}>
                                {clubDetails?.requestRestriction?.isBlocked
                                    ? <WarningIconRed />
                                    : <WarningIconYellow />}
                            </View>
                            <Text style={[styles.warningTextStyle]} >
                                {clubDetails?.requestRestriction?.errorMessage}
                            </Text>
                        </View>}
                        <Spacer />

                        <Text style={styles.inputLabelText}>
                            Number of people
                        </Text>
                        <Select
                            name="number_of_players"
                            placeholder="Number Of People"
                            placeholderNullValue={true}
                            options={['1', '2', '3']}
                            borderBottomColor={colors.silver}
                            fontSize={Typography.FONT_SIZE_14}
                        />
                        <Spacer />

                        <Text style={styles.inputLabelText}>Date</Text>
                        <DateRangeFieldNew
                            setModal={(value) => {
                                setDateRangeModal({
                                    ...value,
                                    disabledDates,
                                });
                            }}
                            textColor={colors.lightBlack}
                            borderBottomColor={colors.silver}
                            placeholder="Select date"
                            placeholderFontWeight="400"
                            placeholderFontSize={Typography?.FONT_SIZE_14}
                            selectedClubNew={clubDetails}
                        />
                        <Spacer />

                        <TextArea
                            name="message"
                            placeholder="Enter here...."
                            title="Additional Notes"
                            height={78}
                            inputTitleStyle={styles.inputLabelText}
                            style={styles.textAreaStyle}
                            maxLength={REQUEST_NOTES_LIMIT}
                            maxCount={REQUEST_NOTES_LIMIT}
                        />
                        <Spacer />
                    </View>

                    <View style={{ flexGrow: 1 }}>
                        <SafeAreaView style={styles.SafeAreaViewStyle}>
                            <View style={styles.bodyWrapper}>
                                <View style={styles.btnWrapper}>
                                    <CancelButton
                                        width="80%"
                                        onPress={() => navigation.goBack()}
                                        customStyle={styles.customStyle}
                                        text="Cancel"
                                    />
                                </View>
                                <GreenButton
                                    text={'Post Request'}
                                    loading={loading?.btnLoading}
                                    width={'44%'}
                                    disabled={clubDetails?.requestRestriction?.isBlocked && !friend_id && screen !== 'Friends Found'}
                                />
                            </View>
                        </SafeAreaView>
                    </View>
                </ScrollView>
            </Form>
            {dateRangeModal && (
                <DateRangeModal
                    modal={{ ...dateRangeModal, monthNote: handleMonthNotes() }}
                    setModal={setDateRangeModal}
                />
            )}
            {loading?.screenLoading && (
                <TGLoader loading={loading?.screenLoading} />
            )}
        </>
    );
};

export default CreateRequestMap;

const styles = StyleSheet.create({
    safeAreaContainer: {
        backgroundColor: colors.white,
    },
    inputLabelText: {
        fontFamily: 'Ubuntu-Regular',
        color: colors.darkgray,
        fontSize: Typography.FONT_SIZE_12,
    },
    inputFieldText: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        lineHeight: Spacing.SCALE_21,
    },
    textAreaStyle: {
        fontSize: Typography.FONT_SIZE_14,
        paddingHorizontal: Spacing.SCALE_13,
        paddingVertical: Spacing.SCALE_12,
        fontWeight: '400',
        lineHeight: Spacing.SCALE_21,
        fontFamily: 'Ubuntu-Regular',
        borderWidth: 1,
        borderColor: colors.borderGray,
        backgroundColor: colors.white,
        borderRadius: 4,
        marginTop: Spacing.SCALE_10,
        color: colors.lightBlack,
    },
    textStyle: {
        fontSize: colors.lightBlack,
    },
    clubNameWrapper: {
        width: '100%',
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: colors.silver,
    },
    contentContainerStyle: {
        flexGrow: 1,
        justifyContent: 'flex-end',
    },
    SafeAreaViewStyle: {
        flexGrow: 1,
        justifyContent: 'flex-end',
        alignItems: 'baseline',
        alignSelf: 'flex-end',
    },
    bodyWrapper: {
        flexDirection: 'row',
        marginTop: 20,
        paddingBottom: 30,

        paddingHorizontal: Spacing.SCALE_20,
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    btnWrapper: {
        flexGrow: 1,
    },
    customStyle: {
        borderWidth: 1,
        borderColor: colors.gray99,
        borderRadius: 5,
    },
    warningMessageContainer: {
        paddingVertical: Spacing.SCALE_8,
        width: '100%',
        paddingRight: 45,
        flexDirection: 'row',
        borderRadius: 8
    },
    warningIconContainer: {
        paddingRight: Spacing.SCALE_10,
        paddingLeft: Spacing.SCALE_10,
        paddingTop: Spacing.SCALE_2,
    },
    warningTextStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
        color: colors.fadeBlack
    }
});
