import { Alert, Image, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useMemo, useState } from 'react';
import { useNavigation } from '@react-navigation/native';

import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { AuthContext } from '../../../context/AuthContext';
import config from '../../../config';
import { GlobalContext } from '../../../context/contextApi';
import { fetcher } from '../../../service/fetcher';
import { tokenCheckURL } from '../../../service/EndPoint';
import { CreateRequestIconTeal, MapLocationIconNew, MutedIcon, Send } from '../../../assets/svg';
import { RootStackParamList } from '../../../interface/type';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MapClubDetail } from '../../../interface';
import { apiServices } from '../../../service/apiServices';

interface FriendListInClubProps {
    friendList: any;
    isMyClub: boolean;
    club: MapClubDetail;
}

const FriendListInClub = ({ friendList, isMyClub, club }: FriendListInClubProps) => {
    const { client, setChannel } = useContext(StreamChatContext);
    const { actions } = useContext(GlobalContext);
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { user } = useContext(AuthContext);
    const [loading, setLoading] = useState(false);

    // Destructure list of friends in this club
    const { name, profilePhoto, email, friend_id, id, stream_channel_id, clubs, is_muted } = useMemo(() => {
        const {
            item: {
                friend_id,
                id,
                stream_channel_id,
                is_muted,
                friend_info: { email, name, phone, profilePhoto, clubs },
            } = {},
        } = friendList;
        return {
            name,
            profilePhoto,
            email,
            friend_id,
            id,
            stream_channel_id,
            clubs,
            is_muted,
        };
    }, []);

    const handleSendOneToOneMessage = async () => {
        const channel = await client.queryChannels({
            members: { $in: [user?.id] },
            id: { $eq: stream_channel_id },
            $or: [{ hidden: { $eq: true } }, { hidden: { $eq: false } }],
        });
        setChannel(channel[0]);
        actions?.updatedChannelAction(channel[0]);
        navigation.navigate('MessageScreen', {
            screen: config.routes.GROUPS,
        });
    };

    const redirectToUserProfile = () => {
        if (friend_id !== user?.id) {
            navigation.navigate('UserProfileScreen', { selectedUser: { id: friend_id } });
        }
    };

    // Check user can create request or not
    const checkUserCreateRequest = () => {
        setLoading(true);
        apiServices.checkCanCreateRequest(user, false).then((res) => {
            setLoading(false);
            if (res?.canCreate)
                navigation.navigate('CreateRequestMap', {
                    club: club,
                    createOneToOneRequest: false,
                    friend_id: friend_id,
                    screen: 'Friends Found One To One',
                });
            else {
                navigation.navigate('DeleteChannelConfirmationPopup', {
                    popupSubText: res?.message,
                    firstBtnLabel: 'Cancel',
                    secondBtnLabel: 'Ok',
                });
            }
        });
    };

    return (
        <>
            <TouchableOpacity style={styles.cardWrapper} onPress={redirectToUserProfile}>
                <View style={styles.box}>
                    <View style={styles.imageWrapper}>
                        {profilePhoto ? (
                            <Image source={{ uri: profilePhoto }} style={styles.img} />
                        ) : (
                            <Text style={styles.initialsStyle}>{name?.trim(' ')[0]}</Text>
                        )}
                    </View>
                    <View style={styles.box1}>
                        {/* View to show name and club name */}
                        <View style={{ rowGap: Spacing.SCALE_7 }}>
                            <View style={styles.textWidthContainer}>
                                <Text style={styles.name}>{name}</Text>
                                {is_muted && (
                                    <View
                                        style={{
                                            marginLeft: Spacing.SCALE_5,
                                        }}>
                                        <MutedIcon />
                                    </View>
                                )}
                            </View>

                            <View style={styles.clubWrapper}>
                                <MapLocationIconNew height={Size.SIZE_16} width={Size.SIZE_16} />
                                <View style={styles.textWidthContainer}>
                                    <Text style={styles.club}>
                                        {clubs.length > 0 ? (clubs.length > 1 ? `${clubs[0]}` : clubs[0]) : ''}
                                        {clubs.length > 1 && (
                                            <Text style={{ fontWeight: '500', color: colors.lightBlack }}>
                                                {' '}
                                                +{clubs.length - 1} others
                                            </Text>
                                        )}
                                    </Text>
                                </View>
                            </View>
                        </View>
                        {/* View to show create request button and send message button */}
                        <View style={styles.btnWrapper}>
                            {!is_muted && !isMyClub && (
                                <Pressable style={styles.rightView} onPress={checkUserCreateRequest}>
                                    <CreateRequestIconTeal />
                                </Pressable>
                            )}
                            <Pressable style={styles.rightView} onPress={handleSendOneToOneMessage}>
                                <Send />
                            </Pressable>
                        </View>
                    </View>
                </View>
            </TouchableOpacity>
        </>
    );
};

export default FriendListInClub;

const styles = StyleSheet.create({
    cardWrapper: {
        flex: 1,
        marginVertical: Spacing.SCALE_4,
        justifyContent: 'center',
        paddingHorizontal: Spacing.SCALE_15,
        backgroundColor: colors.whiteRGB,
        padding: Spacing.SCALE_12,
        borderRadius: Spacing.SCALE_12,
        borderWidth: 1,
        borderColor: colors.lightgray,
    },
    imageWrapper: {
        width: Size.SIZE_45,
        height: Size.SIZE_45,
        borderRadius: Size.SIZE_40,
        borderColor: colors.lightgray,
        borderWidth: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.lightgray,
    },
    box: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_17,
    },
    box1: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    rightView: {
        padding: Spacing.SCALE_8,
        borderWidth: Size.SIZE_1,
        borderColor: colors.darkteal,
        borderRadius: Size.SIZE_7,
        justifyContent: 'center',
        alignItems: 'center',
        width: Size.SIZE_30,
        height: Size.SIZE_30,
        alignSelf: 'center',
        marginHorizontal: Spacing.SCALE_5,
    },
    img: {
        width: Size.SIZE_45,
        height: Size.SIZE_45,
        borderRadius: Size.SIZE_40,
    },
    initialsStyle: {
        textTransform: 'capitalize',
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_25,
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
    },
    name: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    club: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        color: colors.fadeBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    clubWrapper: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_4,
        width: Size.SIZE_150,
    },
    textWidthContainer: {
        width: Size.SIZE_130,
        flexDirection: 'row',
    },
    btnWrapper: {
        flexDirection: 'row',
    },
    divider: {
        borderWidth: 0.5,
        borderColor: colors.lightgray,
    },
});
