import React, { useContext, useEffect, useState } from 'react';
import { View, StyleSheet, Text, FlatList, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

//components and context imports
import ShowFriends from './ShowFriends';
import BroadCastPopup from './BroadCastPopup';
import { AuthContext } from '../../../context/AuthContext';
import { GlobalContext } from '../../../context/contextApi';
import RequestScreenSkelton from '../../requests/view/RequestScreenSkelton';

//utils, interfaces, assets, functions, services, hooks and theme imports
import { BROADCAST_MESSAGE, playedFriendList } from '../../../service/EndPoint';
import { Send } from '../../../assets/svg';
import ProfileHeader from '../../../components/layout/ProfileHeader';
import { Friend, MapClubDetail } from '../../../interface';
import { RootStackParamList } from '../../../interface/type';
import { fetcher } from '../../../service/fetcher';
import { Size, Typography, Spacing } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';

interface PlayedFriendScreenProps {
    route: {
        params: {
            club: MapClubDetail;
            setSelectedClub: (club: MapClubDetail) => void;
        };
    };
}

const PlayedFriendScreen = ({ route }: PlayedFriendScreenProps) => {
    const { club, setSelectedClub } = route?.params;
    const { user, refreshUser } = useContext(AuthContext);
    const { state } = useContext(GlobalContext);
    const [playedFriendsList, setPlayedFriendsList] = useState([]);
    const [isPopupVisible, setIsPopupVisible] = useState(false);
    const [allChannelId, setAllChannelId] = useState<string[]>([]);
    const [inputText, setInputText] = useState('');
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

    const onPressBackButton = () => {
        navigation.goBack();
    };

    useEffect(() => {
        getPlayedFriends();
        return () => {
            setSelectedClub(state?.selectedClub);
        };
    }, [club]);

    useEffect(() => {
        if (playedFriendsList?.length) {
            getAllFriendsChannelId();
        }
    }, [playedFriendsList]);

    const getAllFriendsChannelId = () => {
        let tempChannelId: string[] = [];
        playedFriendsList?.map((data: Friend) => {
            tempChannelId.push(data?.stream_channel_id);
        });
        setAllChannelId(tempChannelId);
    };

    const getPlayedFriends = () => {
        setLoading(true);
        const body = {
            userId: user?.id,
            clubId: club?.clubs?.id,
        };
        fetcher({
            endpoint: playedFriendList,
            method: 'POST',
            body,
        })
            .then((data) => {
                setPlayedFriendsList(data?.friendDetails);
                setLoading(false);
            })
            .catch(console.log);
    };

    const handleSendButton = () => {
        if (inputText) {
            fetcher({
                endpoint: BROADCAST_MESSAGE,
                method: 'POST',
                body: {
                    userId: user?.id,
                    channels: allChannelId,
                    message: inputText,
                    friendsOnly: true,
                },
            }).then((res) => {
                if (res?.status) {
                    setInputText('');
                    setIsPopupVisible(!isPopupVisible);
                }
            });
        } else {
            setError('Please enter a valid message');
        }
    };

    return (
        <>
            <View style={styles.container}>
                <ProfileHeader
                    title={'Friends Played'}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                    containerStyle={{
                        backgroundColor: colors.whiteRGB,
                        paddingBottom: Spacing.SCALE_10,
                    }}
                />
                {loading ? (
                    <RequestScreenSkelton screen="Common" />
                ) : (
                    <View style={styles.mainViewStyle}>
                        <View style={styles.topViewInsideMainView}>
                            <Text style={styles.TotalFriends}>Total Friends: {playedFriendsList?.length}</Text>
                            <Pressable
                                style={styles.sendMessageView}
                                onPress={() => setIsPopupVisible(!isPopupVisible)}>
                                <Text style={styles.SendMessage}>Send message to all</Text>
                                <Send style={styles.sendIcon} />
                            </Pressable>
                        </View>
                        <FlatList
                            showsVerticalScrollIndicator={false}
                            data={playedFriendsList}
                            renderItem={({ item }) => <ShowFriends item={item} navigation={navigation} />}
                            keyExtractor={(item: Friend) => item?.id?.toString()}
                        />
                    </View>
                )}
            </View>
            {isPopupVisible && (
                <BroadCastPopup
                    popupState={[isPopupVisible, setIsPopupVisible]}
                    inputState={[inputText, setInputText]}
                    handleSendButton={handleSendButton}
                    errorState={[error, setError]}
                />
            )}
        </>
    );
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
    },
    mainViewStyle: {
        flex: 1,
        paddingHorizontal: Spacing.SCALE_16,
        backgroundColor: colors.lightgray,
    },
    topViewInsideMainView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_8,
        marginTop: Spacing.SCALE_12,
    },
    TotalFriends: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    SendMessage: {
        color: colors.tealRgb,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    sendMessageView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
    },
    sendIcon: {
        width: Size.SIZE_16,
        height: Size.SIZE_16,
    },
    loaderStyle: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
});
export default PlayedFriendScreen;
