import React, { useContext, useMemo } from 'react';
import { View, Text, StyleSheet, Pressable, TouchableOpacity } from 'react-native';
import FastImage from 'react-native-fast-image';

//context imports
import { StreamChatContext } from '../../../context/StreamChatContext';
import { AuthContext } from '../../../context/AuthContext';
import { GlobalContext } from '../../../context/contextApi';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

//utils, interfaces, assets, functions, services, hooks and theme imports
import useThumbnail from '../../../hooks/useThumbnail';
import config from '../../../config';
import constants from '../../../utils/constants/constants';
import { Friend } from '../../../interface';
import { RootStackParamList } from '../../../interface/type';
import { MapLocationIconNew, Send } from '../../../assets/svg';
import { Size, Typography, Spacing } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';

interface ShowFriendsProps {
    item: Friend;
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

const ShowFriends = ({ item, navigation }: ShowFriendsProps) => {
    const { client, setChannel } = useContext(StreamChatContext);
    const { user, refreshUser } = useContext(AuthContext);
    const { actions } = useContext(GlobalContext);
    const { friend_id, clubs, email, name, phone, profilePhoto, id, notes, stream_channel_id } = useMemo(() => {
        const {
            friend_id,
            friend_info: { clubs, email, name, phone, profilePhoto, id, notes } = {},
            stream_channel_id,
        } = item;
        return {
            friend_id,
            clubs,
            email,
            name,
            phone,
            profilePhoto,
            id,
            notes,
            stream_channel_id,
        };
    }, [item]);

    const handleShowClub = () => {
        if (clubs?.length === 1) {
            return clubs[0];
        } else {
            return `${clubs?.[0]} +${clubs?.length} Others`;
        }
    };

    const handleSendOneToOneMessage = async () => {
        const channel = await client.queryChannels({
            members: { $in: [user?.id] },
            id: { $eq: stream_channel_id },
            $or: [{ hidden: { $eq: true } }, { hidden: { $eq: false } }],
        });
        setChannel(channel[0]);
        actions?.updatedChannelAction(channel[0]);
        navigation.navigate('MessageScreen', { screen: config.routes.GROUPS });
    };

    const redirectToUserProfile = () => {
        if (friend_id !== user?.id) {
            navigation.navigate('UserProfileScreen', { selectedUser: { id: friend_id } });
        }
    };

    return (
        <TouchableOpacity style={styles.container} onPress={redirectToUserProfile}>
            <View style={styles.leftView}>
                {profilePhoto ? (
                    <FastImage
                        source={{
                            uri: useThumbnail(profilePhoto, constants.ImageSize[128])?.thumbnailUrl,
                            priority: FastImage.priority.normal,
                        }}
                        resizeMode={FastImage.resizeMode.contain}
                        style={styles.img}
                    />
                ) : (
                    <View style={styles.initialsWrapper}>
                        <Text style={styles.initialsStyle}>{name?.trim()?.charAt(0)}</Text>
                    </View>
                )}
                <View style={styles.middleContainer}>
                    <Text style={styles.username}>{name}</Text>
                    <View style={styles.middleContainerBottom}>
                        <View style={styles.locationIconWrapper}>
                            <MapLocationIconNew />
                        </View>
                        <View style={styles.textWrapper}>
                            <Text style={styles.clubTextStyle}>{handleShowClub()}</Text>
                        </View>
                    </View>
                </View>
            </View>
            <Pressable style={styles.rightView} onPress={handleSendOneToOneMessage}>
                <Send style={styles.sendIcon} />
            </Pressable>
        </TouchableOpacity>
    );
};
const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: Spacing.SCALE_12,
        borderRadius: Size.SIZE_12,
        backgroundColor: colors.whiteRGB,
        marginVertical: Spacing.SCALE_4,
    },
    img: {
        width: Size.SIZE_40,
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_40,
    },
    initialsStyle: {
        textAlign: 'center',
        textTransform: 'capitalize',
        fontSize: Typography.FONT_SIZE_25,
        color: colors.darkteal,
        fontFamily: 'Ubuntu-Medium',
    },
    leftView: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_17,
    },
    rightView: {
        padding: Spacing.SCALE_8,
        borderWidth: Size.SIZE_1,
        borderColor: colors.darkteal,
        borderRadius: Size.SIZE_7,
        justifyContent: 'center',
        alignItems: 'center',
        width: Size.SIZE_30,
        height: Size.SIZE_30,
        alignSelf: 'center',
    },
    middleContainer: {
        rowGap: Spacing.SCALE_7,
    },
    middleContainerBottom: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_6,
    },

    username: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    clubTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.fadeBlack,
        fontFamily: 'Ubuntu-Light',
        fontWeight: '400',
    },
    sendIcon: {
        width: Size.SIZE_16,
        height: Size.SIZE_16,
    },
    textWrapper: {
        width: Spacing.SCALE_200,
    },
    initialsWrapper: {
        width: Size.SIZE_40,
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_40,
        justifyContent: 'center',
        backgroundColor: colors.lightgray,
    },
    locationIconWrapper: {
        // marginTop: Platform.OS === 'ios' ? Spacing.SCALE_2 : Spacing.SCALE_3,
        // marginRight: Spacing.SCALE_5,
    },
});
export default ShowFriends;
