import { GET_GOLFER_PROFILE_DATA_V3 } from '../EndPoint';
import { fetcher } from '../fetcher';

export const getGolferProfilePhoto = async (userId: string, golferId: string, actions: any) => {
    return fetcher({
        endpoint: GET_GOLFER_PROFILE_DATA_V3,
        method: 'POST',
        body: {
            userId: userId,
            golferId: golferId,
        },
    }).then((res) => {
        actions.userProfile(res?.golferInfo[0]);
        return res;
    });
};
