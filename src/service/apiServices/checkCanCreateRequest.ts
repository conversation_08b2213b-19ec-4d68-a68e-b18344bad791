import { User } from '../../interface';
import { tokenCheckURL } from '../EndPoint';
import { fetcher } from '../fetcher';

// Check user can create request or not
export const checkCanCreateRequest = async (user: User, isRequestToAll?: boolean) => {
    return await fetcher({
        endpoint: tokenCheckURL,
        method: 'POST',
        body: {
            user_id: user?.id,
            isRequestToAll: isRequestToAll,
        },
    }).then((res) => res);
};
