import { TOGGLE_PEGBOARD_VISIBILITY } from '../EndPoint';
import { fetcher } from '../fetcher';

interface TogglePegboardVisibilityParams {
    pegboardId: string;
    isHidden: boolean;
    userId: string;
}

export const togglePegboardVisibility = async (body: TogglePegboardVisibilityParams) => {
    return await fetcher({
        endpoint: TOGGLE_PEGBOARD_VISIBILITY,
        method: 'POST',
        body,
    }).then((res) => {
        return res;
    });
};
