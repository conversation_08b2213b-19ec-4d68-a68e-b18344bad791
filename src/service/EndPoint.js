// import {Config.API_END_POINT, Config.HASURA_END_POUNT, Config.HASURA_WEB_SOCKET} from 'react-native-dotenv';

const googleAPI = 'AIzaSyBw_Oqjf5qsgMuriiqKpgHRmlzORSdouzM';

const mapsPlaceApis = 'https://maps.googleapis.com/maps/api/place/';

import Config from 'react-native-config';

export const autocomplete = `${mapsPlaceApis}autocomplete/json?types=(regions)&key=${googleAPI}&input=`;
export const placeDetails = `${mapsPlaceApis}details/json?key=${googleAPI}&fields=geometry&place_id=`;

// export const ENV = 'dev';
// export const API_END_POINT = "https://thousandgreens-web-dev.herokuapp.com/";
// const HASURA_END_POUNT = "https://thousand-greens-hasura-lpdev.herokuapp.com/";
// const HASURA_WEB_SOCKET = "wss://thousand-greens-hasura-lpdev.herokuapp.com/";

export const ENV = 'staging';
export const API_END_POINT = 'https://thousand-greens-staging.herokuapp.com/';
const HASURA_END_POUNT = 'https://tg-staging-ha-db.herokuapp.com/';
const HASURA_WEB_SOCKET = 'wss://tg-staging-ha-db.herokuapp.com/';

// export const ENV = 'production';
// export const API_END_POINT = "https://thousandgreens.com/";
// const HASURA_END_POUNT = "https://thousandgreens-hasura.herokuapp.com/";
// const HASURA_WEB_SOCKET = "wss://thousandgreens-hasura.herokuapp.com/";

export const validateUsernameUrl = `${Config.API_END_POINT}api/auth/validateUsername`;
export const validatePhoneNumberUrl = `${Config.API_END_POINT}api/auth/validatePhoneNumber`;

export const mapClubsUrl = `${Config.API_END_POINT}api/map/clubs`;
export const mapClubsUrlV2 = `${Config.API_END_POINT}api/map/clubsV2`;
export const mapClubsUrlV2_1 = `${Config.API_END_POINT}api/map/clubsV2.1`;
export const clubSearch = `${Config.API_END_POINT}api/map/clubSearch`;
export const clubSearchV2 = `${Config.API_END_POINT}api/newclubs/searchclubs`;
export const fetchclubsNew = `${Config.API_END_POINT}api/newclubs/v3/fetch-clubs`;
export const fetchclubs = `${Config.API_END_POINT}api/newclubs/fetchclubs`;
export const clubDetails = `${Config.API_END_POINT}api/map/clubDetails`;

export const completeRegistrationUrl = `${Config.API_END_POINT}api/auth/completeRegistration`;
export const sendVerificationCodeUrl = `${Config.API_END_POINT}api/auth/sendVerificationCode`;
export const sendVerificationCodeUrlV2 = `${Config.API_END_POINT}api/signup-V2/send-phone-verification-code`;

//Old APIs
export const verifyEmailVerificationCodeUrl = `${Config.API_END_POINT}api/auth/verifyEmailVerificationCode`;
export const checkVerificationCodeUrl = `${Config.API_END_POINT}api/auth/checkVerificationCode`;
export const sendAccountVerificationEmailUrl = `${Config.API_END_POINT}api/auth/sendAccountVerificationEmail`;

//New ApI
export const verifyEmailVerificationCodeUrlNew = `${Config.API_END_POINT}api/users/change-email/verify-email-code`;
export const sendAccountVerificationEmailUrlNew = `${Config.API_END_POINT}api/users/change-email/send-verification-code`;

export const completeProfileUrl = `${Config.API_END_POINT}api/auth/completeProfile`;
export const signupUrl = `${Config.API_END_POINT}api/auth/signup`;

export const createAccountUrl = `${Config.API_END_POINT}api/signup-V2/create-account`;
export const registrationprocessUrl = `${Config.API_END_POINT}api/signup-V2/registration-process`;

export const getEmailByUsernameUrl = `${Config.API_END_POINT}api/auth/getEmailByUsername`;

export const validatePhoneNumber = `${Config.API_END_POINT}api/auth/validatePhoneNumber`;

//send notifications
export const newChatNotification = `${Config.API_END_POINT}api/notification/newChatNotification`;
export const requesterConfirmedRequest = `${Config.API_END_POINT}api/notification/requesterConfirmedRequest`;
export const gameMarkedCompletedByRequester = `${Config.API_END_POINT}api/notification/gameMarkedCompletedByRequester`;
export const gameMarkedCompletedByHost = `${Config.API_END_POINT}api/notification/gameMarkedCompletedByHost`;
export const requestDeclinedByHost = `${Config.API_END_POINT}api/notification/requestDeclinedByHost`;
export const requestAcceptedByHost = `${Config.API_END_POINT}api/notification/requestAcceptedByHost`;
export const requestDeclinedByRequester = `${Config.API_END_POINT}api/notification/requestDeclinedByRequester`;

export const getRequestUsersUrl = `${Config.API_END_POINT}api/requests/getRequestUsers`;
export const clubSearchUrl = `${Config.API_END_POINT}api/requests/clubSearch`;
export const clubSearchUrlV2 = `${Config.API_END_POINT}api/requests/clubSearchV2`;
export const registerClubs = `${Config.API_END_POINT}api/register/clubs`;

export const verifyPrivateNetworkCodeUrl = `${Config.API_END_POINT}api/private_network/verifyPrivateNetworkCode`;

//Verify club name
export const verifyClubNameUrl = `${Config.API_END_POINT}api/clubs/club-name-validity`;

// Checking can user join to PN
export const userCanJoin = `${Config.API_END_POINT}api/private_network/canuserjoin`;

// Checking can user exit from PN
export const canUserExitURL = `${Config.API_END_POINT}api/private_network/canuserexit`;

// Checking can user join to TG Network
export const addUserToTGUrl = `${Config.API_END_POINT}api/users/add-user-to-tg`;

export const referralAddUrlV1 = `${Config.API_END_POINT}api/referral/v1/add`;

export const acceptChatInviteUrl = `${Config.API_END_POINT}api/chat/acceptChatInvite`;
export const createRequestChatChannelUrl = `${Config.API_END_POINT}api/chat/createRequestChatChannel`;

export const allNotifications = `${Config.API_END_POINT}api/notification/allNotifications`;
export const allNotificationsV2 = `${Config.API_END_POINT}api/notification/allNotificationsV2`;
export const allNotificationsV3 = `${Config.API_END_POINT}api/notification/v3/all-notification`;

// Requests API
export const searchClubUrl = `${Config.API_END_POINT}api/requests/searchClub`;

// My contancts search club  API
export const searchClubContactsUrl = `${Config.API_END_POINT}api/my-contacts/search-club`;

// Search Fav club API
export const searchFavClubUrl = `${Config.API_END_POINT}api/favoriteClubs/search-clubs`;
// Search Fav club API1
export const searchFavouriteClubUrl = `${Config.API_END_POINT}api/favoriteClubs/V2/search-clubs`;

// Search API new test
export const testSearchClub = `${Config.API_END_POINT}api/users/search-club-for-requesting-addition`;

export const filterClubMembers = `${Config.API_END_POINT}api/clubs/filterClubMembers`;
export const gameUserInformation = `${Config.API_END_POINT}api/requestsV2/gameUserInformation`;

//Received URL
export const receivedOpenURL = `${Config.API_END_POINT}api/requestsV3/received/open`;
export const receivedAcceptedURL = `${Config.API_END_POINT}api/requestsV3/received/accepted`;

//Requested URL
export const requestedOpenURL = `${Config.API_END_POINT}api/requestsV2/requested/open`;

//Requested URL v4
export const requestedOpenURLv4 = `${Config.API_END_POINT}api/requestsV4/requested/open`;
export const requestedAcceptedURLv4= `${Config.API_END_POINT}api/requestsV4/requested/accepted`;
export const requestedHistoryURLv4= `${Config.API_END_POINT}api/requestsV4/requested/history`;

//Received History New Version Api
export const historyReceivedUrlV4 = `${Config.API_END_POINT}api/requestsV4/received/history`;

// Token Check URL
export const tokenCheckURL = `${Config.API_END_POINT}api/requestsV2/createRequestTokenCheck`;

// Create Offer token check URL
export const canCreateOfferURL = `${Config.API_END_POINT}api/offers/canCreateOffer`;

// version Check URL
export const versionCheckURL = `${Config.API_END_POINT}api/auth/versionCheck`;

//Feed api
export const feedsURL = `${Config.API_END_POINT}api/feed/V3/get-feed`;

export const systemMessageURL = `${Config.API_END_POINT}api/feed/getSystemMessages`;
export const gameReviewsURL = `${Config.API_END_POINT}api/feed/getGameReviews`;

export const postLikeUnlike = `${Config.API_END_POINT}api/feed/likeUnlikePost`;
export const postReportUnreport = `${Config.API_END_POINT}api/feed/reportUnreportPost`;
export const postUpdate = `${Config.API_END_POINT}api/feed/updatePost`;
export const postDelete = `${Config.API_END_POINT}api/feed/deletePost`;
export const postDetails = `${Config.API_END_POINT}api/feed/getPostDetails`;
export const postDetailsURLV2 = `${Config.API_END_POINT}api/feed/V2/get-post-details`;
export const createNewPost = `${Config.API_END_POINT}api/feed/createPost`;
export const createComment = `${Config.API_END_POINT}api/feed/createComment`;
export const deleteComment = `${Config.API_END_POINT}api/feed/deleteComment`;

//Offers API
export const getOffersURL = `${Config.API_END_POINT}api/offers/getOffers`;

//Offers API Version 2
export const getOffersV2URL = `${Config.API_END_POINT}api/offers/getOffersV2`;

// Update user tier apis
export const updateUserTierV2 = `${Config.API_END_POINT}api/adjustments/updateUserTierV2`;

// mute club apis
export const muteClubs = `${Config.API_END_POINT}api/clubs/muteClubs`;

// check TG group chat mute/unmute user
export const checkMutedTGChat = `${Config.API_END_POINT}api/chat/checkMutedTGChat`;

//Get Country name via Dial Code
export const countryCodeURL = `${Config.API_END_POINT}api/settings/getCountryCode`;

// graphe ql Entry point
export const graphqlUrl = `${Config.HASURA_END_POUNT}v1/graphql`;
export const fetchUserUrl = `${Config.API_END_POINT}api/auth/fetchUser?role=user`
export const graphqlWebUrl = `${Config.HASURA_WEB_SOCKET}v1/graphql`;

// fetch request details using request id
export const requestDetailsURL = `${Config.API_END_POINT}api/requestsV2/requestDetailsTabWise`;

// Favouite Clubs list api
export const favListURL = `${Config.API_END_POINT}api/favoriteClubs/getUserFavoriteClubs`;

// Favouite Clubs list api
export const favListURLV2 = `${Config.API_END_POINT}api/favoriteClubs/V2/get-fav-clubs`;

// Favouite Clubs list api
export const updateFavouriteClub = `${Config.API_END_POINT}api/clubs/update-favourite-club`;

// Get Pegboard list api
export const pegBoardListURL = `${Config.API_END_POINT}api/pegboards/get-pegboards`;

// Get Pegboard list Details api
export const pegBoardListDetailsURL = `${Config.API_END_POINT}api/pegboards/pegboard-details`;

// Add Club List
export const addClubURL = `${Config.API_END_POINT}api/users/add-club-request`;

// MyContact List
export const myContactURL = `${Config.API_END_POINT}api/my-contacts/get-my-contacts`;

// My contact club search
export const myContactClubSearch = `${Config.API_END_POINT}api/my-contacts/search-club`;

//Delete my contact
export const deleteContactURL = `${Config.API_END_POINT}api/my-contacts/delete-my-contact`;

// Add contact
export const addMyContactURL = `${Config.API_END_POINT}api/my-contacts/add-contact`;

// Edit contact
export const editMyContactURL = `${Config.API_END_POINT}api/my-contacts/edit-contact`;

// Club Member List
export const ClubMemberDetailsURL = `${Config.API_END_POINT}api/users/get-my-club-members`;

export const RestrictClubsURL = `${Config.API_END_POINT}api/favoriteClubs/restrict-clubs`;

// Request Token Adjustment
export const tokenAdjustmentRequestURL = `${Config.API_END_POINT}api/users/club-token-adjustment-request`;

//Game Review Delete
export const gameReviewDelete = `${Config.API_END_POINT}api/feed/delete-game-review`;

//Check user can delete own account
export const canDeleteAccountURL = `${Config.API_END_POINT}api/users/can-delete-account`;

// Delete user account URL
export const deleteAccountURL = `${Config.API_END_POINT}api/users/delete-user-account`;

//reset password
export const resetPassword = `${Config.API_END_POINT}api/auth/password-reset-link`;

//NGV count
export const ngvCount = `${Config.API_END_POINT}api/membership/get-ngv-logs`;

//check dues or plan of dues
export const CHECK_DUES = `${Config.API_END_POINT}api/membership/check-due`;

//Initial Payment
export const INITIATE_PAYMENT = `${Config.API_END_POINT}api/membership/initiate-payment`;

//complete payment intent
export const COMPLETE_INTENT = `${Config.API_END_POINT}api/membership/complete-intent`;

//Registration step 8
export const SIGNUP_PROCESS = `${Config.API_END_POINT}api/signup-V2/registration-process`;

//Club Lower tier vissibility
export const CLUB_LOWER_TIER_VISSIBILITY = `${Config.API_END_POINT}api/clubs/update-club-ltv`;

//Phone number details
export const PHONE_DETAILS = `${Config.API_END_POINT}api/helpers/get-phone-details`;
//check Maintenance
export const CHECK_MAINTENANCE = `${Config.API_END_POINT}api/auth/app-maintenance-check`;

//Get Events
export const GET_EVENTS = `${Config.API_END_POINT}api/event/get-event`;

//search friends
export const SEARCH_FRIEND = `${Config.API_END_POINT}api/friends/search-user`;

//Send friend request
export const SEND_FRIEND_REQUEST = `${Config.API_END_POINT}api/friends/requests/send`;

//Accept Friend Request
export const ACCEPT_FRIEND_REQUEST = `${Config.API_END_POINT}api/friends/requests/accept`;

//Declined Friend Request
export const DECLINED_FRIEND_REQUEST = `${Config.API_END_POINT}api/friends/requests/decline`;

//Recieved Requests List
export const RECIEVED_REQUEST_LIST = `${Config.API_END_POINT}api/friends/requests/received`;

//Recieved Create Request List
export const CREATED_REQUEST_LIST = `${Config.API_END_POINT}api/friends/requests/created`;

//Get Friends data for csv
export const FRINEDS_DOWNLOAD_CSV = `${Config.API_END_POINT}api/friends/download-csv`;

//Declined List
export const DECLINED_REQUESTS_LIST = `${Config.API_END_POINT}api/friends/requests/declined`;

//Remove Declined Request
export const REMOVE_DECLINED_REQUEST = `${Config.API_END_POINT}api/friends/requests/remove-declined`;

//Withdraw Friend Request
export const WITHDRAW_FRIEND_REQUEST = `${Config.API_END_POINT}api/friends/requests/withdraw`;

//All Friends List
export const ALL_FRIENDS_LIST = `${Config.API_END_POINT}api/friends/my-friends`;

//All Friends List
export const ALL_FRIENDS_LIST_V2 = `${Config.API_END_POINT}api/friends/v2/my-friends`;

//UnFriend Friends
export const UNFRIEND_FRIENDS = `${Config.API_END_POINT}api/friends/requests/unfriend`;

//Edit Notes
export const EDIT_NOTES = `${Config.API_END_POINT}api/friends/edit-notes`;

//Search Friends By Search
export const SEARCH_FRIENDS = `${Config.API_END_POINT}api/friends/v2/my-friends`;

//Decline Friend Request
export const DECLINE_FRIEND_REQUEST = `${Config.API_END_POINT}api/friends/requests/decline`;

//Mutual Friends
export const MUTUAL_FRIENDS = `${Config.API_END_POINT}api/friends/mutual-friends`;

//Stream chat token
export const GET_STREAM_CHAT_TOKEN = `${Config.API_END_POINT}api/chat-v2/get-token`;

//chat friend list
export const STREAM_FRIEND_LIST = `${Config.API_END_POINT}api/chat-v2/my-friends`;

//create one to one chat
export const CREATE_ONE_TO_ONE_CHANNEL = `${Config.API_END_POINT}api/chat-v2/one-to-one-channel`;

//Forward Message
export const FORWARD_MESSAGE = `${Config.API_END_POINT}api/chat-v2/forward-message`;

//Create New Chat Group
export const CREATE_NEW_CHAT_GROUP = `${Config.API_END_POINT}api/chat-v2/group/create`;

//Edit Group
export const EDIT_GROUP = `${Config.API_END_POINT}api/chat-v2/group/edit`;
//Get Events new Api
export const GET_EVENTS_V2 = `${Config.API_END_POINT}api/event/v2/get-events`;

//Make Another User To Group Admin
export const MAKE_ANOTHER_USER_GROUP_ADMIN = `${Config.API_END_POINT}api/chat-v2/group/add-admin`;

//Make Another User To Group Admin
export const REVOKE_ADMIN_ACCESS = `${Config.API_END_POINT}api/chat-v2/group/remove-admin`;

//Get Channel Members
export const GET_CHANNEL_MEMBERS = `${Config.API_END_POINT}api/chat-v2/group/members`;

//Add Members To Group
export const ADD_MEMBERS_TO_GROUP = `${Config.API_END_POINT}api/chat-v2/group/add-member`;

//Block User
export const BLOCK_USER = `${Config.API_END_POINT}api/chat-v2/block-user`;

//Unblock User
export const UNBLOCK_USER = `${Config.API_END_POINT}api/chat-v2/unblock`;

//Get Block List
export const BLOCK_LIST = `${Config.API_END_POINT}api/chat-v2/block-list`;

//Remove Participants
export const REMOVE_PARTICIPANTS = `${Config.API_END_POINT}api/chat-v2/group/remove-member`;

//User Profile
export const USER_PROFILE = `${Config.API_END_POINT}api/users/profile`;

//check for send friend request
export const CAN_SEN_FRIEND_REQUEST = `${Config.API_END_POINT}api/friends/can-send-request`;

//club details for map
export const CLUB_DETAILS_MAP = `${Config.API_END_POINT}api/newclubs/v3/fetch-club-detail`

//played friend list
export const playedFriendList = `${Config.API_END_POINT}api/newclubs/v3/fetch-club-friends-played-detail`;

//BroadcastMessage
export const BROADCAST_MESSAGE = `${Config.API_END_POINT}api/chat-v2/broadcast-message`

//Create My TG Group
export const CREATE_MY_TG_GROUP = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/create`

//Update My TG Group
export const UPDATE_MY_TG_GROUP = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/edit`

//Get My TG Groups
export const GET_MY_TG_GROUPS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/groups`

//Join My TG Groups
export const JOIN_MY_TG_GROUPS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/join`

//Delete My TG Groups
export const DELETE_MY_TG_GROUPS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/delete-group`

//Get MY Tg Group Details
export const GET_MY_TG_GROUP_DETAIL = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/details`

//Get MY Tg Group Members
export const GET_MY_TG_GROUP_MEMBERS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/members`

//Get Id Of All Friends
export const GET_ALL_FRIENDS_ID = `${Config.API_END_POINT}api/friends/my-friends-id`

//Send Private Request
export const SEND_PRIVATE_REQUEST = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/send-request`

//Withdraw Group Join Request
export const WITHDRAW_GROUP_JOIN_REQUEST = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/withdraw-request`

//Get Pending Members List
export const GET_PENDING_MEMBER_REQUEST_LIST = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/requests`

//Accept pending Member Request
export const ACCEPT_PENDING_MEMBER_REQUEST = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/accept-request`

//Decline Pending Request
export const DECLINE_PENDING_REQUEST = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/decline-request`

//Update club visibility for non-friend
export const UPDATE_CLUB_VISIBILITY_FOR_NON_FRIEND = `${Config.API_END_POINT}api/users/club/update-visibility-for-non-friends`

//Member Capacity Status - My TG Channel
export const GET_MEMBER_CAPACITY_STATUS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/member-capacity-status`

//Tag Suggestion
export const TAG_SUGGESTION = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/tags-suggestions`

//Fetch Tags
export const FETCH_TAGS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/fetch-tags`

//Offers API Version 3
export const getOffersV3URL = `${Config.API_END_POINT}api/offers/getOffersV3`;

//Fetch Request Count
export const GET_REQUEST_COUNT = `${Config.API_END_POINT}api/requestsV2/fetch-tabs-count`

//Fetch Unread Message Status In Request Flow
export const GET_UNREAD_MESSAGE_STATUS = `${Config.API_END_POINT}api/requestsV2/unread-message-status`

//Update Benefit Count
export const UPDATE_BENEFIT_COUNT = `${Config.API_END_POINT}api/benefits/updateBenefitCount`

//Update Visibility Settings
export const UPDATE_VISIBILITY_SETTING = `${Config.API_END_POINT}api/auth/v2/visibility-settings`

//Get Golfer Profile version 2
export const GET_GOLFER_PROFILE_DATA = `${Config.API_END_POINT}api/users/v2/profile`

//Get Golfer Profile version 2
export const GET_GOLFER_PROFILE_DATA_V3 = `${Config.API_END_POINT}api/users/v3/profile`

//Get club visibility setting
export const GET_CLUB_VISIBILITY_SETTING = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/get-club-setting`

//Get Group visible by club
export const GET_GROUPS_BY_CLUB = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/club-visibility/groups-by-club`

//Update master club visibility setting
export const UPDATE_MASTER_CLUB_VISIBILITY_SETTING = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/master-club-setting`

//Get clubs other member
export const ClubMemberDetailsURLsV2 = `${Config.API_END_POINT}api/users/v2/get-my-club-members`;

//Update visible to others
export const UpdateVisibleToOthers = `${Config.API_END_POINT}api/clubs/update-visible-to-others`

//Update club visible in My TG groups
export const UPDATE_GROUP_CLUB_SETTING = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/group-club-setting`

//Create Request new api
export const CREATE_REQUEST = `${Config.API_END_POINT}api/requests/actions/create`

//Edit Request new api
export const EDIT_REQUEST = `${Config.API_END_POINT}api/requests/actions/edit`

//Delete Requested request new api
export const DELETE_REQUESTED_REQUEST = `${Config.API_END_POINT}api/requests/actions/cancel`

//Delete Requested request new api
export const DECLINE_REQUEST_AS_HOST = `${Config.API_END_POINT}api/requests/actions/decline`

//Accept request new api
export const ACCEPT_REQUEST_AS_HOST = `${Config.API_END_POINT}api/requests/actions/accept`

//Edit game request new api
export const EDIT_GAME_AS_HOST = `${Config.API_END_POINT}api/requests/actions/edit-game`

//Mark complete game request new api
export const MARK_COMPLETE_GAME_REQUEST = `${Config.API_END_POINT}api/requests/actions/mark-complete`

//Mark complete game request new api
export const MARK_COMPLETE_GAME_REQUEST_V2 = `${Config.API_END_POINT}api/requests/actions/v2/mark-complete`
//Mark complete game request new api
export const DELETE_GAME_REQUEST = `${Config.API_END_POINT}api/requests/actions/delete`

//Search clubs
export const SEARCH_CLUBS = `${Config.API_END_POINT}api/requests/search-club`

//New api of Update Visibility Settings
export const UPDATE_VISIBILITY_SETTING_NEW = `${Config.API_END_POINT}api/auth/visibility-settings`

//Search clubs
export const REQUEST_AGAINST_OFFER = `${Config.API_END_POINT}api/requests/actions/request-against-offer`
//Create request new v2 api
export const NEW_CREATE_REQUEST = `${Config.API_END_POINT}api/requests/actions/v2/create`

//Favorite restricted
export const FAVOURITE_RESTRICTED = `${Config.API_END_POINT}api/clubs/favourite-restricted`

//Create offer
export const CREATE_OFFER = `${Config.API_END_POINT}api/offers/create`
 
//Create offer V2
export const CREATE_OFFER_V2 = `${Config.API_END_POINT}api/offers/v2/create`

//Offers API Version 4
export const getOffersV4URL = `${Config.API_END_POINT}api/offers/getOffersV4`;

//Offers API Version 5
export const getOffersV5URL = `${Config.API_END_POINT}api/offers/getOffersV5`;

//Edit offer
export const EDIT_OFFER = `${Config.API_END_POINT}api/offers/edit`

//Edit offer
export const EDIT_OFFER_V2 = `${Config.API_END_POINT}api/offers/v2/edit`

//Delete offer
export const DELETE_OFFER = `${Config.API_END_POINT}api/offers/delete`

//Get map Tg GroupMembers List
export const MAP_TG_GROUP_MEMBER_LIST = `${Config.API_END_POINT}api/map/tgGroupMembersList`

//Get friends list in map screen
export const GET_FRIEND_LIST_MAP = `${Config.API_END_POINT}api/friends/map-my-friends`

//Send image message
export const SEND_IMAGE_MESSAGE = `${Config.API_END_POINT}api/chat-v2/send-message`

//Get map friend id
export const GET_MAP_FRIEND_ID = `${Config.API_END_POINT}api/friends/map-friends-id-send-message`

//Get Offer created for MY TG Group
export const GET_MY_TG_GROUP_OFFERS = `${Config.API_END_POINT}api/offers/my-tg-group/get-offers`

//Get Offer Details
export const GET_OFFER_DETAILS = `${Config.API_END_POINT}api/offers/v2/detail`

//Get last notes
export const GET_LAST_NOTES = `${Config.API_END_POINT}api/requests/get-last-note`

//Get all my tg group members id
export const GET_MY_TG_MEMBER_ID = `${Config.API_END_POINT}api/map/group-members-id-send-message`

//Broadcast message TG member group
export const TG_GROUP_MEMBER_BROADCAST_MESSAGE = `${Config.API_END_POINT}api/chat-v2/broadcast-message/tg-group`

//Get offer count
export const GET_OFFER_COUNT = `${Config.API_END_POINT}api/offers/my-tg-group/get-offers-count`

//Create event
export const CREATE_EVENT = `${Config.API_END_POINT}api/event/v4/create`

//Get Events new Api V3
export const GET_EVENTS_V3 = `${Config.API_END_POINT}api/event/v3/get-events`;

//Get Events new Api V3
export const GET_EVENTS_V4 = `${Config.API_END_POINT}api/event/v4/get-events`;

//Edit Event
export const EDIT_EVENT = `${Config.API_END_POINT}api/event/v4/edit-event`

//Delete Event
export const DELETE_EVENT = `${Config.API_END_POINT}api/event/delete-event`

//Club Preferences
export const CLUB_PREFERENCES = `${Config.API_END_POINT}api/clubs/update-gender-prefrence`

//Event count
export const GET_EVENT_COUNT = `${Config.API_END_POINT}api/event/my-tg-group/get-event-count`

//Get pegboard data
export const GET_PEGBOARD = `${Config.API_END_POINT}api/pegboards/user/v2/get`

//Get pegboard data V2
export const GET_PEGBOARD_V2 = `${Config.API_END_POINT}api/pegboards/user/v2/get`

//Get admin pegboard leader board data
export const GET_PEGBOARD_ADMIN_LEADERBOARD = `${Config.API_END_POINT}api/pegboards/user/admin-leaderboard`

//Get pegboard data
export const GET_PEGBOARD_LEADERBOARD = `${Config.API_END_POINT}api/pegboards/user/leaderboard`

//Create New Pegboard
export const CREATE_NEW_PEGBOARD = `${Config.API_END_POINT}api/pegboards/user/create`

//Get user's my tg group
export const USER_MY_TG_GROUP = `${Config.API_END_POINT}api/pegboards/user/my-tg-groups/get`

//Get Friends played in pegboard club
export const GET_FRIENDS_PLAYED_IN_PEGBOARD_CLUB = `${Config.API_END_POINT}api/pegboards/user/friends-played-in-club`

//Get user's my tg group
export const GET_USER_PLAYED_CLUB = `${Config.API_END_POINT}api/pegboards/user/played-clubs-of-user`

//Add club in pegboard
export const ADD_CLUB_IN_PEGBOARD = `${Config.API_END_POINT}api/pegboards/user/add-club`

//Delete pegboard
export const DELETE_PEGBOARD_CLUB = `${Config.API_END_POINT}api/pegboards/user/delete-club`

//Add manual club in pegboard
export const ADD_MANUAL_CLUB = `${Config.API_END_POINT}api/clubs/add-pegboard-club`

//Get pegboard details
export const GET_PEGBOARD_DETAILS = `${Config.API_END_POINT}api/pegboards/user/v2/pegboard-details`

//Get pegboard details
export const GET_PEGBOARD_DETAILS_V3 = `${Config.API_END_POINT}api/pegboards/user/v3/pegboard-details`

//Edit pegboard
export const EDIT_PEGBOARD = `${Config.API_END_POINT}api/pegboards/user/edit`

//Delete pegboard
export const DELETE_PEGBOARD = `${Config.API_END_POINT}api/pegboards/user/delete`

//Update club rank
export const UPDATE_CLUB_RANK = `${Config.API_END_POINT}api/pegboards/user/update-club-rank`

//Check-request-popup
export const CHECK_REQUEST_POPUP = `${Config.API_END_POINT}api/requestsV2/check-request-popup`

//Check-request-popup
export const UPDATE_POPUP_STATUS = `${Config.API_END_POINT}api/requestsV2/update-popup-status`

//Search pegboard club
export const SEARCH_CLUB_FOR_PEGBOARD = `${Config.API_END_POINT}api/clubs/search-clubs-for-pegboard`

//Mark club as played/unplayed
export const UPDATE_PLAYED_CLUB = `${Config.API_END_POINT}api/users/update-played-club`

//Get recieved request details
export const GET_RECIEVED_REQUEST_INFO = `${Config.API_END_POINT}api/requests/get-request-info`

//Get requested request details
export const GET_REQUESTED_REQUEST_INFO = `${Config.API_END_POINT}api/requests/requested/get-request-info`

//Get most members TG groups
export const GET_MOST_MEMBERS_TG_GROUPS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/suggestions/most-members`

//Get Group with most Friends
export const GET_MOST_FRIENDS_TG_GROUPS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/suggestions/most-friends`

//Get new created TG Groups
export const GET_NEW_TG_GROUPS_CREATED = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/suggestions/new-groups`

//Get groups with most mutual members
export const GET_GROUPS_WITH_MUTUAL_MEMEBERS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/suggestions/most-mutual-members`

//Get groups with most new members
export const GET_GROUPS_WITH_NEW_MEMEBERS = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/suggestions/most-new-members`

//Update club visibility for MY TG Group
export const UPDATE_CLUB_VISIBILITY_FOR_MY_TG_GROUP = `${Config.API_END_POINT}api/chat-v2/my-tg-groups/club-visibility/update-visibility`

//Get requested host data
export const GET_REQUESTED_HOSTS = `${Config.API_END_POINT}api/requestsV4/requested/hosts`

//Get requested host data
export const UPDATE_GAME_PLAYERS = `${Config.API_END_POINT}api/requests/actions/update-players`

//Update Ambassador Visibility
export const UPDATE_AMBASSADOR_VISIBILITY  = `${Config.API_END_POINT}api/settings/update-ambassador-visibility`

//Update user address
export const UPDATE_USER_ADDRESS  = `${Config.API_END_POINT}api/users/user-save-address`

//Check user's club year validation
export const CHECK_YEARLY_CLUB_VALIDATION  = `${Config.API_END_POINT}api/auth/check-yearly-validation`

//Get membership categories
export const GET_MEMBERSHIP_CATEGORIES  = `${Config.API_END_POINT}api/membershipCategory/get-membership-categories`

//Update club information
export const UPDATE_CLUB_INFO  = `${Config.API_END_POINT}api/users/club/update-club`

//Get Recommended Clubs
export const GET_RECOMMENDED_CLUBS  = `${Config.API_END_POINT}api/requests/recommended-clubs`

//Get Recommended Clubs
export const ADD_REFERRAL_V1  = `${Config.API_END_POINT}api/referral/v1/add`

//Get Recommended Clubs
export const GET_MY_REFERRALS  = `${Config.API_END_POINT}api/referral/get`

//Create request chat channel
export const CREATE_REQUEST_CHAT_CHANNEL  = `${Config.API_END_POINT}api/requestChat/create-request-chat-channel`

// Update club
export const UPDATE_CLUB = `${Config.API_END_POINT}api/clubs/send-update-notiification`

// Update club visible to other member setting
export const UPDATE_CLUB_VISIBLE_TO_OTHER = `${Config.API_END_POINT}api/clubs/club-visible-to-other`

// Get ambassador custom message
export const GET_AMBASSADOR_CUSTOM_MESSAGE = `${Config.API_END_POINT}api/users/get-ambassador-message`

// Get ambassador custom message
export const UPDATE_AMBASSADOR_CUSTOM_MESSAGE = `${Config.API_END_POINT}api/users/update-ambassador-message`

// Mark read notification
export const READ_NOTIFICATION=`${Config.API_END_POINT}api/notification/mark-all-read`

// Stats
export const STATS = `${Config.API_END_POINT}api/dashboard/stats`

// Dismiss mute club Popup
export const DISMISS_MUTE_CLUB_POPUP = `${Config.API_END_POINT}api/notification/disable-muted-club-notifications`

// Get teal dot status
export const GET_TEAL_DOT_STATUS = `${Config.API_END_POINT}api/users/teal-dot-status`

// Update teal dot status
export const UPDATE_TEAL_DOT_STATUS = `${Config.API_END_POINT}api/users/update-teal-dot-status`

//Get all request host
export const GET_ALL_REQUEST_HOST = `${Config.API_END_POINT}api/requestsV4/requested/get-all-hosts`

//Get all request history
export const GET_ALL_REQUEST_HISTORY = `${Config.API_END_POINT}api/requestsV4/all-history`

//Search user
export const SEARCH_USER = `${Config.API_END_POINT}api/users/search-user`

//Log offline game
export const LOG_OFFLINE_GAME = `${Config.API_END_POINT}api/game/log-offline-game`

//Get map club offers
export const GET_MAP_CLUB_OFFERS = `${Config.API_END_POINT}api/offers/offers-in-club`

//Get game review
export const GET_GAME_REVIEW = `${Config.API_END_POINT}api/game/game-review-by-club`

//Get clubs with offers
export const GET_CLUBS_WITH_OFFERS = `${Config.API_END_POINT}api/newclubs/v3/fetch-clubs-with-offers`

//Toggle pegboard visibility
export const TOGGLE_PEGBOARD_VISIBILITY = `${Config.API_END_POINT}api/pegboards/toggle-visibility`

//Get game review by user
export const GET_GAME_REVIEW_BY_USER = `${Config.API_END_POINT}api/game/game-review-by-user`


