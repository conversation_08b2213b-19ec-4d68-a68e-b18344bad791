const colors = {
    darkteal: '#098089',
    lightgray: '#F2F2F2',
    darkgray: '#808080',
    black: '#000000',
    borderColor: '#D4D4D4',
    lightGrey: '#F2F2F2',
    greyVariant1: 'rgba(235, 235, 235, 1)',
    shadowColor: 'rgba(196, 196, 196, 0.3)',
    shadowDarkColor:'rgba(196, 196, 196, 1)',
    fadeBlack: '#666666',
    orange: '#E05E52',
    borderGray: '#E0E0E0',
    divider: '#E2E2E2',
    lightBlack: '#333333',
    backGray: '#E5E5E5',
    whiteF6: '#F6F6F6',
    textProfile: '#098089',
    bgProfile: '#09808933',
    gray99:'#999999',
    groupInfoGrayIcon:'#DADADA',
    errorColor: '#ff0000',
    lightShadeGray: '#cccccc',
    shadowColors: '#000',
    white: '#ffffff',
    grey: '#EBEBEB',
    charcoal:'#4A4A4A',
    silver:'#C4C4C4',
    light_grey:'rgba(191, 191, 191, 0.2)',
    dark_charcoal:'rgba(51, 51, 51, 1)',
    greyRgba: 'rgba(242, 242, 242, 1)',
    tealRgb: 'rgba(9, 128, 137, 1)',
    tealRgba02: 'rgba(9, 128, 137, 0.2)',
    transparentRgba: 'rgba(0, 0, 0, 0.3)',
    lightestTeal: 'rgba(9, 128, 137, 0.10)',
    darkBlack: '#0F1828',
    bottomSheetOptionBackgroundColor:'rgba(248, 248, 248, 1)',
    bottomSheetOptionsTextColor: 'rgba(0, 122, 255, 1)',
    dividerColor: 'rgba(196, 196, 196, 1)',
    rgbaBlack: 'rgba(0, 0, 0, 1)',
    modalBackGroundColor: 'rgba(0,0,0,0.5)',
    whiteRGB: 'rgba(255, 255, 255, 1)',
    tealBoxColor: 'rgba(9, 128, 137, 0.04)',
    boxOrangeColor: 'rgba(224, 94, 82, 0.2)',
    whiteColor: '#FFFFFF',
    messageTeal: '#096269',
    Alabaster:'rgba(248, 248, 248, 1)',
    Dark_Azure:'rgba(0, 122, 255, 1)',
    btnText: 'rgba(255, 255, 255, 1)',
    linkColor: '#027eb5',
    linkSenderMessageColor: '#81edff',
    confirmColor: '#00828C',
    shadowColor1: 'rgba(0,0,0,0.35)',
    darkGreyRgba: 'rgba(128, 128, 128, 1)',
    greyRgb: 'rgba(102, 102, 102, 1)',
    headerBorderColor: 'rgba(0, 0, 0, 0.05)',
    darkCharcoal: '#333',
    badgeLabelBackgroundColor: 'rgba(9, 128, 137, 0.1)',
    //@ts-ignore
    dividerColor: 'rgba(246, 244, 244, 1)',
    lineDividerColor: 'rgba(226, 226, 226, 1)',
    dividerSectionColor: 'rgba(249, 249, 249, 1)',
    dustyGrey: 'rgba(153, 153, 153, 1)',
    inputFieldBorderColor: 'rgba(224, 224, 224, 1)',
    opacityTeal: '#0980891A',
    tealRGBAColor: 'rgba(9, 128, 137, 0.12)',
    giftBoxCircleColor: 'rgba(246, 246, 246, 1)',
    requestedBtnColor: '#0980891F',
    defaultScreenBG: '#B0B0B033',
    dashedGrey: '#3333331A',
    lightTeal: '#CBDEDF4D',
    lightTeal2: '#E5F2F3',
    offWhite: '#F6F4F4',
    whiteSmoke: '#F9F9F9',
    darkGreySystemMessage: '#E8E8E8',
    systemMessageText: '#666666',
    warningColor: 'rgba(241, 203, 0, 0.1)',
    greyCheckBox: 'rgba(217, 217, 217, 1)',
    tealBrightColor: 'rgba(103, 255, 236, 1)',
    greyShade: 'rgba(74, 74, 74, 1)',
    lightSky: 'rgba(230, 242, 243, 1)',
    homeIconColor: 'rgba(74, 163, 152, 0.15)',
    clubIconColor: 'rgba(82, 154, 86, 0.12)',
    requestIconColor: 'rgba(53, 184, 244, 0.15)',
    chatIconColor: 'rgba(223, 40, 239, 0.15)',
    activeTealColor: 'rgba(74, 163, 152, 0.20)',
    disableGreyColor: 'rgba(128, 128, 128, 0.4)',
    greenColor: '#06924B',
    screenBG: '#F0F2F5',
    lightGreen: '#06924B1A',
    lightOrange: '#E05E521A',
    darkGrey: '#80808080',
    brightGreen: '#2E9360',
    lightTealNew: '#D8EEF0',
};

export { colors };
