import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Size } from '../utils/responsiveUI';

interface Props {
    focused?: boolean;
    Icon: any;
    backgroundColor: string;
}

const TabBarIcon = ({ Icon, backgroundColor }: Props) => {
    {
        return (
            <View style={[styles.container, { backgroundColor: backgroundColor }]}>
                <Icon />
            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        width: Size.SIZE_45,
        height: Size.SIZE_34,
        borderRadius: Size.SIZE_42,
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default TabBarIcon;
