import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { colors } from '../theme/theme';
import { Size, Spacing, Typography } from '../utils/responsiveUI';

interface Props {
    focused: boolean;
    label: string;
}

const TabBarLabel = ({ focused, label }: Props) => {
    return (
        <View style={{ alignItems: 'center' }}>
            <Text style={[styles.label, { color: focused ? colors.tealRgb : colors.lightBlack }]}>{label}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    label: {
        fontSize: Typography.FONT_SIZE_11,
        fontWeight: '500',
        lineHeight: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        marginTop: Spacing.SCALE_15,
    },
    tealDot: {
        height: Size.SIZE_4,
        width: Size.SIZE_4,
        backgroundColor: colors.tealRgb,
        borderRadius: Size.SIZE_50,
        marginTop: Spacing.SCALE_4,
    },
});

export default TabBarLabel;
