export interface PhoneNumberDetails {
    name: string;
    dialCode: string;
    countryCode: string;
    phoneNumber: string;
    unmaskedPhoneNumber: string;
}

export interface MembershipPlan {
    name: string;
    key: string;
}

export interface Address {
    city: string;
    line1: string;
    state: string;
    country: string;
    postal_code: string;
}

export interface StripeCustomerInfo {
    name: string;
    email: string;
    phone: string;
    address: Address;
    stateCode: string;
    countryCode: string;
}

export interface Association {
    id: number;
    name: string;
}

export interface NotificationSettings {
    push: boolean;
    text: boolean;
    email: boolean;
}

export interface AdditionalSettings {
    showAcceptRequestPopup: boolean;
    showCreateRequestPopup: boolean;
}

export interface VerifiedOn {
    email: boolean;
    phone: boolean;
}

export interface FavoriteClub {
    club_id: number;
}

export interface GuestTimeRestrictions {
    Friday: string;
    Monday: string;
    Tuesday: string;
    Thursday: string;
    Wednesday: string;
    Saturday?: string;
    Sunday?: string;
}

export interface ClubDetails {
    id: number;
    name: string;
    hasGuestRestrictions: boolean;
    has_membership_privileges: boolean;
    guest_time_restrictions: GuestTimeRestrictions;
    guestFee: string;
    dressCode: string;
    caddieRequired: boolean;
    caddieFee: string;
    notes: string | null;
    new: boolean;
    lowest_visible_tier: number;
    closurePeriods: ClosurePeriod[];
    club_demand_type: number;
    muted: boolean;
    proximity: string;
    club: Club;
}

export interface Club {
    id: number;
    name: string;
    caddieFee: number | null;
    caddieRequired: boolean;
    closurePeriods: any[]; // You can replace `any[]` with a more specific type if needed
    club_demand_type: number;
    dressCode: string;
    guestFee: string | number; // Adjust type based on actual usage
    guest_time_restrictions: Record<string, string>; // This maps days to time restrictions
    hasGuestRestrictions: boolean;
    has_membership_privileges: boolean;
    lowest_visible_tier: number;
    new: boolean;
    notes: string | null;
}

export interface ClosurePeriod {
    to: string;
    from: string;
}

export interface PaymentMethod {
    type: string;
    otherText?: string;
}

export interface UserClub {
    proximity: string;
    visible_to_non_friends: boolean;
    paymentMethod: PaymentMethod;
    otherInstructions: string | null;
    muted: boolean;
    restricted_membership: boolean;
    is_visibile_to_lower_tiers: boolean;
    visible_to_tiers: number | null;
    favorite_restricted: boolean;
    club_id: number;
    gender_preference: string;
    is_yearly_validated: boolean;
    is_replaced: boolean;
    visibleInNetwork: boolean;
    club: ClubDetails;
}

export interface Referral {
    name: string;
    detail: string;
    prompt: string;
}

export interface User {
    id: string;
    net_games_value: number;
    is_tg_founder: boolean;
    membership_plan_id: number;
    membership_plan: MembershipPlan;
    membership_active: boolean;
    username: string;
    show_visibility_popup: boolean;
    membership_expires_on: string | null;
    created_at: string;
    last_muted_prompt: string;
    deleted_at: string | null;
    annual_confirmation: string | null;
    visited_account_settings: boolean;
    is_tutorial_viewed: boolean;
    show_visibility_popup_time: string;
    birthYear: number;
    email: string;
    englishFluency: string;
    facebook: string;
    gender: string;
    linkedin: string;
    pace: string;
    handicap: string;
    about_yourself: string;
    full_name: string;
    first_name: string;
    last_name: string;
    playAsCouple: boolean;
    phone: string;
    phone_number_details: PhoneNumberDetails;
    tier: number;
    profilePhoto: string;
    tokens: string | null;
    last_active_session: string;
    profile_complete: boolean;
    registration_complete: boolean;
    account_activated: boolean;
    visibleInNetwork: boolean;
    visibleToPublic: boolean;
    tierVisibility: number[];
    visibleToLowerTiers: boolean;
    is_legacy_user: boolean;
    legacy_password_reset: boolean;
    notificationSettings: NotificationSettings;
    verificationMethod: string;
    muted: boolean;
    opt_in_email: boolean;
    verified: boolean;
    declined: string | null;
    mute_other_notifications: boolean;
    association_id: number;
    user_association_number: string;
    ftr_count: number;
    is_tg_ambassador: boolean;
    tg_ambassador_visibility: boolean;
    stripe_customer_info: StripeCustomerInfo;
    yearly_club_validation: number;
    association: Association;
    additional_settings: AdditionalSettings;
    profilePublic: boolean;
    socialPublic: boolean;
    mute_until: string | null;
    visible_to_favorite_clubs: boolean;
    verified_on: VerifiedOn;
    signup_current_step: number;
    favorite_clubs: FavoriteClub[];
    clubs: UserClub[];
    playedClubs: number[];
    referral: Referral;
}

export interface FeatureProps {
    name: string;
    Icon: React.FC<{ width?: number; height?: number }>;
    navigateTo: string;
    eventName: string;
    isRedDot: boolean;
    redDotName?: string;
}

export interface NotificationData {
    email: string;
    request_id: string;
    subject: string;
}

export interface NotificationItem {
    id: string;
    type: string;
    message: string;
    data: NotificationData;
    html_message: string;
    read: boolean;
    user_id: string;
}

export interface StatItem {
    id: string;
    Icon: any;
    value: number;
    label: string;
}

export interface SocialButtonProps {
    Icon: React.FC<React.SVGProps<SVGSVGElement>>;
    text: string;
    fullWidth?: boolean;
    link?: string;
}
export interface NotificationContextType {
    cardHeight: number;
    firstCardLines: number;
}

export interface UserClubRenderItemProps {
    item: ClubDetails;
}

export interface HomeScreenCommonHeaderProps {
    title: string;
    onPressGoBack?: () => void;
    showSearchIcon?: boolean;
    handleSearchIcon?: () => void;
    setActiveView?: (view: string) => void;
    activeView?: string;
    isFilterApplied?: boolean;
    showFilter?: boolean;
    openFilterModal?: () => void;
    handleCreateRequest?: () => void;
    showCreateRequestIcon?: boolean;
    showTripleDot?: boolean;
    handleToggleRecentPopup?: () => void;
    showCreateChatIcon?: boolean;
    handleCreateChat?: () => void;
    handleRequestHistory?: () => void;
    showRequestHistory?: boolean;
    historyUnreadMessageStatus?: boolean;
    hideToggleIcon?: boolean;
}

export interface RequestedAcceptedRequest {
    request_id: string;
    game_id: number;
    club_name: string;
    start_date: string; // format: "YYYY-MM-DD"
    end_date: string; // format: "YYYY-MM-DD"
    game_host_user_id: string;
    message: string;
    accompanied_only: boolean | null;
    number_of_players: number;
    hosts_declined: string[];
    created_at: string; // ISO timestamp
    updated_at: string; // ISO timestamp
    host_completed: boolean;
    requestor_completed: boolean;
    game_date: string; // ISO timestamp
    game_host_full_name: string;
    game_host_user_name: string;
    profilePhoto: string;
    deleted_by_users: any; // can be updated to appropriate type if known
    offer_details: {
        id: string;
        status: string;
        club_id: number;
        details: string;
        user_id: string;
        end_date: string; // ISO timestamp
        offer_id: number;
        created_at: string; // ISO timestamp
        start_date: string; // ISO timestamp
        updated_at: string; // ISO timestamp
        tierVisibility: number[];
        private_network_id: string | null;
    };
    offer_id: string;
    club_id: number;
    requestor_user_id: string;
    request_for_all: boolean;
    is_first_request: boolean;
    requestor_full_name: string;
    requestor_username: string;
    has_messages: boolean;
    requester_has_message: boolean;
    host_user_id: string;
    stream_channel_id: string;
    requestor_profile_photo: string;
}

export interface RequestDetails {
    gameInfo: {
        game_id: number;
        club_name: string;
        date: string;
        accompanied_only: boolean;
        message: string;
        number_of_players: number;
        number_of_hosts: number;
        handicap: string[]; // e.g., ["< 5", "5-10", "> 10"]
        english_fluency: string[]; // e.g., ["native", "fluent", ...]
        playAsCouple: boolean;
        ageGroup: boolean;
        maxAgeGroup: number;
        minAgeGroup: number;
    };
    userInfo: {
        name: string;
        username: string;
        email: string;
        phone: string;
        pace: string;
        handicap: string;
        english_fluency: string;
        social_public: boolean;
        facebook: string;
        linkedin: string;
        age: number;
        userId: string;
        about_yourself: string;
        tier: string;
        deleted_at: string | null;
        isTgFounder: boolean;
        profilePhoto: string | null;
        isTgAmbassador: boolean;
        tgAmbassadorVisibility: boolean;
        isSuperHost: boolean;
    };
}

export interface HostDataInterface {
    created_at: string;
    deleted_at: string | null;
    email: string;
    englishFluency: string;
    facebook: string;
    handicap: string;
    has_messages: boolean;
    id: string;
    linkedin: string;
    name: string;
    pace: string;
    phone: string;
    profilePhoto: string | null;
    sendbird_channel_id: string | null;
    socialPublic: boolean;
    stream_channel_id: string;
    tier: number;
    username: string;
    visibleToPublic: boolean;
}

export interface DeleteRequestParams {
    userId: string;
    requestId: string;
    deleteReason?: string;
}

export interface RequestHistoryItem {
    request_id: string;
    game_id: number;
    club_name: string;
    start_date: string; // ISO date string
    end_date: string; // ISO date string
    requestor_user_id: string;
    requestor_username: string;
    requestor_full_name: string;
    message: string;
    accompanied_only: boolean;
    number_of_players: number;
    hosts_sent: string[];
    hosts_declined: string[];
    created_at: string; // ISO date string
    updated_at: string; // ISO date string
    game_date: string; // ISO date string
    has_messages: boolean;
    sendbird_channel_id: string | null;
    stream_channel_id: string;
    status: 'pending' | 'accepted' | 'declined' | 'completed' | string; // extend as needed
    game_host_full_name: string | null;
    game_host_user_name: string | null;
    deleted_by_users: string[] | null;
    offer_details: any; // Replace `any` with actual type if known
    offer_id: string | null;
    request_for_all: boolean | null;
    is_first_request: boolean;
    type: 'sent' | 'received' | string; // extend as needed
    host_user_id: string;
    requestor_profile_photo: string | null;
}

export interface HostData {
    created_at: string;
    deleted_at: string | null;
    email: string;
    englishFluency: string;
    facebook: string;
    handicap: string;
    has_messages: boolean;
    id: string;
    linkedin: string;
    name: string;
    pace: string;
    phone: string;
    profilePhoto: string | null;
    sendbird_channel_id: string | null;
    socialPublic: boolean;
    stream_channel_id: string;
    tier: number;
    username: string;
    visibleToPublic: boolean;
}

export interface DateRangeModal {
    visible: boolean;
    startDate?: string;
    endDate?: string;
    date?: string;
}

export interface PeopleModal {
    visible: boolean;
    number_of_players: string;
}

export interface SearchedUser {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    profilePhoto: string | null;
    clubs: Club[];
}

export interface MapClub {
    type: 'Feature';
    geometry: {
        type: 'Point';
        coordinates: [number, number] | [[number, number], [number, number]]; // [longitude, latitude] or [[lon1, lat1], [lon2, lat2]]
    };
    id: number;
    properties: {
        id: number;
        ct: number;
        name: string;
        clubDemandType: number;
        favClub: boolean;
        played: boolean;
        addr: string;
        tier: number;
        isFriend: boolean;
        isContact: boolean;
        isMyTgGroupMember: boolean;
        clubMembers: number;
        acceptanceRate: number;
        isSelected?: boolean;
        myTgGroupIds: number[];
        hasPlayAsCoupleMember: boolean;
        color: string;
        hasFemalMember: boolean;
        club_type?: string;
        isFavorited?: boolean;
        offers?: Offer[];
        visibleBounds?: {
            northeast: {
                lat: number;
                lng: number;
            };
        };
    };
}

export interface MapClubDetail {
    clubs: {
        id: number;
        lat: number;
        lng: number;
        address: string;
        name: string;
        lowest_visible_tier: number;
        club_type: number;
        closurePeriods: any[]; // You can replace 'any' with a specific type if you know the structure
        guest_time_restrictions: {
            [day: string]: string; // e.g., "Monday": "All Day"
        };
        club_demand_type: number;
    };
    request: string;
    active_user_count?: number;
    requestCreated: number;
    contacts: number;
    clubMemberCount: number;
    pnCount: number;
    friends: number;
    friendsPlayed: number;
    userPlayedAtClub: boolean;
    isEligibleForCreateRequest: boolean;
    tgGroupMembersCount: number;
    totalMemberCount: number;
}

export interface Friend {
    friend_id: string;
    stream_channel_id: string;
    isMigrated: boolean;
    isSystemAdded: boolean;
    is_muted: boolean;
    id: number;
    notes: string | null;
    friend_info: {
        name: string;
        clubs: string[];
        email: string;
        phone: string;
        profilePhoto: string;
        id?: number;
        notes?: string | null;
    };
}

export interface TgGroupMember {
    id: string;
    tier: number;
    profilePhoto: string | null;
    username: string;
    fullName: string;
    visibleToPublic: boolean;
    groupNames: string[];
    isMuted: boolean;
    isGenderCompatible: boolean;
    isFriend: boolean;
    streamChannelId?: string;
}

export interface FilterState {
    fern: boolean;
    sage: boolean;
    moss: boolean;
    olive: boolean;
    friendsAndContact: boolean;
    myTGGroupMember: boolean;
    openOfferClubs: boolean;
    favoriteClubs: boolean;
    playedClubs: boolean;
    playAsCouple: boolean;
    category?: string;
    selectedTGGroup: string[];
    clubswithFemaleMembers: boolean;
    clubPercentage: string;
    clubMemberCount: string;
}

export interface CoordinateInterface {
    coordinates: [number, number][];
}

export interface MapMarkerCoordinate {
    latitude: number;
    longitude: number;
}
export interface MapMarkerFeature {
    geometry: {
        type: 'Point';
        coordinates: [number, number]; // [longitude, latitude]
        location: string;
    };
    type: 'Feature';
    properties: {
        clubDemandType: number;
        id: number;
        name: string;
        played: boolean;
        ct: number;
        myTgGroupIds: number[];
        favClub: boolean;
        tier: number;
        addr: string;
        color: string;
        isSelected: boolean;
        isContact: boolean;
        hasPlayAsCoupleMember: boolean;
        isFriend: boolean;
        hasFemalMember: boolean;
        isMyTgGroupMember: boolean;
        cluster: boolean;
        cluster_id: number;
        point_count: number;
        point_count_abbreviated: string;
    };
    id: number;
}

export interface MapMarkerPoint {
    x: number;
    y: number;
}

export interface MapMarkerProperties {
    coordinates: MapMarkerCoordinate;
    features: MapMarkerFeature[];
    points: MapMarkerPoint;
}

export interface MapRegion {
    geometry: {
        coordinates: [number, number]; // [longitude, latitude]
        type: string;
    };
    properties: {
        bearing: number;
        isUserInteraction: boolean;
        visibleBounds: [[number, number], [number, number]]; // [[longitude1, latitude1], [longitude2, latitude2]]
        heading: number;
        pitch: number;
        zoomLevel: number;
    };
    type: string;
}

export interface MapClubOffers {
    visible_to_all: boolean;
    user_id: string;
    id: string;
    created_at: string; // ISO date string
    club_id: number;
    start_date: string; // ISO date string
    end_date: string; // ISO date string
    details: string;
    tiervisibility: number[];
    offer_id: number;
    status: string;
    private_network_id: string | null;
    club_name: string;
    club_address: string;
    guest_time_restrictions: {
        [day: string]: string; // e.g., "Monday": "All Day"
    };
    closurePeriods: any[]; // You can specify more detail if you know the structure
    lowest_visible_tier: number;
    tierVisibility: number[];
    my_tg_group_id: string[]; // Assuming these are UUIDs
    chatChannels: any | null; // Replace `any` with specific type if known
    creatorName: string;
    forFriends: boolean;
    isSingleGroupOffer: boolean;
    creatorIsFriend: boolean;
    requested: boolean;
}

export interface GameReview {
    game_id: number;
    user_id: string;
    review: string;
    photo: string;
    created_at: string; // ISO date string
    username: string;
    first_name: string;
    last_name: string;
    profile_photo: string;
    club_name: string;
    game_date: string; // ISO date string
    is_own_review: boolean;
}

export interface Offer {
    id: string;
    offer_id: number;
    user_id: string;
    created_at: string; // ISO date string
    start_date: string; // ISO date string
    end_date: string; // ISO date string
    details: string;
    status: 'created' | 'active' | 'expired' | 'cancelled'; // adjust as per possible statuses
    creatorName: string;
    creatorIsFriend: boolean;
    tierVisibility: number[]; // or more specific type if known
    requested: boolean;
    club_id?: number;
}

export interface DateRange {
    startDate: string;
    endDate: string;
}

export interface OfferDetail {
    id: string;
    user_id: string;
    offer_id: number;
    start_date: string; // ISO date string
    end_date: string; // ISO date string
    details: string;
    my_tg_group_id: string[]; // assuming array of string IDs
    club_name: string;
    club_address: string;
    creator_name: string;
    creator_is_super_host: boolean;
    creator_is_super_guest: boolean;
    creator_is_tg_founder: boolean;
    creator_is_tg_ambassador: boolean;
    creator_id: string;
    group_name: string | null;
    creatorIsFriend: boolean;
    requested: boolean;
    creator_profile_photo: string;
    club_id: number;
}
