import parsePhoneNumberFromString from 'libphonenumber-js';

const formatPhoneNumber = (input: string) => {
    const phoneNumber = parsePhoneNumberFromString(input);

    if (!phoneNumber?.isValid()) return input;

    const countryCode = phoneNumber.countryCallingCode;
    const national = phoneNumber.nationalNumber;

    return `+${countryCode} (${national.slice(0, 3)}) ${national.slice(3, 6)}-${national.slice(6)}`;
};

export default formatPhoneNumber;
