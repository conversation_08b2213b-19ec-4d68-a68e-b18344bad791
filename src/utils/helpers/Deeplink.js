import { Linking } from "react-native";

export function getURLParams(url) {
    //var url = "http://example.com?myVar=test&otherVariable=someData&number=123"

    var regex = /[?&]([^=#]+)=([^&#]*)/g,
        params = {},
        match;
    while (match = regex.exec(url)) {
        params[match[1]] = match[2];
    }
    return params
}

export const handleOpenLink = async (url) => {
    try {
        await Linking.openURL(url);
    } catch {
        alert('URL is invalid')
    }
};

export const extractUrlFromHtml = (htmlContent) => {
    const scriptMatch = htmlContent.match(/window\.location\.href = '(.*?)'/);
    if (scriptMatch && scriptMatch[1]) {
        return scriptMatch[1];
    }
    
    return null;
};
