import config from '../../config';
import routes from '../../config/routes';
import constants from '../../utils/constants/constants';
import { getRouteName, ids, navigateToRequest } from '../../components/layout/notifications/Helper';
import { handleStreamNotificationPress } from '../../components/layout/notifications/streamNotificationHandling';
import { getOfferDetails } from '../../screens/my-TG-Stream-Chat/offersAndEvents/action/getOfferDetails';
import { fetcher } from '../../service/fetcher';
import { GET_PEGBOARD_DETAILS_V3 } from '../../service/EndPoint';

/**
 * Function call when user clicks on push notification
 * @param {string} type - Notification type
 * @param {Object} data - Notification data
 * @param {Object} navigationRef - Navigation reference
 * @param {Object} user - User object
 * @param {Object} actions - Global actions
 * @param {Object} chatClient - Stream chat client
 * @param {Function} setChannel - Function to set channel
 */
const handleNotificationPress = async (
    type,
    data = {},
    navigationRef,
    user,
    actions,
    chatClient,
    setChannel,
) => {
    try {
        if (!navigationRef || !user || !actions) {
            actions?.setAppLoader(false);
            console.error('Missing required parameters:', { navigationRef, user, actions });
            return;
        }

        const id = data[ids[type]] ?? data?.id;
        const route = getRouteName(type, id);

        // Handle club validation states
        if (user?.clubs?.length === 1 && user?.clubs[0]?.is_replaced) {
            actions?.setAppLoader(false);
            navigationRef.navigate(routes.CLUB_THANK_YOU_SCREEN);
            removeNotification(itemId);
            return;
        }

        if (user?.yearly_club_validation === constants.YEARLY_VALIDATION.VERIFICATION_REQUIRED) {
            actions?.setAppLoader(false);
            navigationRef.navigate(routes.CLUB_VALIDATION_SCREEN);
            removeNotification(itemId);
            return;
        }

        // Handle different notification types
        switch (type) {
            case undefined:
                actions?.setAppLoader(false);
                navigationRef.navigate(config.routes.BOTTOM_TAB_NAVIGATION, { screen: config.routes.TG_CHAT });
                actions?.setComeFromNotification(false);
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.referralJoined:
                actions?.setAppLoader(false);
                navigationRef.navigate(config.routes.My_Referrals);
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.Friend_Joined_My_Tg_Group:
                const params = {
                    type,
                    user_id: user?.id,
                    groupStreamId: data?.groupStreamChannelId,
                    request_id: data?.request_id,
                };
                navigateToRequest(navigationRef, params, () => {}, actions);
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.clubUpdate:
                actions?.setAppLoader(false);
                navigationRef.navigate(config.routes.My_Golf_Clubs);
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.My_Tg_All_Friend:
                actions?.setAppLoader(false);
                navigationRef.navigate(config.routes.BOTTOM_TAB_NAVIGATION, { screen: config.routes.TG_CHAT });
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.Friend_Request_Accepted:
            case config.notificationTypeConstants.Friend_Request_Received:
            case config.notificationTypeConstants.Friend_Request_Declined:
            case config.notificationTypeConstants.Mutual_Friends_Connected:
                const friendParams = {
                    type:
                        type === config.notificationTypeConstants.Mutual_Friends_Connected
                            ? config.notificationTypeConstants.Friend_Request_Accepted
                            : type,
                    user_id: user.id,
                    request_id: data?.request_id,
                };
                navigateToRequest(navigationRef, friendParams, () => {}, actions);
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.Friend_Completed_Game:
                await handleStreamNotificationPress(
                    chatClient,
                    user,
                    data?.streamChannelId,
                    navigationRef,
                    setChannel,
                    actions,
                    removeNotification,
                    itemId,
                );
                break;

            case config.notificationTypeConstants.Join_Group_Request:
                actions?.setAppLoader(false);
                navigationRef.navigate(config.routes.PENDING_MEMBER_REQUEST, {
                    streamId: data?.groupId,
                });
                removeNotification(itemId);
                break;
            case config.notificationTypeConstants.CLUB_MUTED:
                actions?.setAppLoader(false);
                navigationRef.navigate(config.routes.My_Golf_Clubs);
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.My_Post_Comment:
            case config.notificationTypeConstants.UPDATE_EMAIL:
            case config.notificationTypeConstants.Admin_Profile_Edit:
                const post = {
                    id: data.post_id,
                    username: user?.username,
                };
                actions?.setAppLoader(false);
                navigationRef.navigate(routes.PERSONAL_PROFILE);
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.My_Tg_Offer:
                const payload = {
                    userId: user?.id,
                    offerId: data?.offer_id,
                };
                const offerDetailsRes = await getOfferDetails(payload);
                actions?.setSelectedOffer({ isOpen: true, selectedOffer: offerDetailsRes?.data });
                navigationRef.navigate(config.routes.OFFER_DETAILS, {
                    offerID: data?.offer_id,
                });
                actions?.setAppLoader(false);
                break;

            case config.notificationTypeConstants.MY_TG_GROUP:
                actions?.setAppLoader(false);
                navigationRef.navigate(config.routes.MY_TG_GROUP_INFO, {
                    group: {
                        streamId: data?.streamChannelId,
                    },
                });
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.OFFLINE_GAME_LOGGED:
            case config.notificationTypeConstants.NGV_UPDATE:
                navigationRef.navigate(config.routes.LIFE_TIME_HISTORY, {
                    ngv: user.net_games_value,
                    user: user,
                });
                actions?.setAppLoader(false);
                break;

            case config.notificationTypeConstants.Message_New:
                await handleStreamNotificationPress(
                    chatClient,
                    user,
                    data?.channel_id,
                    navigationRef,
                    setChannel,
                    actions,
                    removeNotification,
                    itemId,
                );
                break;

            case config.notificationTypeConstants.TIER_REVISION:
                actions?.setAppLoader(false);
                navigationRef.navigate(config.routes.SETTING);
                removeNotification(itemId);
                break;

            case config.notificationTypeConstants.FRIEND_FAVORITE_CLUB:
                await handleStreamNotificationPress(
                    chatClient,
                    user,
                    data?.streamChannelId,
                    navigationRef,
                    setChannel,
                    actions,
                    removeNotification,
                    itemId,
                );
                break;

            case config.notificationTypeConstants.PEGBOARD:
                const handleGetClubList = async (item) => {
                    try {
                        fetcher({
                            method: 'POST',
                            endpoint: GET_PEGBOARD_DETAILS_V3,
                            body: {
                                userId: user?.id,
                                pegboardId: item?.pegboard_id,
                            },
                        })
                            .then((res) => {
                                if (res.status) {
                                    navigationRef.navigate(config.routes.PEGBOARD_ADD_CLUB, {
                                        pegboardId: res?.data?.pegboard?.id,
                                        pegBoardName: res?.data?.pegboard?.name,
                                        totalClubs: res?.data?.pegboardClubs,
                                        creatorId: res?.data?.pegboard?.creator_id,
                                        isPrivate: res?.data?.pegboard?.is_private,
                                        item: {
                                            id: res?.data?.pegboard?.id,
                                            creatorId: res?.data?.pegboard?.creator_id,
                                        },
                                    });
                                    actions?.setAppLoader(false);
                                }
                            })
                            .catch((err) => {
                                showToast({});
                            });
                    } catch (error) {
                        console.log(error);
                    }
                };
                handleGetClubList(data);
                break;

            case config.notificationTypeConstants.NEW_CLUB_USER:
                navigationRef.navigate('Club Member Details', {
                    clubId: data?.clubId,
                });
                actions?.setAppLoader(false);
                break;

            case config.notificationTypeConstants.ADMIN_NOTIFICATION:
                actions?.setAppLoader(false);
                break;

            case config.notificationTypeConstants.MUTED_CLUB_REMINDER:
                navigationRef.navigate(config.routes.MUTE_CLUB_POPUP_SCREEN);
                actions?.setAppLoader(false);
                break;

            case config.notificationTypeConstants.FEMALE_MEMBER_JOINED:
            case config.notificationTypeConstants.COUPLE_JOINED:
                navigationRef.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
                    screen: config.routes.CLUBS,
                    params: { clubId: data?.clubId },
                });
                actions?.setAppLoader(false);
                break;

            default:
                // Handle request/game notifications
                if (
                    type.includes(config.notificationTypeConstants.Request) ||
                    type.includes(config.notificationTypeConstants.Game) ||
                    type.includes(config.notificationTypeConstants.GAME_REMINDER) ||
                    type.includes(config.notificationTypeConstants.Offer)
                ) {
                    const requestParams = {
                        type,
                        user_id: user.id,
                        request_id: data?.request_id,
                        host_id: data?.host_id || data?.hostId,
                        channel_url: data?.channelURL,
                    };

                    const targetRoute = user?.membership_active ? navigationRef : config.routes.BLOCKER_SCREEN;
                    navigateToRequest(targetRoute, requestParams, () => {}, actions, chatClient, setChannel);
                    // removeNotification(itemId);
                }
                // Handle event notifications
                else if (type.includes(config.notificationTypeConstants.Event)) {
                    const targetRoute = user?.membership_active ? route : config.routes.BLOCKER_SCREEN;
                    navigationRef.navigate(targetRoute, { id });
                    actions?.setAppLoader(false);
                }
                // Handle group notifications
                else if (type?.includes(config.notificationTypeConstants.My_Tg_Groups)) {
                    navigationRef.navigate(config.routes.GROUPS);
                    actions?.setAppLoader(false);
                }
                // Handle map notifications
                else if (type?.includes(config.notificationTypeConstants.Map)) {
                    navigationRef.navigate(config.routes.CLUBS);
                    actions?.setAppLoader(false);
                }
                // Handle regular route navigation
                else if (route && user?.membership_active) {
                    navigationRef.navigate(route, { id });
                    actions?.setAppLoader(false);
                }
                break;
        }
    } catch (error) {
        console.error('Error handling notification:', error);
    }
};

// Export as default
export default handleNotificationPress;
