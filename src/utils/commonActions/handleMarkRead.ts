import { getTealDotStatus } from '../../screens/HomeScreen/action/getTealDotStatus';
import { UPDATE_TEAL_DOT_STATUS } from '../../service/EndPoint';
import { fetcher } from '../../service/fetcher';

export const handleMarkRead = async (flagKey: string, userId: string, actions: any) => {
    if (!flagKey) return;
    let res = await fetcher({
        endpoint: UPDATE_TEAL_DOT_STATUS,
        method: 'POST',
        body: {
            userId,
            flagKey,
            value: false,
        },
    });
    if (res?.status) {
        handleTealDotStatus(userId, actions);
    }
};

const handleTealDotStatus = async (userId: string, actions: any) => {
    if (userId) {
        let res = await getTealDotStatus({ userId });
        actions.setTealDotStatus(res?.data);
    }
};
