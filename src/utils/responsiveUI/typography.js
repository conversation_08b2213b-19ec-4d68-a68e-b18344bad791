import {scaleSize} from './mixins';

export const FONT_SIZE_8 = scaleSize(8);
export const FONT_SIZE_9 = scaleSize(9);
export const FONT_SIZE_10 = scaleSize(10);
export const FONT_SIZE_11 = scaleSize(11);
export const FONT_SIZE_12 = scaleSize(12);
export const FONT_SIZE_13 = scaleSize(13);
export const FONT_SIZE_14 = scaleSize(14);
export const FONT_SIZE_15 = scaleSize(15);
export const FONT_SIZE_16 = scaleSize(16);
export const FONT_SIZE_17 = scaleSize(17);
export const FONT_SIZE_18 = scaleSize(18);
export const FONT_SIZE_20 = scaleSize(20);
export const FONT_SIZE_21 = scaleSize(21);
export const FONT_SIZE_22 = scaleSize(22);
export const FONT_SIZE_24 = scaleSize(24);
export const FONT_SIZE_25 = scaleSize(25);
export const FONT_SIZE_28 = scaleSize(28);
export const FONT_SIZE_30 = scaleSize(30);
export const FONT_SIZE_32 = scaleSize(32);
export const FONT_SIZE_40 = scaleSize(40);
export const FONT_SIZE_80 = scaleSize(80);