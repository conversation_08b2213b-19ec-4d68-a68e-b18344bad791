import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { SafeAreaView, View, TouchableOpacity, Text, Platform } from 'react-native';
import BackArrowIcon from '../../assets/images/back-arrow.svg';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import EditIcon from '../../assets/images/edit_new.svg';
import { Back } from '../../assets/images/svg';

import ClubMemberIcon from '../../assets/images/club-member.svg';
import TGText from '../fields/TGText';
import { TripleDot } from '../../assets/svg';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function ProfileHeaderNew({
    title,
    marginBottom = 30,
    editing = false,
    iconShow = '',
    onClick,
    handleTripleDotPress = () => {},
}) {
    const navigation = useNavigation();
    const insets = useSafeAreaInsets();

    return (
        <SafeAreaView style={{ flexDirection: 'row' }}>
            <View
                style={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    paddingHorizontal: Spacing.SCALE_16,
                    flex: 1,
                    width: '100%',
                    paddingTop: Platform.OS === 'ios' ? Spacing.SCALE_1 : insets.top + Spacing.SCALE_20,
                }}>
                <TouchableOpacity
                    onPress={() => {
                        navigation.goBack();
                    }}
                    style={{
                        height: 50,
                        width: 50,
                        justifyContent: 'center',
                    }}>
                    <Back fill="rgba(51, 51, 51, 1)" />
                </TouchableOpacity>
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                    <TGText
                        style={{
                            flex: 1,
                            color: 'rgba(51, 51, 51, 1)',
                            fontSize: Typography.FONT_SIZE_18,
                            fontFamily: 'Ubuntu-Medium',
                        }}>
                        {title}
                    </TGText>
                </View>
            </View>
        </SafeAreaView>
    );
}
