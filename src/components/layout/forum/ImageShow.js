import React, { useState, useEffect } from 'react';
import { View, Text, Image, Dimensions, ActivityIndicator, TouchableOpacity, StatusBar } from 'react-native';
import AvatarImage from '../../../assets/images/avatar.png';
import { colors } from '../../../theme/theme';
import getTimeSincePosted from '../../../utils/helpers/getTimeSincePosted';
import Moment from 'moment';
import LocationIcon from '../../../assets/images/location-black.svg';
import { SafeAreaView } from 'react-native-safe-area-context';
import CloseIcon from '../../../assets/images/close.svg';
import DownloadIcon from '../../../assets/images/download.svg';
import { NavigationContainer } from '@react-navigation/native';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import RNFetchBlob from 'rn-fetch-blob';
import { DefaultNameProfile } from '../../../utils/helpers/PersonalProfileHelper';
import useThumbnail from '../../../hooks/useThumbnail';
import constants from '../../../utils/constants/constants';
import TGText from '../../fields/TGText';

const { width } = Dimensions.get('window');

export default function ImageShow({ route, navigation }) {
    const [timeSincePosted, setTimeSincePosted] = useState();
    const [error, setError] = useState(false);
    const [loading, setLoading] = useState(true);
    const [imageDownload, setImageDownload] = useState(false);

    const review = route.params.review;
    const imageUri = useThumbnail(route.params?.imageUri, constants.ImageSize[1280])?.thumbnailUrl;
    const review_profile_photo = useThumbnail(review?.profile_photo, constants.ImageSize[128])?.thumbnailUrl;
    const review_photo = useThumbnail(review?.photo, constants.ImageSize[1280])?.thumbnailUrl;

    const [imageHeight, setImageHeight] = useState(Dimensions.get('window').width);

    useEffect(() => {
        if (review !== undefined && review !== null) {
            getTimeSincePosted(review, setTimeSincePosted);

            if (review.photo) {
                Image.getSize(
                    review.photo,
                    (imgwidth, height) => {
                        setImageHeight((height * width) / imgwidth);
                    },
                    (error) => {
                        console.log(error);
                    },
                );
            }
        }

        setTimeout(() => {
            setLoading(false);
        }, 2000);
    }, [review]);

    useEffect(() => {
        if (imageUri) {
            Image.getSize(
                imageUri,
                (imgwidth, height) => {
                    setImageHeight((height * width) / imgwidth);
                },
                (error) => {
                    console.log(error);
                },
            );
        }
    }, [imageUri]);

    const handleDownload = (url) => {
        setLoading(true);
        RNFetchBlob.config({
            fileCache: true,
            appendExt: 'png',
        })
            .fetch('GET', url)
            .then((res) => {
                CameraRoll.saveToCameraRoll(res.data, 'photo')
                    .then((res) => message())
                    .catch((err) => setLoading(false));
            })
            .catch((error) => setLoading(false));
    };

    const message = () => {
        setLoading(false);
        setImageDownload(true);
    };

    useEffect(() => {
        if (imageDownload) {
            setTimeout(() => {
                setImageDownload(false);
            }, 1000);
        }
    }, [imageDownload]);

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.blackRGB} />
            <SafeAreaView
                style={{
                    flex: 1,
                    backgroundColor: 'black',
                    justifyContent: 'space-between',
                }}>
                <View>
                    <View>
                        <View
                            style={{
                                flexDirection: 'row',
                                margin: 10,
                                justifyContent: 'space-between',
                                zIndex: 1,
                            }}>
                            <TouchableOpacity style={{ padding: 10 }} onPress={() => navigation.goBack()}>
                                <CloseIcon />
                            </TouchableOpacity>

                            {review !== undefined && review !== null && (
                                <TouchableOpacity
                                    style={{ padding: 10, marginTop: 3 }}
                                    onPress={() => handleDownload(review.photo)}>
                                    <DownloadIcon />
                                </TouchableOpacity>
                            )}
                        </View>

                        {review !== undefined && review !== null && review.row_type === 1 && (
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginBottom: 10,
                                    padding: 10,
                                }}>
                                <View
                                    style={{
                                        backgroundColor: colors.lightgray,
                                        width: 36,
                                        height: 36,
                                        borderRadius: 36,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}>
                                    {review.profile_photo ? (
                                        <Image
                                            source={{ uri: review_profile_photo }}
                                            style={{
                                                height: 36,
                                                width: 36,
                                                borderRadius: 36,
                                            }}
                                            resizeMode={'cover'}
                                        />
                                    ) : (
                                        <DefaultNameProfile
                                            fName={review.username}
                                            containerStyle={{
                                                backgroundColor: null,
                                            }}
                                            textStyle={{ fontSize: 16 }}
                                        />
                                    )}
                                </View>

                                <View style={{ paddingLeft: 15, width: '90%' }}>
                                    <View style={{ flexDirection: 'row' }}>
                                        <TGText
                                            style={{
                                                fontFamily: 'Ubuntu-Medium',
                                                fontSize: 14,
                                                marginBottom: 3,
                                                color: 'white',
                                                flex: 0.65,
                                            }}>
                                            {review.username}
                                        </TGText>
                                    </View>

                                    <Text
                                        style={{
                                            fontFamily: 'Ubuntu-Medium',
                                            fontSize: 12,
                                            color: 'white',
                                        }}>
                                        {timeSincePosted}
                                    </Text>
                                </View>
                            </View>
                        )}
                    </View>

                    {review !== undefined && review !== null && review.photo && !error ? (
                        <View
                            style={{
                                width: '100%',
                                height:
                                    review !== undefined && review !== null && review.row_type === 1 ? '80%' : '90%',
                                justifyContent: 'center',
                            }}>
                            <Image
                                resizeMode="contain"
                                onError={(e) => {
                                    setError(true);
                                }}
                                source={{ uri: review_photo }}
                                style={{
                                    width: '100%',
                                    height: imageHeight,
                                    marginBottom: 5,
                                }}
                            />
                        </View>
                    ) : (
                        <View
                            style={{
                                width: '100%',
                                height: '90%',
                                justifyContent: 'center',
                            }}>
                            <Image
                                resizeMode="contain"
                                onError={(e) => {
                                    setError(true);
                                }}
                                source={{ uri: imageUri }}
                                style={{
                                    width: '100%',
                                    height: imageHeight,
                                    marginBottom: 5,
                                }}
                            />
                        </View>
                    )}

                    {review !== undefined && review !== null && review.row_type === 1 ? (
                        <View style={{ flexDirection: 'row', marginLeft: 10 }}>
                            <TGText
                                style={{
                                    fontSize: 14,
                                    fontFamily: 'Ubuntu-Medium',
                                    color: 'white',
                                }}>
                                {review.like_count} Likes{'  '}
                            </TGText>

                            <TGText style={{ color: 'white' }}>{'\u2022'}</TGText>

                            <TGText
                                style={{
                                    fontSize: 14,
                                    fontFamily: 'Ubuntu-Medium',
                                    color: 'white',
                                }}>
                                {'  '}
                                {review.comment_count} Comments
                            </TGText>
                        </View>
                    ) : (
                        <View />
                    )}
                </View>

                {imageDownload && (
                    <View
                        style={{
                            backgroundColor: 'white',
                            position: 'absolute',
                            left: 0,
                            right: 0,
                            padding: 20,
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexDirection: 'row',
                            marginTop: 50,
                        }}>
                        <Text
                            style={{
                                color: 'black',
                                fontSize: 14,
                                fontWeight: 'normal',
                                flexWrap: 'wrap',
                            }}>
                            Photo saved successfully
                        </Text>
                    </View>
                )}

                {loading && (
                    <View
                        style={{
                            position: 'absolute',
                            width: '100%',
                            height: '100%',
                            justifyContent: 'center',
                        }}>
                        <ActivityIndicator color="white" size="large" />
                    </View>
                )}
            </SafeAreaView>
        </>
    );
}
