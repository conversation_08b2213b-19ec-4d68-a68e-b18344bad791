import React, { useRef, useEffect, useState, useContext, useCallback } from 'react';
import {
    SafeAreaView,
    ScrollView,
    View,
    Text,
    Animated,
    TouchableOpacity,
    Dimensions,
    Linking,
    Image,
    Alert,
} from 'react-native';
import moment from 'moment';

import Icon from 'react-native-vector-icons/AntDesign';
import ClubsIcon from '../../assets/images/clubs-white.svg';
import PlayIcon from '../../assets/images/play-white.svg';
import LeaderboardIcon from '../../assets/images/leaderboard-white.svg';
import NewSetting from '../../assets/svg/newSettings.svg';
import LogoutIcon from '../../assets/images/logout-white.svg';
import auth from '@react-native-firebase/auth';
import { AuthContext } from '../../context/AuthContext';
import useClient from '../../hooks/useClient';
import { UPDATE_USER } from '../../graphql/mutations/user';
import Help from '../../assets/images/svg/faq.svg';
import TGText from '../fields/TGText';
import { colors } from '../../theme/theme';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { DefaultNameProfile } from '../../utils/helpers/PersonalProfileHelper';
import { StreamChatContext } from '../../context/StreamChatContext';
import { ONE_TO_ONE, SYSTEM, SYSTEM_MESSAGE, USER_CREATED_GROUP } from '../../screens/my-TG-Stream-Chat/client';
import { GlobalContext } from '../../context/contextApi';
import config from '../../config';
const Clevertap = require('clevertap-react-native');

const { width, height } = Dimensions.get('window');

const links = {
    ['My TG']: PlayIcon,
    ['Play']: PlayIcon,
    ['Events']: ClubsIcon,
    ['Benefits']: LeaderboardIcon,
    ['Settings']: NewSetting,
    ['Help']: Help,
    ['Log out']: LogoutIcon,
};

const helpLinks = ['FAQ', 'ContactUs'];

function HelpNavigation({ currentRoute, navigation, closeDrawer }) {
    const [expanded, setExpanded] = useState(true);

    const handleFAQHelpNavigatin = (name) => {
        closeDrawer();
        if (name === 'FAQ') {
            Clevertap.recordEvent('Help');
        }
        name === 'ContactUs' ? Linking.openURL('mailto:<EMAIL>') : navigation.navigate(name);
    };

    return (
        <>
            <TouchableOpacity
                onPress={() => setExpanded(!expanded)}
                style={{ marginBottom: expanded ? 20 : 30, paddingLeft: 30 }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        opacity: ['FAQ', 'ContactUs'].includes(currentRoute) ? 1 : 0.5,
                    }}>
                    <Help height={20} width={20} />
                    <Text
                        style={{
                            color: 'white',
                            marginLeft: 15,
                            fontFamily: 'Ubuntu-Medium',
                            fontSize: 20,
                        }}>
                        Help
                    </Text>
                    <View
                        style={{
                            textAlign: 'center',
                            marginLeft: 14,
                            height: 20,
                            width: 22,
                            marginTop: Spacing.SCALE_8,
                        }}>
                        {expanded ? (
                            <Icon name="down" size={15} color="white" />
                        ) : (
                            <Icon name="right" size={15} color="white" />
                        )}
                    </View>
                </View>
            </TouchableOpacity>
            {expanded && (
                <View style={{ paddingLeft: 65, marginBottom: 15 }}>
                    {helpLinks.map((name) => (
                        <TouchableOpacity
                            key={name}
                            style={{ marginBottom: 15 }}
                            onPress={() => handleFAQHelpNavigatin(name)}>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    opacity: name === currentRoute ? 1 : 0.5,
                                }}>
                                <Text
                                    style={{
                                        color: 'white',
                                        fontFamily: 'Ubuntu-Medium',
                                    }}>
                                    {name === 'ContactUs' ? 'Contact Us' : name}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    ))}
                </View>
            )}
        </>
    );
}

export default function SideNavigationDrawer({ drawerOpen, user, navigation, closeDrawer, currentRoute }) {
    const { refreshUser } = useContext(AuthContext);
    const moveAnim = useRef(new Animated.Value(-width / 3)).current;
    const client = useClient();
    const { chatClient, unreadcount, setUnreadcount } = useContext(StreamChatContext);
    const { actions } = useContext(GlobalContext);

    // useEffect(() => {
    //     const eventListner = chatClient.on((event) => {
    //         if (event?.total_unread_count !== undefined || event?.unread_channels !== undefined) {
    //             setUnreadcount(event?.total_unread_count);
    //         } else if (event?.type === 'message.deleted') {
    //             getUnreadCount();
    //         }
    //     });
    //     return () => eventListner?.unsubscribe();
    // }, [chatClient]);

    const filters = {
        members: { $in: [user?.id] },
        $or: [
            {
                $and: [
                    {
                        last_message_at: { $lte: moment().add('1', 'hour') },
                    },
                    {
                        type: { $eq: ONE_TO_ONE },
                    },
                ],
            },
            {
                $and: [
                    {
                        type: { $in: [SYSTEM, USER_CREATED_GROUP] },
                    },
                ],
            },
        ],
    };
    // const getUnreadCount = async () => {
    //     const channels = await chatClient?.queryChannels(filters, { last_message_at: -1 }, { limit: 20 });
    //     const counts = channels?.map((channel) => channel?.state?.unreadCount);
    //     const unreadCount = counts?.filter((count) => count > 0);
    //     if (unreadCount) {
    //         setUnreadcount(unreadCount?.length);
    //     }
    // };

    useEffect(() => {
        if (drawerOpen) {
            Animated.spring(moveAnim, {
                toValue: 0,
                duration: 100,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.spring(moveAnim, {
                toValue: -width / 2,
                duration: 100,
                useNativeDriver: true,
            }).start();
        }
    }, [drawerOpen]);

    const handleSettingNavigation = () => {
        closeDrawer();
        navigation.navigate('Settings');
    };

    const handleOtherScreensNavigation = useCallback(
        (name) => {
            closeDrawer();
            Clevertap.recordEvent(name);

            const screenActions = {
                Marketplace: () => Linking.openURL('https://thousand-greens.myshopify.com/collections/all'),
                Settings: () => navigation.navigate('Settings'),
                'Contact Us': () => Linking.openURL('mailto:<EMAIL>'),
                'Log out': () => {
                    Alert.alert('Confirm!', 'Are you sure you want to log out?', [
                        { text: 'Cancel', style: 'cancel' },
                        {
                            text: 'OK',
                            onPress: async () => {
                                await auth().signOut();
                            },
                        },
                    ]);
                },
            };

            if (screenActions[name]) {
                screenActions[name]();
                return;
            }

            if (name === 'My TG') {
                navigation.navigate(name, { mainTab: 3 });
                actions.setCurrentTab(0);
                actions?.archiveListAction(false);
            } else if (name === config.routes.PLAY) {
                navigation.navigate(name, { mainTab: 0 });
                actions?.setIsFirstTimeInMap(true);
                actions.setMapCurrentFilter('');
                actions?.setMapCurrentState({});
                actions.setMapFilterState({ category: 'All' });
                actions.setCurrentTab(0);
            } else {
                navigation.navigate(name);
                actions?.archiveListAction(false);
            }
        },
        [closeDrawer, navigation, actions],
    );

    return (
        <Animated.View style={{ position: 'absolute', transform: [{ translateX: moveAnim }] }}>
            <SafeAreaView style={{ height, width }}>
                <View
                    style={{
                        paddingLeft: 0,
                        paddingTop: Spacing.SCALE_15,
                        flex: 1,
                    }}>
                    <View style={{ flexGrow: 1 }}>
                        <View style={{ flexDirection: 'row', paddingLeft: 30 }}>
                            <View>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                        marginBottom: Spacing.SCALE_18,
                                    }}>
                                    <TouchableOpacity
                                        onPress={handleSettingNavigation}
                                        style={{
                                            flexDirection: 'row',
                                            marginRight: Spacing.SCALE_30,
                                            alignItems: 'center',
                                        }}>
                                        {user.profilePhoto ? (
                                            <Image
                                                source={{
                                                    uri: user.profilePhoto,
                                                }}
                                                style={{
                                                    height: Size.SIZE_40,
                                                    width: Size.SIZE_40,
                                                    borderRadius: 10,
                                                }}
                                            />
                                        ) : (
                                            <DefaultNameProfile
                                                fName={user?.first_name}
                                                lName={user?.last_name}
                                                containerStyle={{
                                                    height: Size.SIZE_40,
                                                    width: Size.SIZE_40,
                                                    backgroundColor: colors.lightgray,
                                                }}
                                            />
                                        )}
                                        <View
                                            style={{
                                                paddingLeft: Spacing.SCALE_15,
                                            }}>
                                            <TGText
                                                style={{
                                                    color: 'white',
                                                    fontSize: Typography.FONT_SIZE_16,
                                                }}>
                                                {user.first_name} {user.last_name}
                                            </TGText>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                        <View style={{ flexGrow: 1 }}>
                            <View style={{ flex: 1 }}>
                                <ScrollView showsVerticalScrollIndicator={false}>
                                    {Object.entries(links).map(([name, Icon]) => {
                                        if (name === 'Help') {
                                            return (
                                                <React.Fragment key={name}>
                                                    <HelpNavigation
                                                        currentRoute={currentRoute}
                                                        navigation={navigation}
                                                        closeDrawer={closeDrawer}
                                                    />
                                                </React.Fragment>
                                            );
                                        }

                                        return (
                                            <TouchableOpacity
                                                key={name}
                                                style={{
                                                    marginBottom: Spacing.SCALE_24,
                                                    paddingLeft: Spacing.SCALE_30,
                                                }}
                                                onPress={() => handleOtherScreensNavigation(name)}>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        alignItems: 'center',
                                                        opacity: name === currentRoute ? 1 : 0.5,
                                                    }}>
                                                    {name === ' Log out' ? (
                                                        <Image
                                                            source={Icon}
                                                            style={{
                                                                height: Size.SIZE_30,
                                                                width: Size.SIZE_30,
                                                                marginLeft: name === 'Contact Us' ? -5 : 0,
                                                            }}
                                                        />
                                                    ) : (
                                                        <Icon height={20} width={20} />
                                                    )}
                                                    <TGText
                                                        style={{
                                                            color: 'white',
                                                            marginLeft: name === 'Contact Us' ? 10 : 15,
                                                            fontSize: 20,
                                                            marginLeft: name === 'Contact Us' ? 12.5 : 15,
                                                        }}>
                                                        {name}
                                                    </TGText>
                                                    {name === 'Chat' && unreadcount !== 0 && (
                                                        <View
                                                            style={{
                                                                height: 7,
                                                                width: 7,
                                                                backgroundColor: 'red',
                                                                marginBottom: 10,
                                                                borderRadius: 100,
                                                            }}
                                                        />
                                                    )}
                                                </View>
                                            </TouchableOpacity>
                                        );
                                    })}
                                </ScrollView>
                            </View>
                        </View>
                    </View>
                </View>
            </SafeAreaView>
        </Animated.View>
    );
}
