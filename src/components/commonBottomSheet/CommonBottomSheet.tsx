import React, { ReactNode } from 'react';
import {
    Keyboard,
    KeyboardAvoidingView,
    Modal,
    Platform,
    Pressable,
    StyleSheet,
    View,
    ViewStyle,
    StyleProp,
} from 'react-native';

interface CommonBottomSheetProps {
    children: ReactNode;
    showPopup: boolean;
}

const CommonBottomSheet: React.FC<CommonBottomSheetProps> = ({ children, showPopup }) => {
    return (
        <Modal
            animationType="none"
            transparent={true}
            visible={showPopup}
            onRequestClose={Keyboard.dismiss} // Handles Android back press
        >
            <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined} style={styles.container}>
                <View style={styles.modalBodyWrapper}>
                    <Pressable style={styles.modalBackgroundStyle} onPress={Keyboard.dismiss} />
                    {children}
                </View>
            </KeyboardAvoidingView>
        </Modal>
    );
};

export default CommonBottomSheet;

const styles = StyleSheet.create({
    //@ts-ignore
    container: {
        flex: 1,
    } as StyleProp<ViewStyle>,
    modalBodyWrapper: {
        flex: 1,
    },
    modalBackgroundStyle: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
});
