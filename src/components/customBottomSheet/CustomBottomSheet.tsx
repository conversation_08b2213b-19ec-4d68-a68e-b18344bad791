import React from 'react';
import { View, Pressable, Text, StyleSheet, Modal } from 'react-native';

import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';

interface Option {
    label: string;
    action: () => void;
}

interface CustomBottomSheetProps {
    isVisible: boolean;
    onClose: () => void;
    options?: Option[];
    cancelLabel?: string;
}

const CustomBottomSheet: React.FC<CustomBottomSheetProps> = ({
    isVisible,
    onClose,
    options = [], // Array of options with label and action
    cancelLabel = 'Cancel',
}) => {
    return (
        <Modal visible={isVisible} transparent={true} style={styles.modalStyle} onRequestClose={onClose}>
            <View style={styles.container}>
                <Pressable style={styles.blankScreenWrapper} onPress={onClose} />
                <View style={styles.bodyStyle}>
                    <View style={styles.popupWrapper}>
                        {options.map((option, index) => (
                            <React.Fragment key={index}>
                                <Pressable
                                    style={styles.popupContent}
                                    onPress={() => {
                                        option.action();
                                        onClose();
                                    }}>
                                    <Text style={styles.popupOptionStyle}>{option.label}</Text>
                                </Pressable>
                                {index < options.length - 1 && <View style={styles.dividerStyle} />}
                            </React.Fragment>
                        ))}
                    </View>
                    <Pressable style={styles.popupWrapper1} onPress={onClose}>
                        <Text style={styles.cancelBtnStyle}>{cancelLabel}</Text>
                    </Pressable>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    popupWrapper: {
        width: Size.SIZE_340,
        position: 'absolute',
        bottom: Spacing.SCALE_85,
        backgroundColor: colors.bottomSheetOptionBackgroundColor,
        borderRadius: Size.SIZE_18,
        marginHorizontal: Spacing.SCALE_6,
        alignItems: 'center',
    },
    popupWrapper1: {
        width: Size.SIZE_340,
        minHeight: Size.SIZE_54,
        position: 'absolute',
        bottom: Spacing.SCALE_24,
        backgroundColor: colors.whiteColor,
        borderRadius: Size.SIZE_14,
        marginHorizontal: 7,
        justifyContent: 'center',
        alignItems: 'center',
    },
    cancelBtnStyle: {
        color: colors.Dark_Azure,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '600',
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
    },
    popupContent: {
        height: Size.SIZE_40,
        justifyContent: 'center',
    },
    popupOptionStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        color: colors.Dark_Azure,
        fontFamily: 'Ubuntu-Medium',
    },
    modalStyle: {
        paddingHorizontal: 0,
        marginHorizontal: 0,
        paddingVertical: 0,
        marginVertical: 0,
    },
    blankScreenWrapper: {
        flex: 1,
        backgroundColor: colors.transparentRgba,
    },
    bodyStyle: {
        backgroundColor: colors.transparentRgba,
        width: '100%',
        minHeight: '100%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dividerStyle: {
        backgroundColor: colors.shadowDarkColor,
        height: 1,
        width: '95%',
    },
});

export default CustomBottomSheet;
