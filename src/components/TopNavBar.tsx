import { FlatList, StyleSheet, Text, Touchable, TouchableOpacity, View } from 'react-native';
import React from 'react';

// theme, utils, hooks imports
import { colors } from '../theme/theme';
import { Size, Spacing } from '../utils/responsiveUI';

const TopNavBar = ({
    tabList,
    activeTab,
    setActiveTab,
    onPress = () => {},
}: {
    tabList: any[];
    activeTab: string;
    setActiveTab: (tab: string) => void;
    onPress?: () => void;
}) => {
    return (
        <View>
            <FlatList
                data={tabList}
                horizontal
                showsHorizontalScrollIndicator={false}
                ItemSeparatorComponent={() => <View style={{ width: Spacing.SCALE_8 }} />}
                renderItem={({ item }) => (
                    <TouchableOpacity
                        onPress={() => {
                            setActiveTab(item.name);
                            onPress();
                        }}
                        style={[styles.tabItem, activeTab === item.name && styles.activeTabItem]}>
                        <Text style={[styles.tabText, activeTab === item.name && styles.activeTabText]}>
                            {item.name}
                        </Text>
                    </TouchableOpacity>
                )}
            />
        </View>
    );
};

export default TopNavBar;

const styles = StyleSheet.create({
    tabItem: {
        paddingVertical: Spacing.SCALE_8,
        paddingHorizontal: Spacing.SCALE_16,
        borderRadius: Size.SIZE_40,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.whiteRGB,
        borderWidth: 1,
        borderColor: colors.darkgray,
    },
    tabText: {
        fontSize: Size.SIZE_12,
        fontWeight: '500',
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    activeTabText: {
        color: colors.whiteRGB,
    },
    activeTabItem: {
        backgroundColor: colors.tealRgb,
        borderWidth: 1,
        borderColor: colors.tealRgb,
    },
});
