import React, { useContext } from 'react';
import { Button } from 'react-native-elements';

import { MaximizeScreen, MinimizeScreen } from '../../assets/svg';
import { GlobalContext } from '../../context/contextApi';

interface ScreenResizeButtonProps {}

const ScreenResizeButton: React.FC<ScreenResizeButtonProps> = () => {
    const { state, actions } = useContext(GlobalContext);
    const locateIcon = () => {
        if (state?.isMapViewExpanded) {
            return <MinimizeScreen width={20} height={20} />;
        } else {
            return <MaximizeScreen width={20} height={20} />;
        }
    };

    return (
        <Button
            onPress={() => {
                actions.setIsMapViewExpanded(!state.isMapViewExpanded);
            }}
            icon={locateIcon()}
            buttonStyle={styles.buttonStyle}
            containerStyle={{
                position: 'absolute',
                borderRadius: 50,
                bottom: 50,
                right: '5%',
            }}
        />
    );
};

const styles = {
    buttonStyle: {
        backgroundColor: 'white',
        borderRadius: 50,
        padding: 15,
    },
};

export default ScreenResizeButton;
