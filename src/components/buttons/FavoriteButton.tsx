import React, { useEffect, useState } from 'react';
import { TouchableOpacity } from 'react-native';
import { FavoriteActiveIconNew, FavoriteIconNew } from '../../assets/svg';
import { colors } from '../../theme/theme';

interface FavoriteButtonProps {
    onPress: (isFavorited: boolean) => void;
    isFavorited: boolean;
    size?: number;
}

export default function FavoriteButton({ onPress, isFavorited, size = 20 }: FavoriteButtonProps) {
    const [active, setActive] = useState(isFavorited);
    useEffect(() => {
        setActive(isFavorited);
    }, [isFavorited]);

    return (
        <TouchableOpacity
            onPress={() => {
                setActive(!active);
                onPress(!active);
            }}
            style={{ justifyContent: 'center' }}>
            {active ? (
                <FavoriteActiveIconNew height={size} width={size} fill={colors.tealRgb} />
            ) : (
                <FavoriteIconNew height={size} width={size} />
            )}
        </TouchableOpacity>
    );
}
