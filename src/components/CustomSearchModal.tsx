import { StyleSheet, Text, View, TouchableOpacity, Platform, Keyboard } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

//Components imports
import TGSearchNew from '../screens/my-TG-friends/view/TGSearch';
import CommonDateRangeModal from './modals/CommonDateRangeModal';
import TealButtonNew from './buttons/TealButtonNew';

//Theme, utils, assets imports
import { colors } from '../theme/theme';
import { Size, Spacing, Typography } from '../utils/responsiveUI';
import { CrossIcon } from '../assets/svg';
import CommonDateField from './CommonDateField';
import { CLUB_DETAILS_MAP, placeDetails } from '../service/EndPoint';
import { MapClub } from '../interface';
import { GlobalContext } from '../context/contextApi';
import { fetcher } from '../service/fetcher';
import { AuthContext } from '../context/AuthContext';
import { SEARCH_OFFERS_BY_LOCATION_CLUB_AND_DATE } from '../utils/constants/strings';

interface CustomSearchModalProps {
    onClose: () => void;
    searchTerm: string;
    setSearchTerm: (term: string) => void;
    handleSearchPress: () => void;
    searchedClubList: any[];
    locations: any[];
    activeView: string;
    selectedRegion: { lat: number; lng: number } | null;
    setSelectedRegion: (region: { lat: number; lng: number } | null) => void;
    dateRange: DateRange;
    setDateRange: (dateRange: DateRange) => void;
    setSelectedClub: (club: MapClub | null) => void;
    setSearchType: (searchType: string) => void;
    searchedClub: MapClub | null;
    setSearchedClub: (club: MapClub | null) => void;
    setActiveView: (activeView: string) => void;
    setLocationType: (locationType: string) => void;
}

interface DateRange {
    startDate: string;
    endDate: string;
}

const CustomSearchModal: React.FC<CustomSearchModalProps> = ({
    onClose,
    searchTerm,
    setSearchTerm,
    handleSearchPress,
    locations,
    searchedClubList,
    activeView,
    selectedRegion,
    setSelectedRegion,
    dateRange,
    setDateRange,
    setSelectedClub,
    setSearchType,
    searchedClub,
    setSearchedClub,
    setActiveView,
    setLocationType,
}) => {
    const { actions } = useContext(GlobalContext);
    const { user } = useContext(AuthContext);
    const insets = useSafeAreaInsets();
    const [dateRangeModal, setDateRangeModal] = useState<boolean>(false);
    const [showSearchResult, setShowSearchResult] = useState<boolean>(false);
    const [region, setRegion] = useState<{ lat: number; lng: number } | null>(null);
    const [club, setClub] = useState<MapClub | null>(null);
    const [date, setDate] = useState<DateRange>({
        startDate: '',
        endDate: '',
    });

    useEffect(() => {
        if (dateRange.startDate || dateRange.endDate) {
            setDate({
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
            });
        }
    }, [dateRange]);

    //Function used for get location
    const getLocationDetails = (placeId: string, type: string) => {
        fetch(`${placeDetails}${placeId}`)
            .then((data) =>
                data.json().then(({ result }) => {
                    if (result?.geometry) {
                        setLocationType(type);
                        setRegion(result?.geometry?.location);
                    }
                }),
            )
            .catch((error) => {
                console.log('error', error);
            });
    };

    const handleClubPress = (id: number, color: string) => {
        const body = {
            userId: user?.id,
            clubId: id,
            clubColor: color,
        };
        fetcher({
            endpoint: CLUB_DETAILS_MAP,
            method: 'POST',
            body,
        })
            .then((data) => {
                const { lat, lng } = data?.clubs;
                if (lat && lng) {
                    setRegion({ lat: lat - 0.03, lng: lng });
                }
            })
            .catch(console.log);
    };

    //function used for searched data pressed
    const onItemPress = (name: string, data: any) => {
        actions.setIsMapSearchActive(false);
        setShowSearchResult(false);
        if (name === 'club') {
            setSearchType('club');
            setClub(data);
            setSearchTerm(data?.properties?.name);
            handleClubPress(data.id, data.properties.color);
        } else {
            setSearchType('location');
            setSearchTerm(data?.description);
            getLocationDetails(
                data?.place_id,
                data?.types?.includes('locality')
                    ? 'city'
                    : data?.types?.includes('administrative_area_level_1')
                    ? 'state'
                    : 'country',
            );
        }
        Keyboard.dismiss();
    };

    const handleSearch = () => {
        setActiveView('MAP');
        onClose();
        region && setSelectedRegion(region);
        setSelectedClub(club);
        setSearchedClub(club);
        setShowSearchResult(false);
        setDateRange(date);
    };

    return (
        <View style={styles.container}>
            <View style={styles.backdrop} />
            <View style={{ backgroundColor: colors.whiteRGB }}>
                <View
                    style={[
                        styles.header,
                        { paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + Spacing.SCALE_30 },
                    ]}>
                    <TouchableOpacity onPress={onClose} style={styles.backButton}>
                        <CrossIcon />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Search by</Text>
                </View>
                <View>
                    <TGSearchNew
                        searchState={[searchTerm, setSearchTerm]}
                        setShowSearchResult={setShowSearchResult}
                        placeholder={SEARCH_OFFERS_BY_LOCATION_CLUB_AND_DATE}
                        iconStyle={{
                            marginRight: Spacing.SCALE_12,
                        }}
                    />
                    {/* Search result UI and listing */}
                    {showSearchResult && searchTerm?.length > 0 ? (
                        <View style={styles.searchResultContainer}>
                            <View>
                                {searchedClubList && searchedClubList?.length > 0 && (
                                    <>
                                        <Text style={styles.title}>Clubs</Text>
                                        {searchedClubList.slice(0, 5).map((club: any) => (
                                            <TouchableOpacity onPress={() => onItemPress('club', club)}>
                                                <Text style={styles.result}>{club?.properties?.name}</Text>
                                            </TouchableOpacity>
                                        ))}
                                    </>
                                )}
                                {locations?.length > 0 && (
                                    <>
                                        <Text
                                            style={[
                                                styles.title,
                                                searchedClubList?.length > 0
                                                    ? { borderTopLeftRadius: 0, borderTopRightRadius: 0 }
                                                    : {
                                                          borderTopLeftRadius: Spacing.SCALE_10,
                                                          borderTopRightRadius: Spacing.SCALE_10,
                                                      },
                                            ]}>
                                            Locations
                                        </Text>
                                        {locations.slice(0, 5).map((location: any) => (
                                            <TouchableOpacity onPress={() => onItemPress('location', location)}>
                                                <Text style={styles.result}>{location?.description}</Text>
                                            </TouchableOpacity>
                                        ))}
                                    </>
                                )}
                                {locations?.length === 0 && searchedClubList?.length === 0 && searchTerm?.length > 0 ? (
                                    <Text style={styles.noResultText}>No Result Found</Text>
                                ) : null}
                            </View>
                        </View>
                    ) : null}
                </View>
                <CommonDateField setDateRangeModal={setDateRangeModal} dateRange={date} setDateRange={setDate} />
                <TealButtonNew
                    text="Search"
                    onPress={handleSearch}
                    btnStyle={styles.button}
                    textStyle={styles.buttonText}
                />
            </View>

            {dateRangeModal && (
                <View style={styles.dateRangeModalContainer}>
                    <CommonDateRangeModal
                        visible={dateRangeModal}
                        onClose={() => setDateRangeModal(false)}
                        onSave={(dates: any) => {
                            setDate(dates);
                        }}
                        allowRangeSelection={true}
                        modalStyle={{
                            width: Size.SIZE_340,
                            marginTop: Spacing.SCALE_30,
                        }}
                        initialStartDate={date.startDate ? new Date(date.startDate) : undefined}
                        initialEndDate={date.endDate ? new Date(date.endDate) : undefined}
                    />
                </View>
            )}
        </View>
    );
};

export default CustomSearchModal;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_4,
        columnGap: Spacing.SCALE_10,
    },
    backButton: {
        height: Size.SIZE_40,
        width: Size.SIZE_40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        color: colors.lightBlack,
    },
    content: {
        flex: 1,
    },
    title: {
        backgroundColor: colors.lightgray,
        color: colors.darkgray,
        fontFamily: 'Ubuntu-Regular',
        paddingHorizontal: Spacing.SCALE_15,
        paddingVertical: Spacing.SCALE_7,
        fontSize: Size.SIZE_12,
        borderTopLeftRadius: Spacing.SCALE_10,
        borderTopRightRadius: Spacing.SCALE_10,
    },
    result: {
        backgroundColor: 'white',
        fontFamily: 'Ubuntu-Regular',
        paddingHorizontal: Spacing.SCALE_15,
        paddingVertical: Spacing.SCALE_8,
        color: colors.lightBlack,
        fontSize: Size.SIZE_12,
    },
    searchResultContainer: {
        backgroundColor: colors.whiteRGB,
        marginTop: Spacing.SCALE_50,
        width: Size.SIZE_340,
        borderRadius: Spacing.SCALE_10,
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: colors.greyRgba,
        position: 'absolute',
        zIndex: 100,
        alignSelf: 'center',
    },
    noResultText: {
        backgroundColor: colors.lightgray,
        color: colors.darkgray,
        fontFamily: 'Ubuntu-Regular',
        paddingHorizontal: Spacing.SCALE_15,
        paddingVertical: Spacing.SCALE_10,
        textAlign: 'center',
        width: '100%',
    },
    dateRangeContainer: {
        height: Size.SIZE_40,
        width: Size.SIZE_340,
        backgroundColor: colors.whiteRGB,
        borderWidth: Size.SIZE_1,
        alignSelf: 'center',
        borderRadius: Size.SIZE_10,
        borderColor: colors.greyRgba,
        marginTop: Spacing.SCALE_6,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_10,
    },
    dateRangeText: {
        fontSize: Size.SIZE_12,
        fontWeight: '400',
        color: colors.darkgray,
        fontFamily: 'Ubuntu-Regular',
    },
    dateRangeModalContainer: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        paddingHorizontal: 30,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 100,
    },
    backdrop: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    button: {
        marginHorizontal: Spacing.SCALE_16,
        marginTop: Spacing.SCALE_16,
        width: Size.SIZE_340,
        alignSelf: 'center',
        marginBottom: Spacing.SCALE_15,
    },
    buttonText: {
        fontSize: Size.SIZE_14,
        fontWeight: '400',
        color: colors.whiteRGB,
        lineHeight: Size.SIZE_18,
    },
});
