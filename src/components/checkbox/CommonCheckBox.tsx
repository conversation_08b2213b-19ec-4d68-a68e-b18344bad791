import { Text, TouchableOpacity, View } from 'react-native';
import { colors } from '../../theme/theme';
//@ts-ignore
import CheckIcon from '../../assets/images/check.svg';
//checkbox component
const CommonCheckBox = ({ checked, disabled = false, label = '', onChecked = () => {}, checkBoxWarperStyle = {} }: any) => {
    return (
        <TouchableOpacity
            onPress={() => !disabled && onChecked(!checked)}
            style={{ width: '100%', flexDirection: 'row' }}>
            <View
                style={{
                    height: 16,
                    width: 16,
                    borderRadius: 5,
                    backgroundColor: checked && !disabled ? colors.darkteal : colors.whiteRGB,
                    justifyContent: 'center',
                    alignItems: 'center',
                    ...checkBoxWarperStyle,
                }}>
                {checked && <CheckIcon height={10} width={10} />}
            </View>
            <Text
                style={{
                    paddingLeft: 10,
                    fontFamily: 'Ubuntu-Regular',
                    color: colors.dark_charcoal,
                    fontSize: 15,
                }}>
                {label}
            </Text>
        </TouchableOpacity>
    );
};
export default CommonCheckBox;