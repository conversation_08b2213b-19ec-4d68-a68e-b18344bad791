import { Image, SafeAreaView, StyleSheet, Text, TouchableOpacity, View, StatusBar } from 'react-native';
import React, { useContext } from 'react';
import { useNavigation } from '@react-navigation/native';

import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { AuthContext } from '../../context/AuthContext';
import { colors } from '../../theme/theme';

interface HeaderProps {
    headerText?: string;
}

const TGHeaderNew: React.FC<HeaderProps> = () => {
    const navigation = useNavigation();
    const { user } = useContext(AuthContext);
    return (
        <>
            <StatusBar barStyle="dark-content" backgroundColor={colors.whiteRGB} />
            <SafeAreaView style={styles.safeAreaViewStyle}>
                <View style={styles.container}>
                    <TouchableOpacity
                        onPress={() => {
                            navigation.navigate('Settings');
                        }}
                        style={{ flexDirection: 'row', alignItems: 'center', columnGap: Spacing.SCALE_12 }}>
                        <View style={styles.profileIcon}>
                            {user?.profilePhoto ? (
                                <Image source={{ uri: user?.profilePhoto }} style={styles.profileIcon} />
                            ) : (
                                <Text
                                    style={{
                                        fontSize: Typography.FONT_SIZE_18,
                                        color: colors.whiteRGB,
                                        fontFamily: 'Ubuntu-Medium',
                                    }}>
                                    {user?.full_name[0]}
                                </Text>
                            )}
                        </View>
                        <View>
                            <Text style={styles.welcomeBackStyle}>Welcome back!</Text>
                            <Text style={styles.text}>{user?.full_name}</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        </>
    );
};

export default TGHeaderNew;

const styles = StyleSheet.create({
    safeAreaViewStyle: {
        backgroundColor: colors.whiteRGB,
    },
    container: {
        backgroundColor: colors.whiteRGB,
        padding: Spacing.SCALE_16,
    },
    welcomeBackStyle: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.greyRgb,
        lineHeight: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
    },
    text: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.dark_charcoal,
        lineHeight: Typography.FONT_SIZE_18,
        fontFamily: 'Ubuntu-Medium',
    },
    profileIcon: {
        width: Size.SIZE_34,
        height: Size.SIZE_34,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.tealRgb,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
