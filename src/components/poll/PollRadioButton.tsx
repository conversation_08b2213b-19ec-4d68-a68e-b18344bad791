// Package imports
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import React, { useContext, useEffect, useLayoutEffect, useState } from 'react';
import FastImage from 'react-native-fast-image';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

// SVG imports
import TealCheck from '../../assets/svg/tealCheck.svg';
import WhiteCheck from '../../assets/svg/whiteCheck.svg';
// Context imports
import { AuthContext } from '../../context/AuthContext';
import { StreamChatContext } from '../../context/StreamChatContext';
// Size imports and colors imports
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';

interface PollOption {
    text: string;
    id: number;
}

interface PollRadioOptionProps {
    options: PollOption;
    key: string;
    alignment: string;
    poll: any;
    messageData: any;
    votes: any;
}

const PollRadioButton: React.FC<PollRadioOptionProps> = ({
    options,
    alignment,
    poll: messagePoll,
    messageData,
    votes,
}) => {
    const { user } = useContext(AuthContext);
    const { client, channel } = useContext(StreamChatContext);
    const [pollOptedUserId, setPollOptedUserId] = useState<string[]>([]);
    const [poll, setPoll] = useState(messagePoll);
    const progressWidth = useSharedValue(0);

    useLayoutEffect(() => {
        let percentage =
            (poll?.vote_counts_by_option[options?.id] * 100) / Object.keys(channel?.state?.members)?.length || 0;
        progressWidth.value = withTiming(percentage > 100 ? 100 : percentage, {
            duration: 500,
        });
    }, [poll?.vote_counts_by_option[options?.id]]);

    const animatedStyle = useAnimatedStyle(() => ({
        width: `${progressWidth.value}%`,
    }));

    useEffect(() => {
        if (!client) return;

        const handlePollEvents = async (event: any) => {
            // Only handle events for this specific poll
            if (event.poll?.id !== poll?.id) return;

            switch (event.type) {
                case 'poll.created':
                case 'poll.updated':
                case 'poll.closed':
                case 'poll.deleted':
                case 'poll.vote_casted':
                case 'poll.vote_removed':
                case 'poll.vote_changed':
                    const poll = await client.getPoll(event?.poll?.id, user?.id);
                    setPoll(poll?.poll);

                    break;
                default:
                    break;
            }
        };

        // Listen for specific poll events instead of 'all'
        const events = [
            'poll.created',
            'poll.updated',
            'poll.closed',
            'poll.deleted',
            'poll.vote_casted',
            'poll.vote_removed',
            'poll.vote_changed',
        ];

        events.forEach((eventType) => {
            client.on(eventType, handlePollEvents);
        });

        return () => {
            events.forEach((eventType) => {
                client.off(eventType, handlePollEvents);
            });
        };
    }, [client, poll?.id]);

    useLayoutEffect(() => {
        if (poll) {
            const optionVotes = votes.filter((vote: any) => vote.option_id === options.id);
            setPollOptedUserId(optionVotes.map((data: any) => data?.user_id));
        }
    }, [poll, votes, options.id]);

    const handleVote = async (messageId: string, pollId: string, optionId: number) => {
        try {
            if (pollOptedUserId?.includes(user?.id)) {
                const myVoteId = votes?.filter(
                    (voter: any) => voter?.user_id === user?.id && voter?.option_id === optionId,
                )[0]?.id;
                await client.removePollVote(messageId, pollId, myVoteId);
            } else {
                const pollInstance = client.polls.fromState(pollId);
                await pollInstance.castVote(optionId, messageId);
            }
        } catch (error) {
            console.log('error=>', error);
        }
    };

    const checkType = (alignment: string) => {
        return alignment === 'left' ? <WhiteCheck /> : <TealCheck />;
    };
    return (
        <View style={{ marginBottom: Spacing.SCALE_20 }}>
            <TouchableOpacity
                style={styles.container}
                onPress={() => {
                    if (poll?.is_closed) return;
                    handleVote(messageData?.id, poll?.id, options?.id);
                }}>
                {!poll?.is_closed ? (
                    <View
                        style={[
                            styles.radioButton,
                            {
                                backgroundColor:
                                    alignment === 'left'
                                        ? pollOptedUserId?.includes(user?.id)
                                            ? colors.tealRgb
                                            : colors.whiteRGB
                                        : pollOptedUserId?.includes(user?.id)
                                        ? colors.whiteRGB
                                        : colors.tealRgb,
                                borderColor:
                                    alignment === 'left'
                                        ? pollOptedUserId?.includes(user?.id)
                                            ? colors.tealRgb
                                            : colors.greyCheckBox
                                        : pollOptedUserId?.includes(user?.id)
                                        ? colors.whiteRGB
                                        : colors.greyCheckBox,
                            },
                        ]}>
                        {pollOptedUserId?.includes(user?.id) ? checkType(alignment) : null}
                    </View>
                ) : (
                    <View style={[styles.radioButton, { borderWidth: 0 }]} />
                )}
                <View
                    style={[styles.radioButtonContainer, { width: alignment === 'left' ? '75%' : '80%' }]}
                    key={options?.id.toString()}>
                    <Text
                        style={[
                            styles.textStyle,
                            {
                                color: alignment === 'left' ? colors.dark_charcoal : colors.whiteRGB,
                                width: alignment === 'left' ? Size.SIZE_150 : Size.SIZE_183,
                            },
                        ]}>
                        {options?.text}
                    </Text>
                    <View
                        style={[
                            styles.circleImageContainer,
                            { right: alignment === 'left' ? Spacing.SCALE_20 : Spacing.SCALE_4 },
                        ]}>
                        {poll?.latest_votes_by_option[options?.id]?.slice(0, 2)?.map((data: any, index: any) => {
                            return (
                                <View
                                    key={index}
                                    style={[
                                        styles.imageWrapper,
                                        {
                                            zIndex: index,
                                            left:
                                                alignment === 'left'
                                                    ? poll?.latest_votes_by_option[options?.id]?.length === 1
                                                        ? Spacing.SCALE_15
                                                        : index * 16.5
                                                    : poll?.latest_votes_by_option[options?.id]?.length === 1
                                                    ? Spacing.SCALE_15
                                                    : index * 16.5,
                                        },
                                    ]}>
                                    <View style={styles.initialsWrapper}>
                                        {data?.user?.image ? (
                                            <FastImage source={{ uri: data?.user?.image }} style={styles.imageStyle1} />
                                        ) : (
                                            <Text style={styles.initialsText}>{data?.user?.name[0]}</Text>
                                        )}
                                    </View>
                                </View>
                            );
                        })}
                        {poll?.vote_counts_by_option[options?.id] && (
                            <Text
                                style={[
                                    styles.text,
                                    {
                                        color: alignment === 'left' ? colors.dark_charcoal : colors.whiteRGB,
                                        left: alignment === 'left' ? Spacing.SCALE_40 : Spacing.SCALE_37,
                                    },
                                ]}>
                                {poll?.vote_counts_by_option[options?.id]}
                            </Text>
                        )}
                    </View>
                </View>
            </TouchableOpacity>
            {/* Bottom ProgressBar */}
            <View style={{ width: alignment === 'left' ? '92%' : '100%', marginTop: Spacing.SCALE_8 }}>
                <View
                    style={{
                        height: Size.SIZE_4,
                        backgroundColor: alignment === 'left' ? colors.greyCheckBox : 'rgb(84, 193, 197)',
                        marginLeft: Spacing.SCALE_25,
                        borderRadius: Size.SIZE_20,
                    }}>
                    <Animated.View
                        style={[
                            styles.progressBar,
                            animatedStyle,
                            { backgroundColor: alignment === 'left' ? colors.tealRgb : colors.white },
                        ]}></Animated.View>
                </View>
            </View>
        </View>
    );
};

export default PollRadioButton;

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
    },
    radioButtonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    radioButton: {
        height: Size.SIZE_16,
        width: Size.SIZE_16,
        borderRadius: Size.SIZE_40,
        borderWidth: Size.SIZE_1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    textStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_16,
    },
    circleImageContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        position: 'absolute',
    },
    initialsWrapper: {
        width: Size.SIZE_20,
        height: Size.SIZE_20,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.greyRgba,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: colors.shadowColor,
        shadowOffset: { width: -1, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 1,
    },
    initialsText: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        color: colors.tealRgb,
        fontWeight: '500',
        textTransform: 'capitalize',
    },
    imageStyle1: {
        width: Size.SIZE_20,
        height: Size.SIZE_20,
        borderRadius: Size.SIZE_50,
    },
    imageWrapper: {
        width: Size.SIZE_20,
        height: Size.SIZE_20,
        position: 'absolute',
        borderRadius: Size.SIZE_50,
        bottom: 0.1,
    },
    text: {
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_14,
        bottom: Spacing.SCALE_1,
    },
    progressBar: {
        height: '100%',
        borderRadius: 5,
        position: 'relative',
    },
});
