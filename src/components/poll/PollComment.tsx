import {
    Keyboard,
    KeyboardAvoidingView,
    Platform,
    Pressable,
    StyleSheet,
    Text,
    TextInput,
    TouchableWithoutFeedback,
    View,
} from 'react-native';
import React, { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import StreamChatButton from '../buttons/StreamChatButton';

const PollComment = ({ route }: any) => {
    const { callBack } = route?.params;
    const navigation = useNavigation();
    const [comment, setComment] = useState<string>(route?.params?.comment);

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.container}>
                <Pressable style={styles.popupContainer} onPress={() => navigation.pop(1)}>
                    <KeyboardAvoidingView
                        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                        style={styles.popupWrapper}>
                        <Text style={styles.commentText}>Add Comment</Text>
                        <Text style={styles.commentPlaceHolderText}>Enter your Comments</Text>
                        <TextInput
                            value={comment}
                            onChangeText={(text) => setComment(text.trimStart())}
                            placeholder="Enter your comment"
                            placeholderTextColor={colors.greyShade}
                            multiline={true}
                            style={[styles.input, { textAlignVertical: 'top' }]}
                            maxLength={300}
                        />
                        <Text
                            style={{ fontSize: Typography.FONT_SIZE_12, color: colors.greyShade, textAlign: 'right' }}>
                            {comment?.length}/300
                        </Text>
                        <View style={styles.buttonContainer}>
                            <StreamChatButton
                                label="Cancel"
                                isLoading={false}
                                onPress={() => navigation.pop(1)}
                                buttonContainer={{ width: '45%' }}
                                btn={{
                                    borderWidth: 1,
                                    borderColor: colors.dustyGrey,
                                    backgroundColor: colors.whiteRGB,
                                }}
                                btnText={{ color: colors.dark_charcoal }}
                            />
                            <StreamChatButton
                                label="Done"
                                isLoading={false}
                                onPress={() => {
                                    callBack(comment);
                                    setComment('');
                                }}
                                buttonContainer={{ width: '45%' }}
                                btnDissable={!comment}
                            />
                        </View>
                    </KeyboardAvoidingView>
                </Pressable>
            </View>
        </TouchableWithoutFeedback>
    );
};

export default PollComment;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    popupContainer: {
        flex: 1,
        backgroundColor: colors.transparentRgba,
        zIndex: 1,
    },
    popupWrapper: {
        width: '100%',
        backgroundColor: colors.white,
        borderRadius: Size.SIZE_8,
        position: 'absolute',
        zIndex: 100,
        padding: Spacing.SCALE_17,
        minHeight: 356,
        bottom: 0,
    },
    commentText: {
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
        textAlign: 'center',
    },
    commentPlaceHolderText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        color: colors.greyShade,
        marginTop: Spacing.SCALE_20,
        lineHeight: Size.SIZE_16,
    },
    input: {
        height: 150,
        borderWidth: 1,
        borderColor: 'rgba(238, 238, 238, 1)',
        borderRadius: Size.SIZE_8,
        marginTop: Spacing.SCALE_10,
        padding: Spacing.SCALE_10,
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_12,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: Spacing.SCALE_16,
    },
});
