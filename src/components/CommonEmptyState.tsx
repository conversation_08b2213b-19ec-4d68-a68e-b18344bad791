import { StyleProp, StyleSheet, Text, View, ViewStyle } from 'react-native';
import React from 'react';

// Assets, Constants, Theme, Utils imports
import { RequestEmptyState } from '../assets/svg';
import { Typography } from '../utils/responsiveUI';
import { Size } from '../utils/responsiveUI';
import { colors } from '../theme/theme';

const CommonEmptyState = ({ wrapperStyle, text }: { wrapperStyle?: StyleProp<ViewStyle>; text: string }) => {
    return (
        <View style={[styles.emptyStateContainer, wrapperStyle]}>
            <RequestEmptyState />
            <Text style={styles.emptyStateText}>{text}</Text>
        </View>
    );
};

export default CommonEmptyState;

const styles = StyleSheet.create({
    emptyStateContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: Size.SIZE_20,
    },
    emptyStateText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
        lineHeight: Size.SIZE_21,
        marginTop: Size.SIZE_24,
    },
});
