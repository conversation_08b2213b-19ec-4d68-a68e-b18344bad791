import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';

//utils, assets and theme imports
import { Size, Spacing } from '../utils/responsiveUI';
import { handleTimeFormat } from './timeFormatComponent/handleTimeFormat';
import { CrossIcon, CrossIconGrey, DatePickerSvgIcon } from '../assets/svg';
import { colors } from '../theme/theme';

//types
interface DateRange {
    startDate: string;
    endDate: string;
}

interface CommonDateFieldProps {
    setDateRangeModal: (dateRangeModal: boolean) => void;
    dateRange: DateRange;
    setDateRange: (dateRange: DateRange) => void;
}

const CommonDateField = ({ setDateRangeModal, dateRange, setDateRange }: CommonDateFieldProps) => {
    return (
        <TouchableOpacity style={styles.dateRangeContainer} onPress={() => setDateRangeModal(true)}>
            <View style={{ flexDirection: 'row', alignItems: 'center', columnGap: Spacing.SCALE_12 }}>
                <DatePickerSvgIcon style={{ marginLeft: Spacing.SCALE_1 }} />
                <Text
                    style={[
                        styles.dateRangeText,
                        { color: dateRange?.startDate || dateRange?.endDate ? colors.lightBlack : colors.darkgray },
                    ]}>
                    {dateRange?.startDate && dateRange?.endDate
                        ? `${handleTimeFormat(dateRange.startDate)} - ${handleTimeFormat(dateRange.endDate)}`
                        : dateRange?.startDate
                        ? handleTimeFormat(dateRange.startDate)
                        : dateRange?.endDate
                        ? handleTimeFormat(dateRange.endDate)
                        : 'Date Range'}
                </Text>
            </View>
            {(dateRange?.startDate || dateRange?.endDate) && (
                <TouchableOpacity
                    style={styles.crossIconContainer}
                    onPress={() => setDateRange({ startDate: '', endDate: '' })}>
                    <CrossIcon height={Size.SIZE_20} width={Size.SIZE_20} />
                </TouchableOpacity>
            )}
        </TouchableOpacity>
    );
};

export default CommonDateField;

const styles = StyleSheet.create({
    dateRangeContainer: {
        height: Size.SIZE_40,
        width: Size.SIZE_340,
        backgroundColor: colors.whiteRGB,
        borderWidth: Size.SIZE_1,
        alignSelf: 'center',
        borderRadius: Size.SIZE_10,
        borderColor: colors.greyRgba,
        marginTop: Spacing.SCALE_6,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_10,
    },
    dateRangeText: {
        fontSize: Size.SIZE_12,
        fontWeight: '400',
        color: colors.darkgray,
        fontFamily: 'Ubuntu-Regular',
    },
    crossIconContainer: {
        height: Size.SIZE_36,
        width: Size.SIZE_36,
        alignItems: 'flex-end',
        justifyContent: 'center',
    },
});
