import React, { useContext, useEffect, useState } from 'react';
import { FlatList, StyleSheet, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import LinearGradient from 'react-native-linear-gradient';

import TealButtonNew from '../buttons/TealButtonNew';
import { MutedIconNew, TealMapIcon } from '../../assets/svg/index';
import { MUTED_CLUB, MUTED_CLUB_DESCRIPTION } from '../../utils/constants/strings';
import { fetcher } from '../../service/fetcher';
import { AuthContext } from '../../context/AuthContext';
import { DISMISS_MUTE_CLUB_POPUP, muteClubs } from '../../service/EndPoint';
import { ClubDetails, UserClubRenderItemProps } from '../../interface';
import ToggleButton from '../buttons/ToggleButton';
import { GlobalContext } from '../../context/contextApi';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';

const MuteClubPopupScreen: React.FC = () => {
    const { user } = useContext(AuthContext);
    const { actions, state } = useContext(GlobalContext);
    const { appLoader } = state;
    const { setAppLoader } = actions;
    const navigation = useNavigation<NativeStackNavigationProp<any>>();
    const [isDismissChecked, setIsDismissChecked] = useState(false);
    const [mutedClubs, setMutedClubs] = useState<ClubDetails[]>([]);

    useEffect(() => {
        if (user?.clubs) {
            setMutedClubs(user?.clubs.filter((club: ClubDetails) => club.muted));
        }
    }, [user]);

    const handleDismissPopup = async () => {
        await fetcher({
            endpoint: DISMISS_MUTE_CLUB_POPUP,
            method: 'POST',
            body: {
                userId: user?.id,
                showClubMutedPopup: !isDismissChecked,
            },
        });
    };

    const handleUnmute = async () => {
        setAppLoader(true);
        fetcher({
            endpoint: muteClubs,
            method: 'POST',
            body: {
                user_id: user?.id,
                muted: false,
                mute_until: null,
                club_ids: mutedClubs.filter((club) => !club.muted).map((club) => club.club.id),
            },
        })
            .then((res) => {
                if (res.status) {
                    setTimeout(() => {
                        setAppLoader(false);
                        navigation.goBack();
                    }, 1000);
                }
            })
            .catch((err) => {
                console.warn('error', err);
            });
    };

    const handleDismiss = async () => {
        await handleDismissPopup();
        navigation.goBack();
    };

    const RenderItem: React.FC<UserClubRenderItemProps> = ({ item }) => {
        const handleToggleButton = () => {
            setMutedClubs(
                mutedClubs.map((club) => (club.club.id === item.club.id ? { ...club, muted: !club.muted } : club)),
            );
        };

        return (
            <>
                <View style={styles.row}>
                    <View style={styles.rowContent}>
                        <TealMapIcon />
                        <Text style={styles.itemTitleStyle}>{item.club.name}</Text>
                    </View>
                    <View style={styles.toggleWrapper}>
                        <ToggleButton onPress={handleToggleButton} isToggled={item.muted} disabled={false} />
                    </View>
                </View>
                <View style={styles.divider} />
            </>
        );
    };

    const ClubSimmer = () => {
        return (
            <FlatList
                data={mutedClubs}
                renderItem={({ item }) => (
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                columnGap: Spacing.SCALE_8,
                            }}>
                            <ShimmerPlaceholder
                                LinearGradient={LinearGradient}
                                style={[
                                    styles.row,
                                    {
                                        paddingHorizontal: 0,
                                        borderRadius: 10,
                                        width: '10%',
                                        marginVertical: Spacing.SCALE_6,
                                        height: Size.SIZE_32,
                                    },
                                ]}
                            />
                            <ShimmerPlaceholder
                                LinearGradient={LinearGradient}
                                style={{
                                    paddingHorizontal: 0,
                                    borderRadius: 10,
                                    width: '70%',
                                    marginVertical: Spacing.SCALE_6,
                                    height: Size.SIZE_32,
                                }}
                            />
                        </View>
                        <ShimmerPlaceholder
                            LinearGradient={LinearGradient}
                            style={[
                                styles.row,
                                {
                                    paddingHorizontal: 0,
                                    borderRadius: 10,
                                    width: '10%',
                                    alignSelf: 'center',
                                    marginVertical: Spacing.SCALE_6,
                                    height: Size.SIZE_32,
                                },
                            ]}
                        />
                    </View>
                )}
            />
        );
    };

    return (
        <View style={{ flex: 1, backgroundColor: colors.transparentRgba }}>
            <View style={styles.container}>
                <View style={styles.centerElements}>
                    <View style={styles.iconWrapper}>
                        <MutedIconNew width={Size.SIZE_45} height={Size.SIZE_45} />
                    </View>
                    <Text style={styles.header}>{MUTED_CLUB}</Text>
                    <View style={styles.bodyTextWrapper}>
                        <Text style={styles.body}>{MUTED_CLUB_DESCRIPTION}</Text>
                    </View>
                </View>

                <View style={styles.checkBoxWrapper}>
                    {appLoader ? (
                        <ClubSimmer />
                    ) : (
                        <FlatList data={mutedClubs} renderItem={({ item }) => <RenderItem item={item} />} />
                    )}
                </View>

                <View style={styles.btnWrapper}>
                    <TealButtonNew
                        text="Dismiss"
                        btnStyle={[styles.btnStyle, { backgroundColor: colors.greyRgba }]}
                        textStyle={styles.dismissTextStyle}
                        onPress={handleDismiss}
                        disabled={false}
                        loading={false}
                        disabledStyle={false}
                    />
                    <TealButtonNew
                        text="Done"
                        btnStyle={[styles.btnStyle, { backgroundColor: colors.tealRgb }]}
                        textStyle={styles.unmuteTextStyle}
                        onPress={handleUnmute}
                        disabled={false}
                        loading={appLoader}
                        disabledStyle={false}
                    />
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.whiteRGB,
        borderTopLeftRadius: Size.SIZE_8,
        borderTopRightRadius: Size.SIZE_8,
        width: '100%',
        position: 'absolute',
        bottom: 0,
        paddingVertical: Spacing.SCALE_24,
    },
    centerElements: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: Spacing.SCALE_18,
    },
    iconWrapper: {
        width: Size.SIZE_70,
        height: Size.SIZE_70,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.greyVariant1,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: Spacing.SCALE_16,
    },
    header: {
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_28,
        fontSize: Typography.FONT_SIZE_20,
        fontWeight: '500',
        marginBottom: Spacing.SCALE_12,
    },
    body: {
        color: colors.darkGreyRgba,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        textAlign: 'center',
    },
    bodyTextWrapper: {
        width: '100%',
        paddingHorizontal: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_24,
    },
    checkBoxWrapper: {
        alignSelf: 'center',
        height: Size.SIZE_200,
    },
    checkBoxStyle: {
        height: Size.SIZE_16,
        width: Size.SIZE_16,
        borderColor: colors.tealRgb,
        borderRadius: 2,
        borderWidth: 1,
    },
    btnWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_20,
        backgroundColor: colors.whiteRGB,
    },
    btnStyle: {
        borderRadius: Size.SIZE_8,
        alignSelf: 'center',
        width: '45%',
        height: Size.SIZE_54,
    },
    dismissTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
    },
    unmuteTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.whiteRGB,
        fontFamily: 'Ubuntu-Medium',
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_18,
    },
    rowContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    itemTitleStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
        marginLeft: Spacing.SCALE_8,
        width: Size.SIZE_250,
    },
    divider: {
        backgroundColor: colors.greyRgba,
        height: 1,
        marginTop: Spacing.SCALE_12,
        width: '100%',
        alignSelf: 'center',
    },
    toggleWrapper: {
        marginRight: 10,
    },
});

export default MuteClubPopupScreen;
