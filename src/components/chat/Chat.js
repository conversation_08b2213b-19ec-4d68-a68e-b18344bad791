// import React, { useContext, useEffect, useState, useRef } from 'react';
// import {
//     View,
//     ScrollView,
//     SafeAreaView,
//     TextInput,
//     TouchableOpacity,
//     Keyboard,
//     ActivityIndicator,
//     FlatList,
// } from 'react-native';
// import SendBird from 'sendbird';
// import { AuthContext } from '../../context/AuthContext';
// import { INSERT_REQUEST_CHAT, UPDATE_REQUEST_CHAT } from '../../graphql/mutations/chat';
// import { CHAT_REQUEST } from '../../graphql/queries/requests';
// import useClient from '../../hooks/useClient';
// import { colors } from '../../theme/theme';
// import SendIcon from '../../assets/images/send.svg';
// import ChatMessage from './ChatMessage';
// import { ChatOpenContext } from '../layout/notifications/NotificationsContainer';
// import { createRequestChatChannelUrl, newChatNotification } from '../../service/EndPoint';
// import { sendNotification } from '../../utils/notification/createNotification';
// import TGText from '../fields/TGText';

// const sendbird = SendBird.getInstance();

// export default function Chat({
//     request,
//     selectedChatUser,
// }) {
//     const messageRef = useRef()
//     const { user } = useContext(AuthContext);
//     const setChatOpen = useContext(ChatOpenContext);
//     const client = useClient();
//     const [connected, setConnected] = useState(false);
//     const [channelURL, setChannelURL] = useState();
//     const [channel, setChannel] = useState();
//     const [message, setMessage] = useState();
//     const [isLoading, setLoading] = useState(true);
//     const [chatMessages, setChatMessages] = useState();
//     const [incomingMessage, setIncomingMessage] = useState();

//     //Connected effect
//     useEffect(() => {
//         if (selectedChatUser?.sendbird_channel_id) {
//             setChannelURL(selectedChatUser?.sendbird_channel_id);
//         } else {
//             getChatRequest()
//         }
//     }, []);

//     async function createChat(request) {
//         const { channel_url } = await fetch(`${createRequestChatChannelUrl}`, {
//             method: 'POST',
//             headers: { 'Content-Type': 'application/json' },
//             body: JSON.stringify({
//                 request_id: request.id,
//                 host_id: user.id,
//                 requestor_id: request.user.id,
//                 channel_name: `Request #${request.game_id} - ${request.user.first_name} ${request.user.last_name} and ${user.first_name} ${user.last_name}`,
//             }),
//         }).then((data) => data.json());
//         if (channel_url) {
//             setChannelURL(channel_url);
//             client.request(INSERT_REQUEST_CHAT, {
//                 chat: {
//                     request_id: request.id,
//                     sendbird_channel_id: channel_url,
//                     club_member_id: user.id,
//                 },
//             });
//         }
//     }

//     // console.log({ channel, channelURL });
//     // console.log('chatMessages', chatMessages);

//     async function getChatRequest() {
//         if (request.chats.length > 0) {
//             setChannelURL(request.chats[0]?.sendbird_channel_id);
//         } else {
//             createChat(request);
//         }
//     }

//     // useEffect(() => {
//     //     return () => sendbird.disconnect();
//     // }, []);

//     //New Channel Effect
//     // useEffect(() => {
//     //     if (channelURL) {
//     //         setChatOpen({ channelURL });
//     //         sendbird.GroupChannel.getChannel(
//     //             channelURL,
//     //             function (groupChannel, error) {
//     //                 if (error) {
//     //                     setLoading(false)
//     //                     return;
//     //                 }
//     //                 groupChannel.markAsRead();

//     //                 console.log('groupChannel', groupChannel);

//     //                 setChannel(groupChannel);

//     //                 // There should only be one single instance per channel view.
//     //                 const prevMessageListQuery = groupChannel.createPreviousMessageListQuery();

//     //                 // Retrieving previous messages.
//     //                 prevMessageListQuery.load(100, function (messages, error) {
//     //                     if (error) {
//     //                         setLoading(false)
//     //                         return;
//     //                     }
//     //                     //   console.log('messages', messages?.length);
//     //                     setChatMessages(messages);
//     //                     setLoading(false)
//     //                 });
//     //             },
//     //         );

//     //         const channelHandler = new sendbird.ChannelHandler();
//     //         channelHandler.onMessageReceived = function (channel, message) {
//     //             setIncomingMessage(message);
//     //         };
//     //         sendbird.addChannelHandler(channelURL, channelHandler);
//     //     }
//     // }, [channelURL]);

//     async function sendMessage() {
//         const params = new sendbird.UserMessageParams();

//         //   console.log('selectedChatUser id', selectedChatUser.id);

//         params.message = message;
//         params.mentionType = 'users'; // Either 'users' or 'channel'
//         params.has_messages = true; // Either 'true' or 'false'

//         channel.sendUserMessage(params, function (message, error) {
//             if (error) {
//                 return;
//             }
//             const requestID = client.request(UPDATE_REQUEST_CHAT, {
//                 club_member_id: user.id,
//                 request_id: request.id
//             });

//             setChatMessages([...chatMessages, message]);
//             setMessage('');
//             // messageRef.current.scrollToEnd({ animating: true });
//             const params = { request, user, channelURL, host_id: selectedChatUser?.id }

//             sendNotification(newChatNotification, params).then(res => {
//                 //  console.log('res->', res.status);
//             })

//         });

//     }

//     //Incoming message effect
//     useEffect(() => {
//         if (incomingMessage) {
//             if (incomingMessage.channelUrl === channelURL) {
//                 setChatMessages([...chatMessages, incomingMessage]);
//             }
//             setIncomingMessage();
//         }
//     }, [incomingMessage]);



//     return (
//         <View style={{ flex: 1 }}>
//             <View style={{ flex: 1 }}>
//                 {isLoading ?
//                     <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
//                         <ActivityIndicator color={colors.darkteal} size="large" />
//                     </View> : chatMessages && chatMessages?.length == 0 ?
//                         <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
//                             <TGText style={{ fontSize: 16, color: '#666' }}>No messages</TGText>
//                         </View> :
//                         <FlatList
//                             ref={messageRef}
//                             data={chatMessages}
//                             inverted
//                             contentContainerStyle={{ flexDirection: 'column-reverse' }}
//                             renderItem={({ item }) => <ChatMessage message={item} userId={user.id} />}
//                             keyExtractor={(item, index) => index.toString()}
//                         />
//                 }
//             </View>

//             <SafeAreaView>
//                 <View
//                     style={{
//                         backgroundColor: colors.lightgray,
//                         marginHorizontal: 15,
//                         paddingHorizontal: 15,
//                         borderRadius: 10,
//                         marginTop: 15,
//                         marginBottom: 15,
//                     }}>
//                     <TextInput
//                         value={message}
//                         onChangeText={setMessage}
//                         onSubmitEditing={sendMessage}
//                         multiline={true}
//                         textAlignVertical="top"
//                         returnKeyLabel="return"
//                         returnKeyType="send"
//                         blurOnSubmit={true}
//                         maxLength={600}
//                         placeholder="Send a message..."
//                         style={{
//                             fontFamily: 'Ubuntu-Regular',
//                             paddingRight: 40,
//                             paddingTop: 15,
//                             paddingBottom: 15,
//                         }}
//                     />
//                     <TouchableOpacity
//                         onPress={sendMessage}
//                         style={{ position: 'absolute', right: 15, top: 15 }}>
//                         <SendIcon height={23} width={23} />
//                     </TouchableOpacity>
//                 </View>
//             </SafeAreaView>
//         </View>
//     );
// }