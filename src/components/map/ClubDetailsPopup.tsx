import React, { useRef, useEffect, useContext } from 'react';
import RBSheet from 'react-native-raw-bottom-sheet';
import { StyleSheet, View } from 'react-native';

// Components imports
import MapClubDetailNew from './MapClubDetailNew';

// Utils and assets imports
import { BottomDrawerIcon } from '../../assets/svg';
import { Size } from '../../utils/responsiveUI';
import { MapClubDetail, MapClub, FilterState } from '../../interface';
import { GlobalContext } from '../../context/contextApi';

type RBSheetType = {
    open: () => void;
    close: () => void;
};

interface ClubDetailsPopupProps {
    club: MapClubDetail;
    setSelectedClub: (club: MapClubDetail | null) => void;
    setMoreDetailListView: (value: boolean) => void;
    moreDetailListView: boolean;
    setActiveView: (view: string) => void;
    isComeFromMap: boolean;
    recommendedClubs: MapClub[];
    selectedMarkerClub: MapClub | null;
    filter: FilterState;
}

const ClubDetailsPopup = ({
    club,
    setSelectedClub,
    setMoreDetailListView,
    moreDetailListView,
    setActiveView,
    isComeFromMap,
    recommendedClubs = [],
    filter,
}: ClubDetailsPopupProps) => {
    const refRBSheet = useRef<RBSheetType>(null);
    const { actions } = useContext(GlobalContext);
    useEffect(() => {
        if (club && Object.keys(club).length && refRBSheet.current) {
            refRBSheet.current.open();
        }
    }, [club]);

    useEffect(() => {
        if (recommendedClubs?.length && refRBSheet.current) {
            refRBSheet.current.close();
        }
    }, [recommendedClubs]);

    return (
        <>
            <RBSheet
                ref={refRBSheet}
                height={Size.SIZE_280}
                draggable
                dragOnContent
                // @ts-ignore
                closeOnDragDown={true}
                closeOnPressMask={true}
                closeOnPressBack={true}
                onClose={() => {
                    actions.setShouldDetailBottomSheetOpen(false);
                    setSelectedClub(null);
                    if (!isComeFromMap && moreDetailListView) {
                        setMoreDetailListView(true);
                    }
                }}
                customStyles={{
                    wrapper: {
                        backgroundColor: 'transparent',
                    },
                    container: {
                        borderTopLeftRadius: 15,
                        borderTopRightRadius: 15,
                        backgroundColor: 'transparent',
                    },
                    draggableIcon: {
                        display: 'none',
                    },
                }}>
                <View style={styles.dragIconContainer}>
                    <BottomDrawerIcon width={Size.SIZE_50} height={Size.SIZE_8} />
                </View>
                <MapClubDetailNew
                    club={club}
                    closed={() => refRBSheet.current?.close()}
                    filter={filter}
                    setSelectedClub={setSelectedClub}
                />
            </RBSheet>
        </>
    );
};

const styles = StyleSheet.create({
    callout: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'white',
        borderTopLeftRadius: 15,
        borderTopRightRadius: 15,
        shadowColor: 'black',
        shadowOffset: {
            width: 0,
            height: -2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 2,
        paddingBottom: 50,
    },
    noCallout: {
        height: 0,
        width: 0,
    },

    closeButtonContainer: {
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 12,
    },
    closeButton: {
        height: 4,
        width: 50,
        backgroundColor: '#e2e2e2',
        borderRadius: 3.5,
    },
    dragIconContainer: {
        alignItems: 'center',
        paddingTop: 10,
    },
});

export default ClubDetailsPopup;
