import React, { useContext, useEffect, useMemo, useState } from 'react';
import { StyleSheet, Text, View, Platform, Pressable } from 'react-native';

// Components, interfaces and context imports
import TGText from '../fields/TGText';
import TierCard from './TierCard';
import PlayedButton from './PlayedButton';
import FavoriteButton from '../buttons/FavoriteButton';
import { AuthContext } from '../../context/AuthContext';
import { Club, MapClub } from '../../interface';

// Assets, interface, Constants, Theme, Utils imports
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { ClubGolfIconTeal, HighlyRequestedIcon, MapLocationIconNew, UnexploredIcon } from '../../assets/svg';
import { colors } from '../../theme/theme';
import { FAVORITE_CLUB } from '../../graphql/mutations/clubs';
import { UNFAVORITE_CLUB } from '../../graphql/mutations/clubs';
import useClient from '../../hooks/useClient';
import { GlobalContext } from '../../context/contextApi';
import { UPDATE_USER } from '../../graphql/mutations/user';
import constants from '../../utils/constants/constants';

const CleverTap = require('clevertap-react-native');

interface MapClubCardProps {
    club: MapClub;
    handleClubPress: (club: MapClub) => void;
    isMember: boolean;
    setMapClubs: (clubs: MapClub[]) => void;
    myClubs: MapClub[];
    ownClub: number[];
}

function MapClubCard({ club, handleClubPress, setMapClubs, isMember, myClubs, ownClub }: MapClubCardProps) {
    const { id, addr, color, ct, name, tier, type, clubDemandType, isContact, isFriend, clubMembers } = useMemo(() => {
        const {
            geometry: {} = {},
            id,
            properties: { addr, color, ct, name, tier, clubDemandType, isContact, isFriend, clubMembers } = {},
            type,
        } = club;
        return { id, addr, color, ct, name, tier, type, clubDemandType, isContact, isFriend, clubMembers };
    }, [club]);
    const { user, refreshUser } = useContext(AuthContext);
    const { actions } = useContext(GlobalContext);
    const client = useClient();
    const [contactOnlyClub, setContactOnlyClub] = useState(false);

    useEffect(() => {
        handleSetContactClub();
    }, [isContact, isFriend]);

    /**
     * Determine if the club is a contact-only club for the current user.
     * This means the user is not a part of the club and either has contacts or friends in the club
     * or the club is a MY TG club and has no members.
     */
    const handleSetContactClub = () => {
        if (
            //@ts-ignore
            !ownClub.includes(id) && // if the user is not a part of this club
            (isContact || isFriend) && // and the user has contacts or friends
            ((!user?.visibleToPublic && color === 'teal_contact') || !clubMembers) // and there is no member in this club // and there is no member in this club
        ) {
            setContactOnlyClub(true);
        } else {
            setContactOnlyClub(false);
        }
    };

    /**
     * Handle favorite the club
     * @param active
     */
    async function handleFavoriteClub(active: boolean, clubId: number) {
        actions.setAppLoader(true);
        let res = await client.request(active ? FAVORITE_CLUB : UNFAVORITE_CLUB, {
            club_id: clubId,
            user_id: user.id,
        });
        if (res) {
            CleverTap.recordEvent(constants.CLEVERTAP.CLICK_FAVORITE_CLUB, {
                Selection: active ? 'Favorite' : 'UnFavorite',
                'Club name': club.properties.name,
                'Club Address': club.properties.addr,
                'User email': user.email,
                'User tier': user.tier, // e.g., Gold / Silver / Bronze / Platinum
                'User Membership': user.membership_plan?.name, // Active / Inactive
                'Locator colour': club.properties.color, // Blue / Green / Grey
                'Played club': user.playedClubs.includes(clubId),
            });
            await refreshUser();
            setMapClubs(myClubs.map((club) => (club.id === clubId ? { ...club, isFavorited: active } : club)));
        }
    }

    async function handlePlayedClub(active: boolean, clubId: number) {
        actions.setAppLoader(true);
        let res = await client.request(UPDATE_USER, {
            user_id: user.id,
            user: {
                playedClubs: active
                    ? [...user.playedClubs, clubId]
                    : user.playedClubs.filter((club_id: number) => club_id !== clubId),
            },
        });
        if (res) {
            CleverTap.recordEvent(constants.CLEVERTAP.CLICK_PLAYED_CLUB, {
                Selection: active ? 'Played' : 'UnPlayed',
                'Club name': club.properties.name,
                'Club Address': club.properties.addr,
                'User email': user.email,
                'User tier': user.tier, // e.g., Gold / Silver / Bronze / Platinum
                'User Membership': user.membership_plan?.name, // Active / Inactive
                'Locator colour': club.properties.color, // Blue / Green / Grey
                'Club Type':
                    user.favorite_clubs.filter(({ club_id }: { club_id: number }) => club_id === id).length === 1
                        ? 'Favorite'
                        : 'Unfavorite',
            });
            actions.setAppLoader(false);
            await refreshUser();
        }
    }

    return (
        <>
            {clubDemandType ? (
                <View style={styles.highlyRequestedContainer}>
                    {clubDemandType === 1 ? <HighlyRequestedIcon /> : <UnexploredIcon />}
                </View>
            ) : null}
            <Pressable
                onPress={() => {
                    handleClubPress(club);
                }}
                style={[
                    styles.detailsContainer,
                    {
                        width: clubDemandType ? '99%' : '100%',
                    },
                ]}>
                <View style={styles.tierCardContainer}>
                    <TierCard
                        clubType={ct ?? 0}
                        tier={tier ?? 0}
                        wrapperStyle={[styles.tierCard, { marginTop: clubDemandType ? Spacing.SCALE_10 : 0 }]}
                    />
                    <View style={styles.buttonContainer}>
                        {ct !== 2 && (
                            <PlayedButton
                                onPress={(isPlayed: boolean) => handlePlayedClub(isPlayed, id)}
                                isPlayed={user.playedClubs.includes(id)}
                            />
                        )}
                        {ct !== 2 && !contactOnlyClub && !ownClub.includes(id) && (
                            <View style={styles.favoriteButtonContainer}>
                                <FavoriteButton
                                    onPress={(active) => handleFavoriteClub(active, id)}
                                    size={Size.SIZE_14}
                                    isFavorited={
                                        user.favorite_clubs.filter(({ club_id }: { club_id: number }) => club_id === id)
                                            .length === 1
                                    }
                                />
                            </View>
                        )}
                    </View>
                </View>
                <View>
                    <View style={[styles.buttonContainer, styles.clubNameContainer]}>
                        <ClubGolfIconTeal />
                        <View style={{ maxWidth: '80%' }}>
                            <TGText style={styles.clubNameText}>{name}</TGText>
                        </View>
                    </View>
                    <View style={styles.addressDetailSection}>
                        <View style={styles.locationIconWrapper}>
                            <MapLocationIconNew />
                        </View>
                        <Text style={styles.addressText}>{addr}</Text>
                    </View>
                </View>
                <View>
                    <Text style={styles.clubDetailsText}>View Club Details</Text>
                </View>
            </Pressable>
        </>
    );
}

const styles = StyleSheet.create({
    detailsContainer: {
        flexDirection: 'column',
        padding: Spacing.SCALE_12,
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_10,
        marginVertical: Spacing.SCALE_4,
        borderWidth: 1,
        borderColor: colors.lightgray,
        alignSelf: 'flex-end',
    },
    topDetailSection: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
    },
    addressDetailSection: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: Spacing.SCALE_8,
        columnGap: Spacing.SCALE_10,
    },
    detailSection: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    see_offer: {
        paddingVertical: 2.5,
        paddingHorizontal: 10,
        borderColor: 'teal',
        borderWidth: 1,
        borderRadius: 5,
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 10,
        height: 25,
    },
    imageStyle: {
        height: Size.SIZE_14,
        width: Size.SIZE_13,
    },
    locationIconWrapper: {
        marginTop: Platform.OS === 'ios' ? Spacing.SCALE_1 : Spacing.SCALE_3,
    },
    tierCard: {
        borderRadius: Size.SIZE_6,
    },
    tierCardContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    buttonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_12,
    },
    favoriteButtonContainer: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_22,
        borderWidth: 1,
        borderColor: colors.lightgray,
        alignItems: 'center',
        justifyContent: 'center',
        padding: Spacing.SCALE_5,
    },
    clubNameContainer: {
        columnGap: Spacing.SCALE_8,
        marginTop: Spacing.SCALE_12,
    },
    clubNameText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    addressText: {
        color: colors.fadeBlack,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        width: '90%',
    },
    clubDetailsText: {
        color: colors.tealRgb,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_16,
    },
    highlyRequestedContainer: {
        position: 'absolute',
        zIndex: 2,
        alignItems: 'center',
        justifyContent: 'center',
        top: Spacing.SCALE_4,
    },
});

export default MapClubCard;
