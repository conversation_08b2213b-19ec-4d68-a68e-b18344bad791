import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';

// Utils, theme and assets
import { Size, Typography } from '../../utils/responsiveUI';
import { Spacing } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { CheckIcon } from '../../assets/svg';

// Component
const PlayedButton = ({ onPress, isPlayed }: { onPress?: (active: boolean) => void; isPlayed?: boolean }) => {
    return (
        <TouchableOpacity
            style={[
                styles.buttonContainer,
                isPlayed
                    ? { ...styles.tealButton, flexDirection: 'row-reverse', paddingLeft: Spacing.SCALE_10 }
                    : { ...styles.greyButton, flexDirection: 'row', paddingRight: Spacing.SCALE_10 },
            ]}
            onPress={() => {
                if (onPress) {
                    onPress(!isPlayed);
                }
            }}>
            <View
                style={[
                    styles.checkIconContainer,
                    isPlayed ? { backgroundColor: colors.tealRgb } : { backgroundColor: colors.darkGrey },
                ]}>
                <CheckIcon />
            </View>
            <Text style={styles.buttonText}>Played</Text>
        </TouchableOpacity>
    );
};

export default PlayedButton;

const styles = StyleSheet.create({
    buttonContainer: {
        borderRadius: Size.SIZE_34,
        padding: Spacing.SCALE_4,
        columnGap: Spacing.SCALE_4,
        alignItems: 'center',
    },
    tealButton: {
        backgroundColor: colors.opacityTeal,
    },
    greyButton: {
        backgroundColor: colors.lightGrey,
    },
    checkIconContainer: {
        width: Size.SIZE_16,
        height: Size.SIZE_16,
        borderRadius: Size.SIZE_21,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_11,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    positionRight: {
        flexDirection: 'row-reverse',
    },
    positionLeft: {
        flexDirection: 'row',
    },
});
