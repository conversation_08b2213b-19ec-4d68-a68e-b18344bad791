import { StyleSheet, View, FlatList, Animated } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { useAnimatedStyle, useSharedValue, withSpring, cancelAnimation } from 'react-native-reanimated';

// Components imports
import MapClubCard from './MapClubCard';
import CommonEmptyState from '../CommonEmptyState';
import { AuthContext } from '../../context/AuthContext';

// Assets, Constants, Theme, Utils imports
import { MapClub } from '../../interface';
import { Spacing } from '../../utils/responsiveUI';
import { MAP_CLUB_LIST_EMPTY_STATE } from '../../utils/constants/strings';
import { colors } from '../../theme/theme';

interface ListViewProps {
    listItems: MapClub[];
    onPressClub: (club: MapClub) => void;
    myClubs: MapClub[];
    setMapClubs: (clubs: MapClub[]) => void;
}

const ListView: React.FC<ListViewProps> = ({ listItems, onPressClub, myClubs, setMapClubs }) => {
    const animatedOpacity = useSharedValue(0);
    const { user } = useContext(AuthContext);
    const [ownClub, setOwnClub] = useState<number[]>([]);

    useEffect(() => {
        // Start animation immediately with spring animation for smoother effect
        animatedOpacity.value = withSpring(1, {
            damping: 15,
            stiffness: 100,
        });

        // Cleanup animation when component unmounts
        return () => {
            cancelAnimation(animatedOpacity);
        };
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
        return {
            opacity: animatedOpacity.value,
        };
    });

    useEffect(() => {
        let temporaryClub: number[] = [];
        user?.clubs?.map((club: any) => {
            temporaryClub.push(club?.club_id);
        });
        setOwnClub(temporaryClub);
    }, [user]);

    const renderItem = ({ item }: { item: MapClub }) => {
        return (
            <View style={styles.detailWrapper}>
                <MapClubCard
                    club={item}
                    isMember={myClubs.some((club) => club.id === item.id)}
                    handleClubPress={onPressClub}
                    setMapClubs={setMapClubs}
                    myClubs={myClubs}
                    ownClub={ownClub}
                />
            </View>
        );
    };

    return (
        <Animated.View style={[styles.listView, animatedStyle]}>
            <FlatList
                data={listItems}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
                contentContainerStyle={styles.listViewContent}
                ListEmptyComponent={
                    <CommonEmptyState text={MAP_CLUB_LIST_EMPTY_STATE} wrapperStyle={styles.emptyStateWrapper} />
                }
            />
        </Animated.View>
    );
};

const styles = StyleSheet.create({
    detailWrapper: {
        marginHorizontal: 15,
    },
    listView: {
        backgroundColor: colors.screenBG,
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    listViewContent: {
        paddingBottom: Spacing.SCALE_15,
    },
    emptyStateWrapper: {
        marginTop: Spacing.SCALE_100,
    },
});

export default ListView;
