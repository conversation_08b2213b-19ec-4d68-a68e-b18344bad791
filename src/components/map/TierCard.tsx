import React from 'react';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';

// Components imports
import TGText from '../fields/TGText';

// Assets, Theme, Utils imports
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';


interface TierCardProps {
    clubType: number;
    tier: number;
    wrapperStyle?: StyleProp<ViewStyle>;
}

const TierCard = ({ clubType, tier, wrapperStyle = {} }: TierCardProps) => {
    const tiers = [, 'Fern', 'Sage', 'Moss', , 'Olive'];
    return (
        <View style={[styles.tierNameWrapper, wrapperStyle]}>
            <TGText style={styles.textStyle}>{clubType == 2 ? 'Virtual' : tiers[tier]}</TGText>
        </View>
    );
};

export default TierCard;

const styles = StyleSheet.create({
    tierNameWrapper: {
        paddingHorizontal: Spacing.SCALE_8,
        paddingVertical: Spacing.SCALE_6,
        backgroundColor: 'rgba(9, 128, 137, 0.1)',
        borderRadius: Size.SIZE_6,
        width: Spacing.SCALE_50,
        height: Spacing.SCALE_20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    textStyle: {
        color: colors.darkteal,
        fontSize: Typography.FONT_SIZE_10,
        lineHeight: Size.SIZE_10,
        textTransform: 'uppercase',
        fontFamily: 'Ubuntu-Bold',
    },
});
