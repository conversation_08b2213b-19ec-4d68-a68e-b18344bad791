import { StyleSheet, Text, TouchableOpacity, ViewStyle } from 'react-native';
import React from 'react';

// Utils and theme imports
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';

const TripleDotToolTip = ({
    tooltipStyle = {},
    tooltipText = '',
    onPress = () => {},
}: {
    tooltipStyle: ViewStyle;
    tooltipText: string;
    onPress: () => void;
}) => {
    return (
        <TouchableOpacity style={[styles.tooltipContainer, tooltipStyle]} onPress={onPress}>
            <Text style={styles.tooltipText}>{tooltipText}</Text>
        </TouchableOpacity>
    );
};

export default TripleDotToolTip;

const styles = StyleSheet.create({
    tooltipContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: Spacing.SCALE_8,
        paddingHorizontal: Spacing.SCALE_14,
        backgroundColor: colors.white,
        borderRadius: Size.SIZE_8,
        borderWidth: 1.5,
        borderColor: colors.lightgray,
        shadowColor: colors.shadowColor,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        zIndex: 1000,
        minWidth: 120,
    },
    tooltipText: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        color: colors.lightBlack,
        textAlign: 'center',
    },
});
