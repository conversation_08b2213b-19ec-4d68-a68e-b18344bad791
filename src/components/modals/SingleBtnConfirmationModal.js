import React, { useState } from 'react';
import { Modal, Pressable, StyleSheet, TouchableWithoutFeedback, View, Text, KeyboardAvoidingView } from 'react-native';

import Cross from '../../assets/svg/cross.svg';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import Button from '../buttons/CustomButton';
import { TextInput } from 'react-native-gesture-handler';

const SingleBtnConfirmationModal = ({
    openPopupState,
    popupHeading = '',
    popupText = '',
    btnText = 'Yes',
    onPress = () => {},
    loading = false,
    showReason = false,
    reason,
    setReason,
}) => {
    const [openPopup, setOpenPopup] = openPopupState;
    const [reasonError, setReasonError] = useState('');
    return (
        <Modal
            animationIn="fadeInUp"
            transparent={true}
            isVisible={openPopup}
            customBackdrop={
                <TouchableWithoutFeedback style={{ flex: 1 }}>
                    <View style={{ flex: 1, backgroundColor: colors.transparentRgba }} />
                </TouchableWithoutFeedback>
            }
            backdropTransitionOutTiming={1}
            style={{ paddingHorizontal: 0, marginHorizontal: 0, paddingVertical: 0, marginVertical: 0 }}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}
                keyboardShouldPersistTaps="handled">
                <View style={{ flex: 1 }}>
                    <Pressable style={{ flex: 1, backgroundColor: colors.transparentRgba }} />
                    <View style={styles.popupWrapper}>
                        <Pressable
                            style={styles.crossIconWrapper}
                            onPress={() => {
                                setOpenPopup(false);
                                setReason('');
                            }}>
                            <Cross />
                        </Pressable>

                        <View style={styles.textWrapper}>
                            <Text style={styles.headerText}>{popupHeading}</Text>
                            <Text style={styles.textStyle}>{popupText}</Text>
                        </View>
                        {showReason && (
                            <View
                                style={{
                                    paddingHorizontal: Spacing.SCALE_16,
                                    alignItems: 'center',
                                    marginTop: Spacing.SCALE_10,
                                }}>
                                <Text style={styles.reasonText}>Add Reason</Text>
                                <TextInput
                                    style={[styles.input, reasonError ? styles.inputError : null]}
                                    placeholder="Enter here.."
                                    placeholderTextColor={colors.darkgray}
                                    multiline={true}
                                    value={reason}
                                    onChangeText={(text) => {
                                        setReason(text.trimStart());
                                        if (reasonError) setReasonError('');
                                    }}
                                />
                                {reasonError ? <Text style={styles.errorText}>{reasonError}</Text> : null}
                            </View>
                        )}
                        <View style={styles.btnWrapper}>
                            <Button
                                btnStyle={{
                                    width: '90%',
                                    height: Size.SIZE_50,
                                    backgroundColor: colors.orange,
                                    paddingVertical: 0,
                                    borderRadius: Size.SIZE_8,
                                }}
                                label={btnText}
                                labelStyle={{ fontSize: Typography.FONT_SIZE_16, fontFamily: 'Ubuntu-Medium' }}
                                onPress={() => {
                                    if (showReason) {
                                        if (reason.length === 0) {
                                            setReasonError('Reason is required');
                                        } else {
                                            onPress();
                                        }
                                    } else {
                                        onPress();
                                    }
                                }}
                                loading={loading}
                            />
                        </View>
                    </View>
                </View>
            </KeyboardAvoidingView>
        </Modal>
    );
};

export default SingleBtnConfirmationModal;

const styles = StyleSheet.create({
    popupWrapper: {
        width: '100%',
        position: 'absolute',
        bottom: 0,
        backgroundColor: 'white',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    crossIconWrapper: {
        paddingHorizontal: Spacing.SCALE_22,
        paddingTop: Spacing.SCALE_23,
        alignItems: 'flex-end',
    },
    btnWrapper: {
        alignItems: 'center',
        width: '100%',
        marginVertical: Size.SIZE_24,
    },
    headerText: {
        fontSize: Typography.FONT_SIZE_24,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_32,
    },
    textStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        lineHeight: Size.SIZE_21,
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_8,
        color: colors.systemMessageText,
    },
    textWrapper: {
        alignSelf: 'center',
        marginTop: Size.SIZE_20,
    },
    reasonText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        color: colors.systemMessageText,
        alignSelf: 'flex-start',
        marginBottom: Spacing.SCALE_6,
    },
    inputError: {
        borderColor: colors.orange,
    },
    errorText: {
        color: colors.orange,
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        marginBottom: Spacing.SCALE_16,
        alignSelf: 'flex-start',
    },
    input: {
        borderWidth: 1,
        borderColor: colors.borderGray,
        borderRadius: Size.SIZE_8,
        paddingHorizontal: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        width: Size.SIZE_340,
        height: Size.SIZE_80,
        textAlignVertical: 'top',
    },
    reasonText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        color: colors.systemMessageText,
        alignSelf: 'flex-start',
        marginBottom: Spacing.SCALE_6,
    },
    required: {
        color: colors.orange,
    },
});
