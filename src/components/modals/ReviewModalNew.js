import React, { useState, useContext, useEffect } from 'react';
import {
    View,
    Text,
    KeyboardAvoidingView,
    Platform,
    Dimensions,
    StyleSheet,
    TouchableOpacity,
    Modal,
    Pressable,
    Keyboard,
} from 'react-native';
import firebase from '@react-native-firebase/app';
import '@react-native-firebase/auth';
import '@react-native-firebase/storage';
import { useNavigation } from '@react-navigation/native';
import * as yup from 'yup';

import { AuthContext } from '../../context/AuthContext';
import Form, { FormContext } from '../../forms/FormContext';
import Spacer from '../layout/Spacer';
import useClient from '../../hooks/useClient';
import { MARK_COMPLETE_GAME_REQUEST_V2, UPDATE_GAME_PLAYERS } from '../../service/EndPoint';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { fetcher } from '../../service/fetcher';
import showToast from '../toast/CustomToast';
import { colors } from '../../theme/theme';
import LikeHandBlackIcon from '../../assets/svg/LikeHandBlackIcon.svg';
import LikeHandWhiteIcon from '../../assets/svg/LikeHandWhiteIcon.svg';
import Button from '../buttons/CustomButton';
import InputBox from '../fields/InputBox';
import PhotoUploadNew from '../fields/PhotoUploadNew';
import {
    GAME_REVIEW_PLACEHOLDER,
    REQUEST_COMPLETED_AND_MOVED_TO_HISTORY,
    SUCCESS,
} from '../../utils/constants/strings';

async function uploadImage({ fileName, uri, path }) {
    const storageRef = firebase.storage().ref().child(`game-review/${firebase.auth()?.currentUser?.uid}-${fileName}`);
    await storageRef.putFile(uri);
    return storageRef.getDownloadURL();
}

function ExperienceForm({ continueForm, setReview, review, closeModal, loading }) {
    const validationSchema =
        review?.satisfied === 'yes'
            ? {}
            : review?.satisfied === ''
            ? {
                  satisfied: yup.string().required('Please select if the game experience was satisfactory'),
              }
            : {
                  reason: yup.string().required('Reason is required'),
              };
    return (
        <Form
            onSubmit={continueForm}
            onUpdate={setReview}
            initialValues={{
                satisfied: '',
                reason: '',
            }}
            validationSchema={validationSchema}>
            <View behavior={Platform.OS === 'ios' ? 'padding' : null}>
                <Text style={styles.secondaryTextStyle}>Was the game experience satisfactory?</Text>

                <ReviewOptionComponent review={review} />
                <ReviewOptionBtn loading={loading} closeModal={closeModal} />
            </View>
        </Form>
    );
}

function ReviewForm({ setReview, saveReview, closeModal, errors, loading, navigation }) {
    return (
        <Form
            onSubmit={saveReview}
            onUpdate={setReview}
            customErrors={errors}
            initialValues={{
                review: '',
            }}>
            <View behavior={Platform.OS === 'ios' ? 'padding' : null} style={styles.container}>
                <Text
                    style={[
                        styles.secondaryTextStyle,
                        {
                            marginTop: Spacing.SCALE_1,
                            paddingHorizontal: Spacing.SCALE_10,
                            fontSize: Typography.FONT_SIZE_14,
                            lineHeight: Typography.FONT_SIZE_21,
                            marginBottom: Spacing.SCALE_16,
                        },
                    ]}>
                    Your review will be shared in TG Public Chat with other fellow golfers
                </Text>

                <GameReviewComponent />

                <View style={{ paddingHorizontal: 10 }}>
                    <PhotoUploadNew
                        name="photo"
                        screen={'GameReview'}
                        navigation={navigation}
                        imagePickerStyle={{
                            backgroundColor: 'rgba(203, 222, 223, 0.3)',
                            height: Size.SIZE_135,
                            width: '100%',
                            borderColor: 'rgba(51, 51, 51, 0.1)',
                            borderStyle: 'dashed',
                        }}
                    />
                </View>

                <ReviewOptionBtn
                    loading={loading}
                    closeModal={closeModal}
                    tealBtnName="Submit"
                    btnWrapper={{ marginBottom: Spacing.SCALE_30 }}
                />
            </View>
        </Form>
    );
}

/* Confirm number of players form */
const ConfirmPlayers = ({
    closeModal = () => {},
    loading = false,
    updatePlayers = () => {},
    setPlayers = () => {},
    players = 1,
}) => {
    return (
        <>
            <Form onSubmit={() => updatePlayers(players)}>
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    style={{ paddingHorizontal: Spacing.SCALE_10 }}>
                    <Text
                        style={[
                            styles.secondaryTextStyle,
                            {
                                fontSize: Typography.FONT_SIZE_14,
                                lineHeight: Typography.FONT_SIZE_21,
                                marginBottom: Spacing.SCALE_25,
                            },
                        ]}>
                        Please confirm the number of players again to proceed with the completion process.
                    </Text>

                    <View style={styles.numberBox}>
                        {[1, 2, 3].map((number) => (
                            <TouchableOpacity
                                key={number}
                                style={[styles.numberContainer, players === number && styles.selectedStyle]}
                                onPress={() => setPlayers(number)}>
                                <Text style={[styles.numberText, players === number && styles.selectedTextStyle]}>
                                    {number}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>

                    <ReviewOptionBtn loading={loading} closeModal={closeModal} />
                </KeyboardAvoidingView>
            </Form>
        </>
    );
};

export default function ReviewModalNew({ closeModal, isMyRequest, request }) {
    const [review, setReview] = useState({});
    const [experience, setExperience] = useState({});
    const [loading, setLoading] = useState(false);
    const [activeForm, setActiveForm] = useState('experience');
    const client = useClient();
    const { user } = useContext(AuthContext);
    const navigation = useNavigation();
    const [errors, setErrors] = useState({});
    const { width, height } = Dimensions.get('window');
    const [players, setPlayers] = useState(request?.number_of_players || 1);

    // Function call when mark game complete as a requester
    async function saveReview() {
        if (review && review?.review && review?.review.trim().length > 0) {
            fetcher({
                endpoint: UPDATE_GAME_PLAYERS,
                method: 'POST',
                body: {
                    userId: user?.id,
                    requestId: request.request_id,
                    players: players,
                },
            })
                .then(async (res) => {
                    if (res?.status) {
                        setLoading(false);
                        try {
                            setLoading(true);
                            const markCompleteRequestParams = {
                                userId: user?.id,
                                requestId: request?.request_id,
                                satisfied: experience.satisfied === 'yes',
                                reason: experience.reason,
                                review: review?.review,
                            };
                            if (review.photo) {
                                let photo = await uploadImage(review.photo);
                                markCompleteRequestParams.photo = photo;
                            }
                            fetcher({
                                endpoint: MARK_COMPLETE_GAME_REQUEST_V2,
                                method: 'POST',
                                body: markCompleteRequestParams,
                            }).then((res) => {
                                if (res?.status) {
                                    setLoading(false);
                                    closeModal(true);
                                    showToast({
                                        type: SUCCESS,
                                        header: '',
                                        message: REQUEST_COMPLETED_AND_MOVED_TO_HISTORY,
                                    });
                                } else {
                                    showToast({});
                                }
                            });
                        } catch (e) {
                            showToast({});
                        }
                    } else {
                        showToast({});
                    }
                })
                .catch(() => {
                    showToast({});
                });
        } else setErrors({ review: 'Please add your game Review' });
    }

    // Function used to mark complete the request
    const markCompleteRequest = () => {
        const markCompleteRequestParams = {
            userId: user?.id,
            requestId: request?.request_id,
            satisfied: experience.satisfied === 'yes',
            reason: experience.reason,
        };
        setLoading(true);
        fetcher({
            endpoint: MARK_COMPLETE_GAME_REQUEST_V2,
            method: 'POST',
            body: markCompleteRequestParams,
        })
            .then((res) => {
                if (res?.status) {
                    setLoading(false);
                    closeModal(true);
                    showToast({ type: SUCCESS, header: '', message: REQUEST_COMPLETED_AND_MOVED_TO_HISTORY });
                } else {
                    showToast({});
                }
            })
            .catch(() => {
                showToast({});
            });
    };

    async function continueForm() {
        // if condition  will call when we complete mark the game in Requested/Accepted tab and else will call when we complete mark the game in Received/Accepted tab
        if (isMyRequest) {
            if (!request?.host_completed) {
                setActiveForm('confirm-player');
            } else {
                setActiveForm('review');
            }
        } else {
            if (!request?.requestor_completed) {
                setActiveForm('confirm-player');
            } else {
                markCompleteRequest();
            }
        }
    }

    //API to update number of players
    const updatePlayers = (players) => {
        if (isMyRequest) {
            setActiveForm('review');
        } else {
            setLoading(true);
            fetcher({
                endpoint: UPDATE_GAME_PLAYERS,
                method: 'POST',
                body: {
                    userId: user?.id,
                    requestId: request.request_id,
                    players: players,
                },
            })
                .then((res) => {
                    if (res?.status) {
                        setLoading(false);
                        markCompleteRequest();
                    } else {
                        showToast({});
                    }
                })
                .catch(() => {
                    showToast({});
                });
        }
    };

    return (
        <Modal
            animationIn="fadeInUp"
            transparent={true}
            backdropTransitionOutTiming={1}
            isVisible={true}
            style={styles.modal}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : null}
                style={{ flex: 1 }}
                keyboardShouldPersistTaps="handled"
                enabled={true}>
                <View style={styles.modalBodyWrapper}>
                    <Pressable style={styles.popupWrapper} onPress={Keyboard.dismiss}>
                        <View style={styles.modalBackgroundStyle}>
                            <Text style={styles.primaryTextStyle}>
                                {activeForm === 'experience'
                                    ? 'Rate Your Experience'
                                    : activeForm === 'confirm-player'
                                    ? 'Confirm No. Of Players'
                                    : 'Add Game Review'}
                            </Text>

                            {activeForm === 'experience' && (
                                <ExperienceForm
                                    continueForm={continueForm}
                                    review={experience}
                                    closeModal={closeModal}
                                    setReview={setExperience}
                                    loading={loading}
                                />
                            )}

                            {activeForm === 'review' && isMyRequest && (
                                <ReviewForm
                                    review={review}
                                    closeModal={closeModal}
                                    setReview={setReview}
                                    saveReview={saveReview}
                                    loading={loading}
                                    errors={errors}
                                    navigation={navigation}
                                />
                            )}
                            {activeForm === 'confirm-player' && (
                                <ConfirmPlayers
                                    closeModal={closeModal}
                                    loading={loading}
                                    updatePlayers={updatePlayers}
                                    request={request}
                                    setPlayers={setPlayers}
                                    players={players}
                                />
                            )}
                        </View>
                    </Pressable>
                </View>
            </KeyboardAvoidingView>
        </Modal>
    );
}

/**
 *
 * @returns
 */
const ReviewOptionComponent = ({ review = {} }) => {
    const { form, updateForm, errors } = useContext(FormContext);
    const [reason, setReason] = useState('');

    useEffect(() => {
        updateForm('reason', reason);
    }, [reason]);

    return (
        <>
            <View style={styles.iconBox}>
                <View style={styles.secondBox}>
                    <TouchableOpacity
                        style={[
                            styles.iconContainer,
                            form.satisfied === 'yes' ? { backgroundColor: colors.darkteal } : {},
                        ]}
                        onPress={() => {
                            updateForm('satisfied', 'yes');
                        }}>
                        {form.satisfied === 'yes' ? <LikeHandWhiteIcon /> : <LikeHandBlackIcon />}
                    </TouchableOpacity>
                    <Text style={styles.iconTextStyle}>Yes</Text>
                </View>
                <View style={styles.secondBox}>
                    <TouchableOpacity
                        style={[
                            styles.iconContainer,
                            form.satisfied === 'no' ? { backgroundColor: colors.orange } : {},
                        ]}
                        onPress={() => {
                            updateForm('satisfied', 'no');
                        }}>
                        {form.satisfied === 'no' ? (
                            <LikeHandWhiteIcon style={{ transform: [{ rotate: '180deg' }] }} />
                        ) : (
                            <LikeHandBlackIcon style={{ transform: [{ rotate: '180deg' }] }} />
                        )}
                    </TouchableOpacity>
                    <Text style={styles.iconTextStyle}>No</Text>
                </View>
            </View>
            {errors?.satisfied && <Text style={styles.errorTextStyle}>{errors?.satisfied}</Text>}

            {review?.satisfied === 'no' && (
                <>
                    <Spacer />
                    <Text
                        style={[
                            styles.secondaryTextStyle,
                            {
                                fontSize: Typography.FONT_SIZE_14,
                                marginVertical: 1,
                                paddingHorizontal: Spacing.SCALE_10,
                            },
                        ]}>
                        Please let us know why you are not satisfied with the game
                    </Text>

                    <InputBox
                        title="Add Reason"
                        name="reason"
                        placeholder="Please elaborate reason for the dissatisfaction"
                        valueState={[reason, setReason]}
                        containerStyle={styles.inputContainerStyle}
                        inputStyle={[styles.inputStyle, form?.errors?.reason && { borderColor: colors.errorColor }]}
                        isMultiLine
                        titleStyle={{
                            fontSize: Typography.FONT_SIZE_12,
                            fontWeight: '400',
                            fontFamily: 'Ubuntu-Regular',
                            color: colors.fadeBlack,
                        }}
                    />
                    {errors?.reason && (
                        <Text
                            style={[
                                styles.errorTextStyle,
                                { paddingHorizontal: Spacing.SCALE_10, textAlign: 'center' },
                            ]}>
                            {errors.reason}
                        </Text>
                    )}
                </>
            )}
        </>
    );
};

/**
 *
 * @param {*} param0
 * @returns
 */
const ReviewOptionBtn = ({ loading = false, closeModal = () => {}, tealBtnName = 'Continue', btnWrapper = '' }) => {
    const form = useContext(FormContext);

    return (
        <>
            <View style={[styles.btnWrapper, btnWrapper]}>
                <Button
                    btnStyle={{ backgroundColor: colors.lightGrey, ...styles.btnCustomStyle }}
                    label="Cancel"
                    labelStyle={{ paddingVertical: 0, fontSize: Typography.FONT_SIZE_16, color: colors.lightBlack }}
                    onPress={() => {
                        closeModal();
                    }}
                />

                <Button
                    btnStyle={{ backgroundColor: colors.darkteal, ...styles.btnCustomStyle }}
                    label={tealBtnName}
                    labelStyle={{ paddingVertical: 0, fontSize: Typography.FONT_SIZE_16 }}
                    onPress={(v) => {
                        if (form) {
                            if (!(loading || form?.loading)) {
                                form.submitForm(v);
                            }
                        }
                    }}
                    loading={loading}
                />
            </View>
        </>
    );
};

const GameReviewComponent = () => {
    const [review, setReview] = useState('');
    const form = useContext(FormContext);

    useEffect(() => {
        form.updateForm('review', review);
    }, [review]);

    return (
        <>
            <InputBox
                title="Add your Review"
                name="review"
                placeholder={GAME_REVIEW_PLACEHOLDER}
                valueState={[review, setReview]}
                containerStyle={styles.inputContainerStyle}
                inputStyle={styles.inputStyle}
                isMultiLine
                titleStyle={{
                    fontSize: Typography.FONT_SIZE_12,
                    fontWeight: '400',
                    fontFamily: 'Ubuntu-Regular',
                    color: colors.fadeBlack,
                }}
            />
            {form?.errors?.review && <Text style={styles.errorTextStyle}>{form?.errors?.review}</Text>}
        </>
    );
};

const styles = StyleSheet.create({
    modal: {
        paddingHorizontal: 0,
        marginHorizontal: 0,
        paddingVertical: 0,
        marginVertical: 0,
    },
    modalBodyWrapper: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    popupWrapper: {
        width: '100%',
        position: 'absolute',
        bottom: 0,
    },
    modalBackgroundStyle: {
        flex: 1,
        backgroundColor: colors.white,
        padding: Spacing.SIZE_74,
        paddingTop: Spacing.SCALE_30,
        borderTopLeftRadius: Size.SIZE_8,
        borderTopRightRadius: Size.SIZE_8,
    },
    container: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    primaryTextStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '500',
        lineHeight: Typography.FONT_SIZE_32,
        color: colors.lightBlack,
        textAlign: 'center',
        marginBottom: Spacing.SCALE_8,
    },
    secondaryTextStyle: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        lineHeight: Typography.FONT_SIZE_24,
        color: colors.fadeBlack,
        textAlign: 'center',
        marginBottom: Spacing.SCALE_10,
    },
    iconBox: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        marginTop: Spacing.SCALE_10,
    },
    secondBox: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    iconTextStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        lineHeight: Typography.FONT_SIZE_30,
        color: colors.lightBlack,
        marginTop: Spacing.SCALE_5,
    },
    iconContainer: {
        height: Size.SIZE_74,
        width: Size.SIZE_74,
        backgroundColor: colors.lightgray,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 50,
    },
    btnWrapper: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'center',
        width: '100%',
        marginTop: Size.SIZE_20,
        marginBottom: Size.SIZE_20,
        columnGap: Spacing.SCALE_14,
    },
    btnCustomStyle: {
        width: '42%',
        height: Size.SIZE_45,
        paddingVertical: 0,
        borderRadius: 8,
    },
    numberContainer: {
        width: Size.SIZE_94,
        height: Size.SIZE_42,
        borderWidth: 2,
        borderColor: colors.lightgray,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 4,
    },
    numberText: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        textAlign: 'center',
        lineHeight: Typography.FONT_SIZE_18,
        color: colors.lightBlack,
    },
    selectedStyle: {
        backgroundColor: 'rgba(9, 128, 137, 0.1)',
        borderColor: colors.darkteal,
    },
    selectedTextStyle: {
        color: colors.darkteal,
    },
    numberBox: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: Spacing.SCALE_10,
        paddingHorizontal: Spacing.SCALE_10,
    },
    inputContainerStyle: {
        paddingHorizontal: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_5,
    },
    inputStyle: {
        borderWidth: 1,
        paddingHorizontal: Spacing.SCALE_10,
        marginTop: Spacing.SCALE_6,
        height: Size.SIZE_100,
        borderRadius: 4,
        fontWeight: '400',
        paddingTop: Spacing.SCALE_5,
        textAlignVertical: 'top',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Typography.FONT_SIZE_21,
        color: colors.lightBlack,
        paddingVertical: 0,
    },
    errorTextStyle: {
        fontFamily: 'Ubuntu-Light',
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        textAlign: 'center',
        color: colors.errorColor,
    },
});
