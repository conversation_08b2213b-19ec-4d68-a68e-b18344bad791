import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, Text, TouchableWithoutFeedback, Modal, StyleSheet } from 'react-native';
import { Calendar } from 'react-native-calendars';
import moment from 'moment';

//Components imports
import TGText from '../fields/TGText';

//Theme, utils, assets imports
import { colors } from '../../theme/theme';

interface DateSelection {
    dateString: string;
    day: number;
    month: number;
    year: number;
    timestamp: number;
}

interface CommonDateRangeModalProps {
    visible: boolean;
    onClose: () => void;
    onSave: (dates: { startDate?: Date; endDate?: Date }) => void;
    initialStartDate?: Date;
    initialEndDate?: Date;
    minDate?: Date;
    maxDate?: Date;
    disabledDates?: string[];
    allowRangeSelection?: boolean;
    title?: string;
    notes?: string;
    monthNote?: string;
    modalStyle?: any;
}

export default function CommonDateRangeModal({
    visible,
    onClose,
    onSave,
    initialStartDate,
    initialEndDate,
    minDate,
    maxDate,
    disabledDates = [],
    allowRangeSelection = false,
    title = 'Select Date',
    notes,
    monthNote,
    modalStyle = {},
}: CommonDateRangeModalProps) {
    const [startDate, setStartDate] = useState<moment.Moment | null>(
        initialStartDate ? moment(initialStartDate) : null
    );
    const [endDate, setEndDate] = useState<moment.Moment | null>(
        initialEndDate ? moment(initialEndDate) : null
    );

    useEffect(() => {
        if (initialStartDate) {
            setStartDate(moment.utc(initialStartDate));
        }
        if (initialEndDate) {
            setEndDate(moment.utc(initialEndDate));
        }
    }, [initialStartDate, initialEndDate]);

    function getDates() {
        let markedDates: { [key: string]: any } = {};

        // Add disabled dates
        if (disabledDates.length > 0) {
            disabledDates.forEach(date => {
                markedDates[date] = {
                    disabled: true,
                    disableTouchEvent: true,
                };
            });
        }

        if (!startDate) {
            return markedDates;
        }

        if (!allowRangeSelection || !endDate) {
            markedDates[startDate.format('YYYY-MM-DD')] = {
                selected: true,
                startingDay: true,
                endingDay: true,
                color: colors.darkteal,
                textColor: 'white',
            };
            return markedDates;
        }

        // Handle range selection
        const rangeStart = moment.min(startDate, endDate);
        const rangeEnd = moment.max(startDate, endDate);
        const dayCount = rangeEnd.diff(rangeStart, 'days');

        for (let i = 0; i <= dayCount; i++) {
            const date = rangeStart.clone().add(i, 'days').format('YYYY-MM-DD');
            markedDates[date] = {
                selected: true,
                startingDay: i === 0,
                endingDay: i === dayCount,
                color: colors.darkteal,
                textColor: 'white',
            };
        }

        return markedDates;
    }

    function handleDatePress(day: DateSelection) {
        const selectedDate = moment.utc(day.dateString);

        if (!allowRangeSelection) {
            setStartDate(selectedDate);
            setEndDate(null);
            return;
        }

        if (!startDate || endDate) {
            setStartDate(selectedDate);
            setEndDate(null);
        } else {
            if (selectedDate.isBefore(startDate)) {
                setEndDate(startDate);
                setStartDate(selectedDate);
            } else {
                setEndDate(selectedDate);
            }
        }
    }

    function handleSave() {
        onSave({
            startDate: startDate?.toDate(),
            endDate: endDate?.toDate(),
        });
        onClose();
    }

    return (
        <Modal visible={visible} transparent={true}>
            <View style={styles.modalOverlay}>
                <TouchableWithoutFeedback onPress={onClose}>
                    <View style={styles.backdrop} />
                </TouchableWithoutFeedback>
                <View style={[styles.modalContent, modalStyle]}>
                    <Text style={styles.title}>{title}</Text>
                    <Calendar
                        current={minDate && moment(minDate).isAfter(moment()) ? minDate : new Date()}
                        minDate={minDate && moment(minDate).isAfter(moment()) ? minDate : new Date()}
                        maxDate={maxDate || moment().add(1, 'year').toDate()}
                        onDayPress={handleDatePress}
                        markedDates={getDates()}
                        markingType={allowRangeSelection ? 'period' : 'custom'}
                        hideExtraDays={true}
                        disableMonthChange={true}
                    />

                    {notes && (
                        <TGText style={styles.notes}>{notes}</TGText>
                    )}
                    {monthNote && (
                        <TGText style={styles.monthNote}>{monthNote}</TGText>
                    )}

                    <View style={styles.buttonContainer}>
                        <TouchableOpacity onPress={onClose} style={styles.cancelButton}>
                            <Text style={styles.cancelButtonText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
                            <Text style={styles.saveButtonText}>Save</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    modalOverlay: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        paddingHorizontal: 30,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 100,
    },
    backdrop: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 10,
        width: '100%',
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: colors.lightgray,
    },
    title: {
        fontFamily: 'RobotoSlab-Regular',
        padding: 30,
        paddingBottom: 15,
        fontSize: 20,
        textAlign: 'center',
    },
    notes: {
        fontFamily: 'Ubuntu-Light',
        fontSize: 12,
        marginTop: 10,
        marginHorizontal: 20,
        color: 'red',
        textAlign: 'center',
    },
    monthNote: {
        fontFamily: 'Ubuntu-Light',
        fontSize: 12,
        marginTop: 10,
        marginHorizontal: 20,
        color: colors.darkteal,
        textAlign: 'center',
    },
    buttonContainer: {
        flexDirection: 'row',
        padding: 20,
        justifyContent: 'space-between',
    },
    cancelButton: {
        paddingHorizontal: 30,
        paddingVertical: 10,
        borderRadius: 10,
    },
    cancelButtonText: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 16,
        color: colors.darkgray,
        textAlign: 'center',
    },
    saveButton: {
        backgroundColor: colors.darkteal,
        paddingHorizontal: 30,
        paddingVertical: 10,
        borderRadius: 10,
    },
    saveButtonText: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 16,
        color: 'white',
    },
});
