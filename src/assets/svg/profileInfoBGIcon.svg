<svg width="375" height="97" viewBox="0 0 375 97" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_47617_64967)" filter="url(#filter0_i_47617_64967)">
<rect width="375" height="97" fill="white" style="fill:white;fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M277.082 -43.963C304.681 -47.3172 325.633 -20.5859 344.008 0.269737C360.898 19.4398 374.592 41.6238 374.99 67.1645C375.394 93.081 363.974 117.373 346.126 136.176C327.755 155.531 303.371 174.929 277.082 170.311C252.13 165.928 245.204 134.738 228.678 115.545C214.427 98.9924 189.143 88.9854 188.274 67.1645C187.395 45.0806 210.866 32.0422 224.666 14.7736C241.696 -6.53627 249.995 -40.671 277.082 -43.963Z" fill="#ECF4D6" style="fill:#ECF4D6;fill:color(display-p3 0.9255 0.9569 0.8392);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M98.9318 -96.423C67.4528 -100.245 43.5558 -69.7878 22.5984 -46.0254C3.33469 -24.1835 -12.2841 1.09224 -12.7382 30.1926C-13.1989 59.7212 -0.174606 87.3988 20.1825 108.823C41.1364 130.875 68.9474 152.977 98.9318 147.715C127.391 142.721 135.291 107.185 154.139 85.3162C170.394 66.4566 199.232 55.0549 200.223 30.1926C201.225 5.0308 174.455 -9.82478 158.715 -29.5001C139.291 -53.78 129.826 -92.6722 98.9318 -96.423Z" fill="#9AD0C2" style="fill:#9AD0C2;fill:color(display-p3 0.6039 0.8157 0.7608);fill-opacity:1;"/>
<foreignObject x="-127.5" y="-127.5" width="630" height="352.5"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(63.75px);clip-path:url(#bgblur_1_47617_64967_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="127.5" width="375" height="97.5" fill="white" fill-opacity="0.01" style="fill:white;fill-opacity:0.01;"/>
</g>
<defs>
<filter id="filter0_i_47617_64967" x="0" y="-0.75" width="375" height="97.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.75"/>
<feGaussianBlur stdDeviation="12.75"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_47617_64967"/>
</filter>
<clipPath id="bgblur_1_47617_64967_clip_path" transform="translate(127.5 127.5)"><rect width="375" height="97.5"/>
</clipPath><clipPath id="clip0_47617_64967">
<rect width="375" height="97" fill="white" style="fill:white;fill-opacity:1;"/>
</clipPath>
</defs>
</svg>
