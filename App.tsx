import 'localstorage-polyfill';
import React, { useEffect, useState, useCallback, useRef, useMemo, useLayoutEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { ApolloProvider } from 'react-apollo';
import NetInfo from '@react-native-community/netinfo';
import { ApolloProvider as ApolloHookProvider } from '@apollo/client';
import Mapbox from '@rnmapbox/maps';
import { Platform, NativeEventEmitter, NativeModules } from 'react-native';
import Toast, { ErrorToast, SuccessToast, ToastProps } from 'react-native-toast-message';
import RNFS from 'react-native-fs';
const CleverTap = require('clevertap-react-native');

import RenderNavigation from './src/navigation';
import useAuth from './src/hooks/useAuth';
import useApolloClient from './src/hooks/useApolloClient';
import { mapBoxAccessToken } from './src/screens/my-TG-Stream-Chat/client';
import { Size, Typography } from './src/utils/responsiveUI';
import { colors } from './src/theme/theme';
import ErrorBoundary from './src/components/ErrorBoundry/ErrorBoundry';
import constants from './src/utils/constants/constants';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { User } from './src/interface';

const App = React.memo(() => {
    const {
        user,
        token,
        initializing,
        refreshUser,
        refreshToken,
        needsAnnualConfirmation,
        deferAnnualConfirmation,
        setDeferAnnualConfirmation,
        streamClient,
        duesState,
        maintenance,
        clubValidationRequired,
    } = useAuth() as unknown as { user: User | null; [key: string]: any };

    const { SharedDataModule } = NativeModules;

    const [isInternet, setInternet] = useState<boolean>(true);
    const [shareSheetImageUrl, setShareSheetImageUrl] = useState<string[]>([]);

    const navigationRef = useRef(null);

    const client = useApolloClient(token);

    useLayoutEffect(() => {
        setShareSheetImageUrl([]);
    }, []);

    useEffect(() => {
        if (Platform.OS === 'android') {
            const sharedDataEmitter = new NativeEventEmitter(SharedDataModule);
            const subscription = sharedDataEmitter.addListener('SharedData', (event) => {
                convertContentUrisToFilePaths(event?.uris).then((localPath) => {
                    handleImage(localPath);
                });
            });

            return () => {
                subscription.remove(); // Cleanup listener on component unmount
            };
        }
    }, []);

    useEffect(() => {
        const fetchSharedData = async () => {
            try {
                const sharedData = await SharedDataModule.getSharedData();
                if (sharedData) {
                    convertContentUrisToFilePaths(sharedData?.uris).then((localPath) => {
                        handleImage(localPath);
                    });
                }
            } catch (error) {
                console.error('Error fetching shared data:', error);
            }
        };

        if (Platform.OS === 'android') fetchSharedData();
    }, []);

    const convertContentUrisToFilePaths = async (contentUris: string[]) => {
        try {
            const convertedPaths = await Promise.all(
                contentUris.map(async (contentUri, index) => {
                    // Generate a unique filename using timestamp
                    const uniqueId = new Date().getTime() + Math.floor(Math.random() * 1000);
                    const destPath = `${RNFS.TemporaryDirectoryPath}/shared_image_${uniqueId}_${index}.jpg`;
    
                    await RNFS.copyFile(contentUri, destPath);
                    return destPath;
                })
            );
    
            return convertedPaths;
        } catch (error) {
            console.error('Error converting content URIs:', error);
            return [];
        }
    };

    // Memoized Toast configuration
    const toastConfig = useMemo(
        () => ({
            error: (props: ToastProps) => (
                <ErrorToast
                    {...props}
                    text1Style={{
                        fontSize: Typography.FONT_SIZE_15,
                        fontFamily: 'Ubuntu-Medium',
                    }}
                    text2Style={{
                        fontSize: Typography.FONT_SIZE_12,
                        fontFamily: 'Ubuntu-Medium',
                    }}
                    text2NumberOfLines={10}
                    style={{ borderLeftColor: 'red', height: Size.SIZE_70 }}
                />
            ),
            success: (props: ToastProps) => (
                <SuccessToast
                    {...props}
                    text1Style={{
                        fontSize: Typography.FONT_SIZE_15,
                        fontFamily: 'Ubuntu-Medium',
                    }}
                    text2Style={{
                        fontSize: Typography.FONT_SIZE_12,
                        fontFamily: 'Ubuntu-Medium',
                    }}
                    text2NumberOfLines={10}
                    style={{ borderLeftColor: colors.tealRgb, height: Size.SIZE_70 }}
                />
            ),
        }),
        [],
    );

    // Handle shared image
    const handleImage = useCallback((data: string[] = []) => {
        try {
            setShareSheetImageUrl(data);
        } catch (e) {
            console.error('Error in handleImage:', e);
        }
    }, []);

    // NetInfo listener and Mapbox configuration
    useEffect(() => {
        const unsubscribe = NetInfo.addEventListener((state) => {
            if (state.isConnected !== isInternet) {
                setInternet(state.isConnected ?? true);
            }
        });

        Mapbox.setAccessToken(mapBoxAccessToken);
        Mapbox.setTelemetryEnabled(false);

        return () => unsubscribe();
    }, [isInternet]);

    // CleverTap setup
    useEffect(() => {
        if (user?.id) {
            CleverTap.getCleverTapID((err: Error | null, res: string) => {
                if (err) console.error('CleverTapID Error:', err);
                else console.log('CleverTapID:', res);
            });

            CleverTap.setDebugLevel(3);
            CleverTap.onUserLogin({
                [constants.CLEVERTAP.PROFILE.IDENTITY]: user?.id,
                [constants.CLEVERTAP.PROFILE.NAME]: `${user?.first_name} ${user?.last_name}`,
                [constants.CLEVERTAP.PROFILE.EMAIL]: user?.email,
            });
        }
    }, [user?.id]);

    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <ErrorBoundary>
                <NavigationContainer ref={navigationRef}>
                    {client && (
                        <ApolloProvider client={client}>
                            <ApolloHookProvider client={client}>
                                <RenderNavigation
                                    navigationRef={navigationRef}
                                    duesState1={duesState}
                                    shareSheetImageUrl={shareSheetImageUrl}
                                    user={user}
                                    token={token}
                                    initializing={initializing}
                                    refreshUser={refreshUser}
                                    refreshToken={refreshToken}
                                    needsAnnualConfirmation={needsAnnualConfirmation}
                                    deferAnnualConfirmation={deferAnnualConfirmation}
                                    setDeferAnnualConfirmation={setDeferAnnualConfirmation}
                                    streamClient={streamClient}
                                    maintenance={maintenance}
                                    clubValidationRequired={clubValidationRequired}
                                />
                                <Toast config={toastConfig} />
                            </ApolloHookProvider>
                        </ApolloProvider>
                    )}
                </NavigationContainer>
            </ErrorBoundary>
        </GestureHandlerRootView>
    );
});

export default App;
