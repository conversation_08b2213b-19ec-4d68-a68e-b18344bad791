package com.thousandgreens

import android.content.Intent
import android.net.Uri
import android.widget.Toast
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.facebook.react.modules.core.RCTNativeAppEventEmitter

class SharedDataModule(private val reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    
    companion object {
        private var sharedData: List<String>? = null
        private var reactContextRef: ReactApplicationContext? = null
        
        fun handleIntent(intent: Intent?) {
            if (intent == null) {
                return
            }

            val action = intent.action
            val type = intent.type

            when {
                // Handle single image
                Intent.ACTION_SEND == action && type?.startsWith("image/") == true -> {
                    showToast("Processing single image share")
                    val imageUri: Uri? = intent.getParcelableExtra(Intent.EXTRA_STREAM)
                    handleImages(listOfNotNull(imageUri))
                }
                
                // Handle multiple images
                Intent.ACTION_SEND_MULTIPLE == action && type?.startsWith("image/") == true -> {
                    showToast("Processing multiple images share")
                    val imageUris: ArrayList<Uri>? = intent.getParcelableArrayListExtra(Intent.EXTRA_STREAM)
                    handleImages(imageUris ?: emptyList())
                }
            }
        }

        private fun handleImages(uris: List<Uri>) {
            if (uris.isEmpty()) return

            val uriStrings = uris.map { it.toString() }
            sharedData = uriStrings

            reactContextRef?.let { context ->
                try {
                    context.runOnUiQueueThread {
                        val map = Arguments.createMap().apply {
                            putString("type", "image")
                            putArray("uris", Arguments.makeNativeArray(uriStrings))
                        }
                        
                        // Try multiple ways to emit the event
                        try {
                            // Method 1: DeviceEventManagerModule
                            context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                                ?.emit("SharedData", map)
                            
                            // Method 2: RCTNativeAppEventEmitter
                            context.getJSModule(RCTNativeAppEventEmitter::class.java)
                                ?.emit("SharedData", map)
                                
                        } catch (e: Exception) {
                        }
                    }
                } catch (e: Exception) {
                    showToast("Error in handleImages: ${e.message}")
                }
            } ?: showToast("ReactContext is null")
        }

        private fun showToast(message: String) {
            reactContextRef?.runOnUiQueueThread {
                Toast.makeText(reactContextRef?.baseContext, message, Toast.LENGTH_SHORT).show()
                try {
                    val context = reactContextRef
                    if (context != null && context.hasActiveReactInstance()) {
                        context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                            ?.emit("LogMessage", message)
                    }
                } catch (e: Exception) {
                    // If event emitter fails, at least we see the toast
                }
            }
        }
    }

    init {
        reactContextRef = reactContext
    }

    override fun getName(): String = "SharedDataModule"

    override fun getConstants(): Map<String, Any> {
        return hashMapOf(
            "DEFAULT_EVENT_NAME" to "SharedData",
            "LOG_EVENT_NAME" to "LogMessage"
        )
    }

    @ReactMethod
    fun addListener(eventName: String) {
    }

    @ReactMethod
    fun removeListeners(count: Int) {
    }

    @ReactMethod
    fun getSharedData(promise: Promise) {
        if (sharedData != null) {
            val map = Arguments.createMap().apply {
                putString("type", "image")
                putArray("uris", Arguments.makeNativeArray(sharedData))
            }
            promise.resolve(map)
            // Clear the data after sending
            sharedData = null
        } else {
            promise.resolve(null)
        }
    }

    private fun sendEvent(eventName: String, params: WritableMap?) {
        try {
            reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit(eventName, params)
        } catch (e: Exception) {
        }
    }

    private fun hasValidContext(): Boolean {
        return reactContext.hasActiveReactInstance()
    }
}
